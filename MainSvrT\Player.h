/*#include "Tools.h"

void __fastcall MyGameStart(void* Player, void* edx) {
	IChar IPlayer(Player);
	CPlayer::GameStart(Player);

		

		if (IPlayer.IsOnline() && !IPlayer.IsBuff(155) && IPlayer.GetLevel() == 1)
		{
			if ((std::string)Welcome_msg_first == "true" && IPlayer.GetLevel() == 1)
			{
				char FIRST[1024];
				sprintf_s(FIRST, "Welcome to HighGamer's Server source! Buy it at Discord: HighGamer.com#8990");
				CPlayer::WriteAll(0xFF, "dsd", 247, FIRST, 1);
				sprintf_s(FIRST, firstmsg, IPlayer.GetName());
				CPlayer::WriteAll(0xFF, "dsd", 247, FIRST, 4);
			}

			for (unsigned int i = 0; i < GameStartC_Config.size(); i++) {
				if (IPlayer.IsOnline() && IPlayer.GetClass() == GameStartC_Config[i].Class)
				{
					(*(void(__cdecl**)(int, signed int, signed int, signed int))(*(DWORD*)(int)IPlayer.GetOffset() + 88))((int)IPlayer.GetOffset(), 26, 1, GameStartC_Config[i].level - 1);//Level
					(*(void(__cdecl**)(int, signed int, signed int, int))(*(DWORD*)(int)IPlayer.GetOffset() + 88))((int)IPlayer.GetOffset(), 23, 1, 5 * *(DWORD*)((int)IPlayer.GetOffset() + 60));//Stats Point
					(*(void(__cdecl**)(DWORD, signed int, DWORD, DWORD))(*(DWORD*)(int)IPlayer.GetOffset() + 88))((int)IPlayer.GetOffset(), 24, 1, 2 * *(DWORD*)((int)IPlayer.GetOffset() + 60));//Skill Point
					IPlayer.Teleport(0, GameStartC_Config[i].X, GameStartC_Config[i].Y);
					IPlayer.OpenHTML(GameStartC_Config[i].HTML);
				}
			}

			for (unsigned int i = 0; i < GameStart_Config.size(); i++) {
				if (IPlayer.IsOnline() && IPlayer.GetClass() == GameStart_Config[i].Class && IPlayer.GetLevel() == 1)
				{
					int Weapon = CItem::CreateItem(GameStart_Config[i].Reward, GameStart_Config[i].Prefix, 1, -1);

					if (Weapon)
					{
						CIOObject::AddRef(Weapon);
						int Info = 4194432;

						if (GameStart_Config[i].Dss)
							*(DWORD*)(Weapon + 84) = GameStart_Config[i].Dss;

						if (GameStart_Config[i].Mix)
							Info += GameStart_Config[i].Mix;

						if (GameStart_Config[i].Bof)
							Info += 2097152;

						*(DWORD*)(Weapon + 48) = Info;
						*(DWORD*)(Weapon + 108) = GameStart_Config[i].Def;
						*(DWORD*)(Weapon + 116) = GameStart_Config[i].Eva;
						*(DWORD*)(Weapon + 100) = GameStart_Config[i].Attack;
						*(DWORD*)(Weapon + 104) = GameStart_Config[i].Magic;
						*(DWORD*)(Weapon + 112) = GameStart_Config[i].Toa;
						*(DWORD*)(Weapon + 124) = GameStart_Config[i].Upgrade;
						int ValidItem = CPlayer::_InsertItem(IPlayer.GetOffset(), 7, Weapon);

						if (ValidItem == 1)
						{
							CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 15, *(DWORD*)(Weapon + 108), 0);
							CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 10, *(DWORD*)(Weapon + 116), 0);
							CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 27, *(DWORD*)(Weapon + 100), 0);
							CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 28, *(DWORD*)(Weapon + 104), 0);
							CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 9, *(DWORD*)(Weapon + 112), 0);
							CDBSocket::Write(28, "ddbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 2, *(DWORD*)(Weapon + 124));
							PetLifeCheck[*(DWORD*)(Weapon + 36)].Time = (int)time(0) + GameStart_Config[i].Time;
							PetLifeCheck[*(DWORD*)(Weapon + 36)].Player = (int)IPlayer.GetOffset();
							for (int i = 0; i < 10; i++)
							{
								CDBSocket::Write(89, "ddd", IPlayer.GetPID(), PetLifeCheck.find(*(DWORD*)(Weapon + 36))->second.Time, *(DWORD*)(Weapon + 36));
							}
							CPlayer::Write(IPlayer.GetOffset(), 0xFF, "ddd", 230, *(DWORD*)(Weapon + 36), GameStart_Config[i].Time);
							*(DWORD*)(Weapon + 68) = GetTickCount() + (GameStart_Config[i].Time);
							*(DWORD*)(Weapon + 72) = 0;
							CItem::OnTimer(Weapon, 0);
							CBase::Delete((void*)Weapon);
						}
						CIOObject::Release((void*)Weapon);
					}
				}
			}

			for (unsigned int i = 0; i < PlayerGameStartN_Config.size(); i++) {
				if (IPlayer.IsOnline() && IPlayer.GetLevel() == 1)
					CItem::InsertItem((int)IPlayer.GetOffset(), 21, PlayerGameStartN_Config[i].index, PlayerGameStartN_Config[i].prefix, PlayerGameStartN_Config[i].amount, -1);
			}

			IPlayer.Buff(155, 129620000, 0);
		}
	}
	*/

#include "Tools.h"

void __fastcall MyGameStart(void* Player, void* edx) {
    IChar IPlayer(Player);
    CPlayer::GameStart(Player);

    if (IPlayer.IsOnline() && !IPlayer.IsBuff(155) && IPlayer.GetLevel() == 1)
    {
        if ((std::string)Welcome_msg_first == "true" && IPlayer.GetLevel() == 1)
        {
            char FIRST[1024];
            sprintf_s(FIRST, "Welcome to HighGamer's Server source! Buy it at Discord: HighGamer.com#8990");
            CPlayer::WriteAll(0xFF, "dsd", 247, FIRST, 1);
            sprintf_s(FIRST, firstmsg, IPlayer.GetName());
            CPlayer::WriteAll(0xFF, "dsd", 247, FIRST, 4);
        }

        for (unsigned int i = 0; i < GameStartC_Config.size(); i++) {
            if (IPlayer.IsOnline() && IPlayer.GetClass() == GameStartC_Config[i].Class)
            {
                (*(void(__cdecl**)(int, signed int, signed int, signed int))(*(DWORD*)(int)IPlayer.GetOffset() + 88))((int)IPlayer.GetOffset(), 26, 1, GameStartC_Config[i].level - 1);//Level
                (*(void(__cdecl**)(int, signed int, signed int, int))(*(DWORD*)(int)IPlayer.GetOffset() + 88))((int)IPlayer.GetOffset(), 23, 1, 5 * *(DWORD*)((int)IPlayer.GetOffset() + 60));//Stats Point
                (*(void(__cdecl**)(DWORD, signed int, DWORD, DWORD))(*(DWORD*)(int)IPlayer.GetOffset() + 88))((int)IPlayer.GetOffset(), 24, 1, 2 * *(DWORD*)((int)IPlayer.GetOffset() + 60));//Skill Point
                IPlayer.Teleport(0, GameStartC_Config[i].X, GameStartC_Config[i].Y);
                IPlayer.OpenHTML(GameStartC_Config[i].HTML);
            }
        }

        for (unsigned int j = 0; j < GameStart_Config.size(); j++) {
            if (IPlayer.IsOnline() && IPlayer.GetClass() == GameStart_Config[j].Class && IPlayer.GetLevel() == 1)
            {
                int Weapon = CItem::CreateItem(GameStart_Config[j].Reward, GameStart_Config[j].Prefix, 1, -1);

                if (Weapon)
                {
                    CIOObject::AddRef(Weapon);
                    int Info = 4194432;

                    if (GameStart_Config[j].Dss)
                        *(DWORD*)(Weapon + 84) = GameStart_Config[j].Dss;

                    if (GameStart_Config[j].Mix)
                        Info += GameStart_Config[j].Mix;

                    if (GameStart_Config[j].Bof)
                        Info += 2097152;

                    *(DWORD*)(Weapon + 48) = Info;
                    *(DWORD*)(Weapon + 108) = GameStart_Config[j].Def;
                    *(DWORD*)(Weapon + 116) = GameStart_Config[j].Eva;
                    *(DWORD*)(Weapon + 100) = GameStart_Config[j].Attack;
                    *(DWORD*)(Weapon + 104) = GameStart_Config[j].Magic;
                    *(DWORD*)(Weapon + 112) = GameStart_Config[j].Toa;
                    *(DWORD*)(Weapon + 124) = GameStart_Config[j].Upgrade;
                    int ValidItem = CPlayer::_InsertItem(IPlayer.GetOffset(), 7, Weapon);

                    if (ValidItem == 1)
                    {
                        CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 15, *(DWORD*)(Weapon + 108), 0);
                        CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 10, *(DWORD*)(Weapon + 116), 0);
                        CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 27, *(DWORD*)(Weapon + 100), 0);
                        CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 28, *(DWORD*)(Weapon + 104), 0);
                        CDBSocket::Write(17, "ddbbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 9, *(DWORD*)(Weapon + 112), 0);
                        CDBSocket::Write(28, "ddbb", *(DWORD*)(Weapon + 36), *(DWORD*)(Weapon + 32), 2, *(DWORD*)(Weapon + 124));
                        PetLifeCheck[*(DWORD*)(Weapon + 36)].Time = (int)time(0) + GameStart_Config[j].Time;
                        PetLifeCheck[*(DWORD*)(Weapon + 36)].Player = (int)IPlayer.GetOffset();
                        for (int k = 0; k < 10; k++)
                        {
                            CDBSocket::Write(89, "ddd", IPlayer.GetPID(), PetLifeCheck.find(*(DWORD*)(Weapon + 36))->second.Time, *(DWORD*)(Weapon + 36));
                        }
                        CPlayer::Write(IPlayer.GetOffset(), 0xFF, "ddd", 230, *(DWORD*)(Weapon + 36), GameStart_Config[j].Time);
                        *(DWORD*)(Weapon + 68) = GetTickCount() + (GameStart_Config[j].Time);
                        *(DWORD*)(Weapon + 72) = 0;
                        CItem::OnTimer(Weapon, 0);
                        CBase::Delete((void*)Weapon);
                    }
                    CIOObject::Release((void*)Weapon);
                }
            }
        }

        for (unsigned int m = 0; m < PlayerGameStartN_Config.size(); m++) {
            if (IPlayer.IsOnline() && IPlayer.GetLevel() == 1)
                CItem::InsertItem((int)IPlayer.GetOffset(), 21, PlayerGameStartN_Config[m].index, PlayerGameStartN_Config[m].prefix, PlayerGameStartN_Config[m].amount, -1);
        }

        IPlayer.Buff(155, 129620000, 0);
    }
}