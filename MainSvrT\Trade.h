namespace MainSvrT
{
	void __fastcall CancelTrade(void *Player, void *edx)
	{
		IChar IPlayer(Player);

		if (IPlayer.IsOnline())
		{
			CPlayer::CancelTrade(Player);
			IPlayer.CancelBuff(257);
		}
	}

	void __fastcall TradeAgreed(void *Player, void *edx)
	{
		IChar IPlayer(Player);

		if (IPlayer.IsOnline())
		{
			CPlayer::TradeAgreed(Player);
			IPlayer.CancelBuff(257);
		}
	}

	void __fastcall ShowOffItem(void *Player, void *edx, int Amount, void *pPacket, int pPos)
	{
		IChar IPlayer(Player);

		if (IPlayer.IsOnline())
		{
			if (IPlayer.IsBuff(257))
			{
				IPlayer.SystemMessage("You only can upload once on the trading list.",TEXTCOLOR_RED);
				return;
			}

			CPlayer::ShowOffItem(Player,Amount,pPacket,pPos);
			IPlayer.Buff(257,604800,0);
		}
	}

	void __fastcall EndPvP(int Looser, void *edx, int Winner, int a, int b, int c)
	{
		IChar BDL((void*)Looser); IChar BDW((void*)Winner); int Opponent = 0;

		if (c == 6 && BDW.IsOnline() && BetDuelItems.count(BDW.GetID()))
		{
			auto range = BetDuelItems.equal_range(BDW.GetID());

			for (auto i = range.first; i != range.second; i++)
			{
				if (i->second.Removed == true) CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Kal Online",BDW.GetName(),i->second.IID,0,0,i->second.Prefix,i->second.Amount,0,"You are the winner of bet duel");				
				if (i->second.Removed == false) BDW.GiveReward(i->second.Index,i->second.Prefix,i->second.Amount,-128,0,0,0,0,0,0,0,"You are the winner of bet duel");
				if (i->second.Opponent != BDW.GetID()) Opponent = i->second.Opponent;
			}

			if (BetDuelItems.count(BDW.GetID())) BetDuelItems.erase(BDW.GetID());
			if (BetDuelItems.count(Opponent)) BetDuelItems.erase(Opponent);
		}

		if (c == 7)
		{
			if (BDW.IsOnline() && BetDuelItems.count(BDW.GetID()))
			{
				auto range = BetDuelItems.equal_range(BDW.GetID());

				for (auto i = range.first; i != range.second; i++)
				{
					if (i->second.Owner == BDW.GetPID())
					{
						if (i->second.Removed == true) CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Kal Online",BDW.GetName(),i->second.IID,0,0,i->second.Prefix,i->second.Amount,0,"You are the winner of bet duel");				
						if (i->second.Removed == false) BDW.GiveReward(i->second.Index,i->second.Prefix,i->second.Amount,-128,0,0,0,0,0,0,0,"Bet duel ended draw");
					}
				}

				if (BetDuelItems.count(BDW.GetID())) BetDuelItems.erase(BDW.GetID());
			}

			if (BDL.IsOnline() && BetDuelItems.count(BDL.GetID()))
			{
				auto range = BetDuelItems.equal_range(BDL.GetID());

				for (auto i = range.first; i != range.second; i++)
				{
					if (i->second.Owner == BDL.GetPID())
					{
						if (i->second.Removed == true) CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Kal Online",BDL.GetName(),i->second.IID,0,0,i->second.Prefix,i->second.Amount,0,"You are the winner of bet duel");				
						if (i->second.Removed == false) BDL.GiveReward(i->second.Index,i->second.Prefix,i->second.Amount,-128,0,0,0,0,0,0,0,"Bet duel ended draw");
					}
				}

				if (BetDuelItems.count(BDL.GetID())) BetDuelItems.erase(BDL.GetID());
			}
		}

		CPlayer::EndPvP(Looser,Winner,a,b,c);
	}

	void __fastcall _TradeItem(int FPlayer, void *edx, int SPlayer)
	{
		IChar IPlayer((void*)FPlayer); IChar xPlayer((void*)SPlayer);

		if (BetDuel.count(IPlayer.GetID()) && BetDuel.find(IPlayer.GetID())->second == xPlayer.GetID() && IPlayer.IsOnline() && xPlayer.IsOnline())
		{
			int a = 0, b = 0, c = 0, d = 0, e = 0, amount = 0, iid = 0, h = 0, i = 0, j = 0, item = 0;
			Undefined::Check((int)((char*)FPlayer + 1080),(int)&a);

			while (1)
			{
				b = (int)Undefined::sub_445B40(FPlayer + 1080,&c);
				if (!Undefined::CheckValues(&a,b)) break;
				d = Undefined::sub_420010(&a);
				amount = *(DWORD*)(d + 4); iid = *(DWORD*)d;
				Undefined::CreateMonsterValue((void *)(FPlayer + 1068),(int)&h,(int)&iid);
				i = Undefined::Check(FPlayer + 1068,(int)&j);
				if (!Undefined::CheckValues(&h,i)) break;
				item = *(DWORD*)(Undefined::GetValue(&h) + 4);
				*(DWORD*)&a += 8;
				BDItems Set;
				Set.Amount = amount;
				Set.IID = iid;
				Set.Index = *(DWORD*)(*(DWORD*)(item + 40) + 64);
				Set.Owner = IPlayer.GetPID();
				Set.Opponent = xPlayer.GetID();

				if (*(DWORD*)(item + 44))
					Set.Prefix = *(DWORD*)(*(DWORD*)(item + 44) + 32);
				else
					Set.Prefix = 0;

				if (amount == 1 && *(DWORD*)(item + 52) == 1)
				{
					Set.Removed = true;
					CPlayer::Write(IPlayer.GetOffset(),8,"db",iid,11);
					CDBSocket::Write(7,"dddwbd",iid,IPlayer.GetPID(),0,*(DWORD*)(*(DWORD*)(item + 40) + 64),11,1);
				} else {
					Set.Removed = false;
					CPlayer::RemoveItem(IPlayer.GetOffset(),9,*(DWORD*)(*(DWORD*)(item + 40) + 64),amount);
				}

				BetDuelItems.insert(std::make_pair(IPlayer.GetID(),Set));
				Set.Opponent = IPlayer.GetID();
				BetDuelItems.insert(std::make_pair(xPlayer.GetID(),Set));
			}

			if (IPlayer.IsOnline() && xPlayer.IsOnline() && !CChar::IsGState((int)IPlayer.GetOffset(),128) && !CChar::IsGState((int)xPlayer.GetOffset(),128))
			{
				CChar::AddGState(FPlayer, 128);
				CPlayer::Write(IPlayer.GetOffset(),46,"dd",*(DWORD*)(FPlayer + 28),*(DWORD*)(FPlayer + 280));
				*(DWORD*)(FPlayer + 1516) = SPlayer;
				CChar::AddGState(SPlayer, 128);		
				CPlayer::Write(xPlayer.GetOffset(),46,"dd",*(DWORD*)(SPlayer + 28),*(DWORD*)(SPlayer + 280));
				*(DWORD*)(SPlayer + 1516) = FPlayer;
				*(DWORD *)(SPlayer + 1512) = *(DWORD*)(FPlayer + 1512);
				*(DWORD*)(FPlayer + 1512) = *(DWORD*)(SPlayer + 1512);
			}

			BetDuel.erase(IPlayer.GetID());
		} else {
			CPlayer::_TradeItem((void*)FPlayer,SPlayer);
		}
	}
}