namespace MainSvrT
{
	void __fastcall RisingKing4th(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(91);

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			int nMana = (60 + (nSkillGrade * 3));
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (nSkillGrade && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < nMana)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						int Around = Target.GetObjectListAround(3);
						int nDmg = (((IPlayer.GetMaxMagAttack() + (((IPlayer.GetMaxMagAttack() / 1) + (CChar::GetInt((int)IPlayer.GetOffset()) / 1)) + ((CChar::GetWis((int)IPlayer.GetOffset()) / 1) + (nSkillGrade * 2000)))) * 8) + (IPlayer.GetLevel() * 10)) * SHMRKM;
						if(Target.GetType() == 0) nDmg = (nDmg * SHMRK) / 100;
						IPlayer._ShowBattleAnimation(Target, 91);
						IPlayer.SetDirection(Target);
						IPlayer.DecreaseMana(nMana);
						IPlayer.IncreaseHp(nDmg);

						while(Around)
						{
							IChar Object((void*)*(DWORD*)Around);

							if (Object.IsValid() && IPlayer.IsValid() && (*(int (__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0))
								IPlayer.OktayDamageArea(Object,nDmg,91);

							Around = CBaseList::Pop((void*)Around);
						}
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}