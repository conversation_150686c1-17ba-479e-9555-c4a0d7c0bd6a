#include <Windows.h>
#include <sstream>
#include <strstream>
#include <stdlib.h>
#include <direct.h>
#include <io.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include <stdio.h>
#include <stdarg.h>
#include <time.h>
#include <math.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
#include <string>
#include <list>
#include <map>
#include <Shlwapi.h>
#include "Functions.h"
#include "XObjectDB.h"
#include "Lisp.h"
#include "XFile.h"
#include "XMacro.h"
#include "XParser.h"
#include "Utility.h"
#pragma comment(lib, "shlwapi.lib")
#pragma warning (disable : 4996)

namespace XObjectDB
{
    std::string m_strConfig;

    struct ObjectTable
    {
        char* pString;
        BOOL(*pfnCreateObject)(lisp::var param);
        int operator - (const char* str) const { return strcmp(pString, str); }
    };

    DWORD CreateObject(DWORD dwParam, lisp::var var);

    ObjectTable m_vObjectTable[] =
    {
        { "genmonster", MacroGenMonster::GenMonsterDB::LoadConfig },
        { "voyagershop", MacroGenVoyager::CGenVoyagerDB::LoadConfig },
    };

    bool FileExists(const char* path)
    {
        return PathFileExistsA(path) == TRUE;
    }

    std::string GetBaseName(const std::string& path)
    {
        char base[_MAX_FNAME] = { 0 };
        _splitpath(path.c_str(), nullptr, nullptr, base, nullptr);
        return std::string(base);
    }
}

DWORD XObjectDB::CreateObject(DWORD dwParam, lisp::var var)
{
    if (var.errorp()) return FALSE;
    LPCSTR pString = (LPCSTR)dwParam;
    ObjectTable* pTable = equal(&m_vObjectTable[0], &m_vObjectTable[sizeof(m_vObjectTable) / sizeof(ObjectTable)], pString);
    if (pTable == &m_vObjectTable[sizeof(m_vObjectTable) / sizeof(ObjectTable)]) return FALSE;
    return (*pTable->pfnCreateObject)(var);
}

BOOL XObjectDB::Load(LPCSTR szTitle)
{
    std::strstream normal;
    normal << szTitle << ".txt" << std::ends;
    return Load(normal.str(), FALSE);
}

BOOL XObjectDB::Load(LPCSTR szFileName, BOOL bDecode)
{
    std::string fullPath = szFileName;
    std::string baseName = GetBaseName(fullPath);

    if (!FileExists(fullPath.c_str()))
    {
        CConsole::Red("%s.txt File open failed...\n", baseName.c_str());
        return FALSE;
    }

    CConsole::Blue("Reading [%s]...\n", baseName.c_str());

    XFileEx file;
    if (!file.PostOpen(_alloca(file.PreOpen(szFileName, -1))))
    {
        CConsole::Red("Failed to process %s.txt\n", baseName.c_str());
        return FALSE;
    }

    XParser parser;
    parser.Open(&file);
    //XParser::TOKEN token;  // Commented out unreferenced variable
    std::string name;

    /*for (;;) {
        switch (token = parser.GetToken()) {
        case XParser::T_END:
            goto quit;
        case XParser::T_CLOSE:
            CConsole::Red("Unmatched close parenthesis at %s Line: %d\n", szFileName, parser.GetLine());
            parser.ResetDepth();
            break;
        case XParser::T_OPEN:
        {
            if (parser.GetToken() != XParser::T_STRING) goto fail;
            name = parser.GetString();
            if (!parser.ParseList(CreateObject, (DWORD)name.c_str()))
            {
                CConsole::Red("Invalid format at %s Line: %d\n", szFileName, parser.GetLine());
                return FALSE;
            }
        }
        break;
        default:
        fail:
            CConsole::Red("Invalid format at %s Line: %d\n", szFileName, parser.GetLine());
            for (;;) {
                if (parser.GetDepth() == 0) break;
                if (parser.GetToken() == XParser::T_END) break;
            }
        }
    }*/
//quit:  // Commented out unreferenced label
    return TRUE;
}