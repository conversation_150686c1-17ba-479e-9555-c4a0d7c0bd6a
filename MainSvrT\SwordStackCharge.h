namespace MainSvrT
{
	void __fastcall SwordStackCharge(IChar IPlayer)
	{
		int pSkill = IPlayer.GetSkillPointer(16);

		if (pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			if (IPlayer.GetCurMp() <= 50) return;
			IPlayer.DecreaseMana(50); int c = 0;
			int a = IPlayer.GetSkillPointer(13);
			int b = IPlayer.GetSkillPointer(40);
			if (a && b) c = 10; if (a && !b) c = 8;
			ControlBlade.find(IPlayer.GetPID())->second += nSkillGrade;
			if (ControlBlade.find(IPlayer.GetPID())->second >= c) ControlBlade.find(IPlayer.GetPID())->second = c;
			CChar::WriteInSight(IPlayer.GetOffset(),235,"dbw",IPlayer.GetID(),51,ControlBlade.find(IPlayer.GetPID())->second);
			IPlayer._ShowBattleAnimation(IPlayer,16,nSkillGrade);
		}
	}
}