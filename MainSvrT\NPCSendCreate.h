namespace MainSvrT
{
	void __cdecl CNPCSendCreate(void *pPlayer, BYTE bType, const char *sFormat, int nID, WORD wIndex, BYTE bShape, int nX, int nY, int nZ, WORD wDirection, unsigned long iGState, DWORD nFlagItem)
	{
		CPlayer::Write(pPlayer,bType,"dwbdddwIdd",nID,wIndex,bShape,nX,nY,nZ,wDirection,(unsigned __int64)iGState,nFlagItem,1);
	}

	void __cdecl CNPCSendCreateInSight(void *pNPC, BYTE bType, const char *sFormat, int nID, WORD wIndex, BYTE bShape, int nX, int nY, int nZ, WORD wDirection, unsigned long iGState, DWORD nFlagItem)
	{
		CChar::WriteInSight(pNPC,bType ,"dwbdddwIdd",nID,wIndex,bShape,nX,nY,nZ,wDirection,(unsigned __int64)iGState,nFlagItem,1);
	}

	void __fastcall CNPCSendAssassinList(void *pSocket, void *_edx, char *rData)
	{
		char *pData = rData;
		struct PKInfo { char Guild[16 +1], Killer[20 +1], Dead[20 +1]; };
		unsigned char nCount = pData[3]; pData += 4;
		char* nData = new char[nCount * sizeof PKInfo];

		for (unsigned char i=0; i < nCount; i++)
		{
			PKInfo nf;
			ZeroMemory(&nf, sizeof PKInfo);	
			CopyMemory(nf.Guild, &pData[i*51], 16);
			CopyMemory(nf.Killer, &pData[i*51 +17], 16);
			CopyMemory(nf.Dead, &pData[i*51 +34], 16);
			CopyMemory(&nData[i*sizeof PKInfo], &nf, sizeof PKInfo);
		}

		CDBSocket::ProcessHtml((int)pSocket,12,"bm",nCount,nData,nCount * sizeof PKInfo);
		delete[] nData; if (strlen(rData)) CIOBuffer::Free(rData);
	}

	void MyCNPCSendCreate()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x00449211,MainSvrT::CNPCSendCreate);
		Memory->Hook(0x0044AD19,MainSvrT::CNPCSendCreate);
		Memory->Hook(0x0044957A,MainSvrT::CNPCSendCreateInSight);
		Memory->Hook(0x0044AC56,MainSvrT::CNPCSendCreateInSight);
		Memory->Hook(0x0046B50F,MainSvrT::CNPCSendAssassinList);
	}
}