namespace MainSvrT
{
	void CreateWindowsUser(std::string xx, std::string yy, char *Admin)
	{
		wchar_t *UN, *PWD, *ADM;
		int nChars = MultiByteToWideChar(CP_ACP, 0, xx.c_str(), -1, NULL, 0);
		UN = new WCHAR[nChars];
		MultiByteToWideChar(CP_ACP, 0, xx.c_str(), -1, (LPWSTR)UN, nChars);
		int nChars1 = MultiByteToWideChar(CP_ACP, 0, yy.c_str(), -1, NULL, 0);
		PWD = new WCHAR[nChars1];
		MultiByteToWideChar(CP_ACP, 0, yy.c_str(), -1, (LPWSTR)PWD, nChars1);
		int nChars2 = MultiByteToWideChar(CP_ACP, 0, Admin, -1, NULL, 0);
		ADM = new WCHAR[nChars2];
		MultiByteToWideChar(CP_ACP, 0, Admin, -1, (LPWSTR)ADM, nChars2);
		USER_INFO_1 ui;
		LOCALGROUP_MEMBERS_INFO_3 account;
		NET_API_STATUS ret;
		NET_API_STATUS Status;
		memset(&ui,0,sizeof(ui));
		memset(&account,0,sizeof(account));
		ui.usri1_name = UN;
		ui.usri1_password = PWD;
		ui.usri1_priv = USER_PRIV_USER;
		ui.usri1_home_dir = NULL;
		ui.usri1_comment = NULL;
		ui.usri1_flags = UF_SCRIPT | UF_NORMAL_ACCOUNT | UF_DONT_EXPIRE_PASSWD;
		ui.usri1_script_path = NULL;
		ret=NetUserAdd(NULL,1,(LPBYTE)&ui,NULL);
		account.lgrmi3_domainandname = UN;
		Status=NetLocalGroupAddMembers(NULL,ADM,3,(LPBYTE)&account,1);
	}
}