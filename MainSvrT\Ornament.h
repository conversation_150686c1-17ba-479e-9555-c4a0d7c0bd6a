int __cdecl GetLowestTrigramGrade(int PID)
{
	int Grade = 20;
	if (TrigramHP.find(PID)->second && TrigramHP.find(PID)->second < Grade) Grade = TrigramHP.find(PID)->second;
	if (TrigramMP.find(PID)->second && TrigramMP.find(PID)->second < Grade) Grade = TrigramMP.find(PID)->second;
	if (TrigramAtk.find(PID)->second && TrigramAtk.find(PID)->second < Grade) Grade = TrigramAtk.find(PID)->second;
	if (TrigramStr.find(PID)->second && TrigramStr.find(PID)->second < Grade) Grade = TrigramStr.find(PID)->second;
	if (TrigramAgi.find(PID)->second && TrigramAgi.find(PID)->second < Grade) Grade = TrigramAgi.find(PID)->second;
	if (TrigramInt.find(PID)->second && TrigramInt.find(PID)->second < Grade) Grade = TrigramInt.find(PID)->second;
	if (TrigramWis.find(PID)->second && TrigramWis.find(PID)->second < Grade) Grade = TrigramWis.find(PID)->second;
	if (TrigramHth.find(PID)->second && TrigramHth.find(PID)->second < Grade) Grade = TrigramHth.find(PID)->second;
	if (Grade == 20) Grade = 0;
	return Grade;
}

int __fastcall OrnamentPutOn(int Itemx, void *edx, int Playerx)
{
	IItem Item((void*)Itemx); IChar Player((void*)Playerx); int GetPetTime = 259200;

	if (Player.IsOnline())
	{
		if (Player.IsBuff(256))
		{
			Player.SystemMessage("You need to wait 3 seconds to put on your item.", TEXTCOLOR_INFOMSG);
			return 0;
		} else {
			Player.Buff(256,2,0);
			int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
			MainSvrT::ReadItemTable(Item.GetIID(),GetDSS,GetStat,GetTime,Lock);
			if (Item.CheckIndex() >= 3384 && Item.CheckIndex() <= 3386 && !MainSvrT::CPlayerIsWState(Playerx,0,-112)) return 0;

			if (Item.CheckIndex() >= 3384 && Item.CheckIndex() <= 3386 && CheckHaninMirror.find(Player.GetPID())->second)
			{
				int Type = GetStat % 100;

				if (Player.IsBuff(418))
				{
					int EssenceStat = Player.GetBuffValue(418);

					if (EssenceStat & (1 << Type))
					{
						Player.BoxMsg("You can wear each essence type once.");
						return 0;
					}
				}
			}

			if (Item.CheckIndex() >= 3381 && Item.CheckIndex() <= 3383)
			{
				if (CheckHaninMirror.find(Player.GetPID())->second)
					return 0;
				else
					CheckHaninMirror[Player.GetPID()] = Item.GetIID();
			}

			if (Item.CheckIndex() >= 3384 && Item.CheckIndex() <= 3386 && CheckHaninMirror.find(Player.GetPID())->second)
			{
				int Recheck = 0, Check = 0, Value = 0, Itemy = 0;
				Undefined::CreateMonsterValue((char*)Playerx + 1068, (int)&Value, (int)&CheckHaninMirror.find(Player.GetPID())->second);
				Check = Undefined::Check((int)((char*)Playerx + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return 0;
				Itemy = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem Mirror((void*)Itemy);
				int SlotSize = 0, TotalSlotSize = 0;
				SlotSize = GetStat + 1;
				if (SlotSize == 1 && Mirror.CheckIndex() == 3383) SlotSize = 2;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-96)) TotalSlotSize += 1;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-97)) TotalSlotSize += 1;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-98)) TotalSlotSize += 1;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-99)) TotalSlotSize += 1;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-100)) TotalSlotSize += 1;
				if (TotalSlotSize >= SlotSize) return 0;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-96)) *(DWORD*)(*(DWORD*)(Itemx + 40) + 72) = -95;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-97)) *(DWORD*)(*(DWORD*)(Itemx + 40) + 72) = -96;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-98)) *(DWORD*)(*(DWORD*)(Itemx + 40) + 72) = -97;
				if (MainSvrT::CPlayerIsWState(Playerx,0,-99)) *(DWORD*)(*(DWORD*)(Itemx + 40) + 72) = -98;
			}

			if (PetTime.count(Item.CheckIndex()) && !ItemLifeCheck.count(Item.GetIID()))
			{
				if (PetTime.count(Item.CheckIndex()) && PetTime.find(Item.CheckIndex())->second.Time > 0) GetPetTime = PetTime.find(Item.CheckIndex())->second.Time;
				ItemLifeCheck[Item.GetIID()] = (int)time(0) + GetPetTime;
				MainSvrT::UpdateItemTable(Item.GetIID(),0,0,(int)time(0) + GetPetTime,"pet");
				CPlayer::Write(Player.GetOffset(),0xFF,"ddd",230,Item.GetIID(),GetPetTime*1000);
				*(DWORD*)(Itemx+68) = GetTickCount() + (2000*GetPetTime);
				*(DWORD*)(Itemx+72) = 0;
				CItem::OnTimer(Itemx,0);
			}

			if (PetTime.count(Item.CheckIndex()))
			{			
				if ((Item.CheckIndex() >= 2004 && Item.CheckIndex() <= 2007) || (Item.CheckIndex() >= 2422 && Item.CheckIndex() <= 2423) || (Item.CheckIndex() >= 4101 && Item.CheckIndex() <= 4102) || (Item.CheckIndex() >= 7124 && Item.CheckIndex() <= 7125))
					CChar::WriteInSight(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),2);
				else
					CChar::WriteInSight(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),0);
			}

			if (ConfigLimitedPet.count(Item.CheckIndex())) CChar::WriteInSight(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),0);
			if (MonsterPet.count(Item.CheckIndex())) CChar::WriteInSight(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),0);
			return CItemOrnament::PutOn(Itemx,Playerx);
		}
	} else {
		return 0;
	}
}

int __fastcall OrnamentPutOff(void *Itemx, void *edx, int Playerx)
{
	IItem Item(Itemx); IChar Player((void*)Playerx);

	if (Player.IsOnline())
	{
		if (Player.IsBuff(256))
		{
			Player.SystemMessage("You need to wait 3 seconds to put off your item.", TEXTCOLOR_INFOMSG);
			return 0;
		} else {
			Player.Buff(256,2,0);
			return CItemOrnament::PutOff(Itemx,Playerx);
		}
	} else {
		return 0;
	}
}

int __fastcall CItemOrnamentFreeSpec(void *Itemx, void *edx, int Playerx)
{
	IItem Item(Itemx); IChar Player((void*)Playerx);

	if (Player.IsOnline())
	{
		int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
		MainSvrT::ReadItemTable(Item.GetIID(),GetDSS,GetStat,GetTime,Lock);

		if (Item.CheckIndex() >= 7883 && Item.CheckIndex() <= 7906)
		{
			int Stat = ItemShow::BattleHorseStat(Player.GetOffset(),Item.GetIID(),Item.CheckIndex(),GetStat,false);
			if (Item.CheckIndex() == 7883 || Item.CheckIndex() == 7889 || Item.CheckIndex() == 7895 || Item.CheckIndex() == 7901) BattleHorse[Player.GetPID()].Str = 0;
			if (Item.CheckIndex() == 7884 || Item.CheckIndex() == 7890 || Item.CheckIndex() == 7896 || Item.CheckIndex() == 7902) BattleHorse[Player.GetPID()].Int = 0;
			if (Item.CheckIndex() == 7885 || Item.CheckIndex() == 7891 || Item.CheckIndex() == 7897 || Item.CheckIndex() == 7903) BattleHorse[Player.GetPID()].Def = 0;
			if (Item.CheckIndex() == 7886 || Item.CheckIndex() == 7892 || Item.CheckIndex() == 7898 || Item.CheckIndex() == 7904) BattleHorse[Player.GetPID()].Hp = 0;
			if (Item.CheckIndex() == 7887 || Item.CheckIndex() == 7893 || Item.CheckIndex() == 7899 || Item.CheckIndex() == 7905) BattleHorse[Player.GetPID()].Abs = 0;
			if (Item.CheckIndex() == 7888 || Item.CheckIndex() == 7894 || Item.CheckIndex() == 7900 || Item.CheckIndex() == 7906) BattleHorse[Player.GetPID()].Eva = 0;

			if (BattleHorse.find(Player.GetPID())->second.Active)
			{
				if (Item.CheckIndex() == 7883 || Item.CheckIndex() == 7889 || Item.CheckIndex() == 7895 || Item.CheckIndex() == 7901) Player.RemoveStr(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7884 || Item.CheckIndex() == 7890 || Item.CheckIndex() == 7896 || Item.CheckIndex() == 7902) Player.RemoveInt(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7885 || Item.CheckIndex() == 7891 || Item.CheckIndex() == 7897 || Item.CheckIndex() == 7903) Player.RemoveDef(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7886 || Item.CheckIndex() == 7892 || Item.CheckIndex() == 7898 || Item.CheckIndex() == 7904) Player.RemoveHp(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7887 || Item.CheckIndex() == 7893 || Item.CheckIndex() == 7899 || Item.CheckIndex() == 7905) Player.RemoveAbsorb(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7888 || Item.CheckIndex() == 7894 || Item.CheckIndex() == 7900 || Item.CheckIndex() == 7906) Player.RemoveEva(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
			}
		}

		if ((Item.CheckIndex() >= 7700 && Item.CheckIndex() <= 7707) || (Item.CheckIndex() >= 7719 && Item.CheckIndex() <= 7722))
		{
			int FStat = 0, SStat = 0; MainSvrT::ReadMonsterPet(Item.GetIID(),FStat,SStat);
			int Str = Player.GetStr(), Wis = Player.GetWis(), Hth = Player.GetHth(), Int = Player.GetInt(), Agi = Player.GetAgi();
			int Otp = (15*Str/54), MinPAtk = ((11*Str)-80)/30, MaxPAtk = ((8*Str)-25)/15, Eva = Agi/3, HpPoint = ((2*Hth*Hth)/100);
			int MpPoint = ((2*Wis*Wis)/100), MinMAtk = (((7*Int)-20)/12)+(Wis/7), MaxMAtk = ((7*Int)/12)+(14*Wis/45);

			if (FStat)
			{
				int Type = FStat >> 16; int Stat = FStat & 0xFF;
				if (Type == 0) Player.RemoveStr((Str*Stat)/100);
				if (Type == 1) Player.RemoveWis((Wis*Stat)/100);
				if (Type == 2) Player.RemoveInt((Int*Stat)/100);
				if (Type == 3) Player.RemoveHp((Hth*Stat)/100);
				if (Type == 4) Player.RemoveAgi((Agi*Stat)/100);
				if (Type == 5) Player.RemoveOTP(Stat);
				if (Type == 6) Player.RemoveEva(Stat);
				if (Type == 7) Player.RemoveDef(Stat);
				if (Type == 9) Player.RemoveAbsorb(Stat);
				if (Type == 10) Player.DecreaseMaxHp(HpPoint*Stat);
				if (Type == 11) Player.DecreaseMaxMp(MpPoint*Stat);
				if (Type == 13) Player.RemovePhyAttack((MinPAtk*Stat)/4);
				if (Type == 14) Player.RemovePhyAttack((MaxPAtk*Stat)/4);
				if (Type == 15) Player.DecreaseCritRate(Stat);
				if (Type == 16) Player.DecreaseCritDamage(Stat);
				if (Type == 17) Player.DecreaseEBRate(1);
				if (Type == 18) Player.RemoveMagAttack((MinMAtk*Stat)/4);
				if (Type == 19) Player.RemoveMagAttack((MaxMAtk*Stat)/4);
			}

			if (SStat)
			{
				int Type = SStat >> 16; int Stat = SStat & 0xFF;
				if (Type == 0) Player.RemoveStr((Str*Stat)/100);
				if (Type == 1) Player.RemoveWis((Wis*Stat)/100);
				if (Type == 2) Player.RemoveInt((Int*Stat)/100);
				if (Type == 3) Player.RemoveHp((Hth*Stat)/100);
				if (Type == 4) Player.RemoveAgi((Agi*Stat)/100);
				if (Type == 5) Player.RemoveOTP(Stat);
				if (Type == 6) Player.RemoveEva(Stat);
				if (Type == 7) Player.RemoveDef(Stat);
				if (Type == 9) Player.RemoveAbsorb(Stat);
				if (Type == 10) Player.DecreaseMaxHp(HpPoint*Stat);
				if (Type == 11) Player.DecreaseMaxMp(MpPoint*Stat);
				if (Type == 13) Player.RemovePhyAttack((MinPAtk*Stat)/4);
				if (Type == 14) Player.RemovePhyAttack((MaxPAtk*Stat)/4);
				if (Type == 15) Player.DecreaseCritRate(Stat);
				if (Type == 16) Player.DecreaseCritDamage(Stat);
				if (Type == 17) Player.DecreaseEBRate(1);
				if (Type == 18) Player.RemoveMagAttack((MinMAtk*Stat)/4);
				if (Type == 19) Player.RemoveMagAttack((MaxMAtk*Stat)/4);
			}
		}

		if (Item.CheckIndex() >= 3381 && Item.CheckIndex() <= 3383 && MainSvrT::CPlayerIsWState(Playerx,0,-96))
			return 0;

		if (Item.CheckIndex() >= 3381 && Item.CheckIndex() <= 3383 && CheckHaninMirror.find(Player.GetPID())->second)
			CheckHaninMirror[Player.GetPID()] = 0;

		if (Item.CheckIndex() >= 3384 && Item.CheckIndex() <= 3386 && CheckHaninMirror.find(Player.GetPID())->second)
		{
			int Recheck = 0, Check = 0, Value = 0, Itemy = 0;
			Undefined::CreateMonsterValue((char*)Playerx + 1068, (int)&Value, (int)&CheckHaninMirror.find(Player.GetPID())->second);
			Check = Undefined::Check((int)((char*)Playerx + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return 0;
			Itemy = *(DWORD*)(Undefined::GetValue(&Value) + 4);
			IItem Mirror((void*)Itemy);
			int Valuex = 0, Type = 0; double Stat = 0;
			Valuex = GetStat;
			Type = Valuex % 100;
			Stat = Valuex / 10000;
			int EssenceStat = Player.GetBuffValue(418);
			EssenceStat = EssenceStat & ~(1 << Type);
			Player.Buff(418,1296000,EssenceStat);
			int Str = Player.GetStr(), Wis = Player.GetWis(), Hth = Player.GetHth(), Int = Player.GetInt(), Agi = Player.GetAgi();
			int Otp = (15*Str/54), MinPAtk = ((11*Str)-80)/30, MaxPAtk = ((8*Str)-25)/15, Eva = Agi/3, HpPoint = ((2*Hth*Hth)/100);
			int MpPoint = ((2*Wis*Wis)/100), MinMAtk = (((7*Int)-20)/12)+(Wis/7), MaxMAtk = ((7*Int)/12)+(14*Wis/45);

			if (Stat && Mirror.CheckIndex() == 3381)
			{
				if (Type == 0) Player.RemoveStr(((Str*Stat)/100)+0.5);
				if (Type == 1) Player.RemoveWis(((Wis*Stat)/100)+0.5);
				if (Type == 2) Player.RemoveInt(((Int*Stat)/100)+0.5);
				if (Type == 3) Player.RemoveHp(((Hth*Stat)/100)+0.5);
				if (Type == 4) Player.RemoveAgi(((Agi*Stat)/100)+0.5);
				if (Type == 5) Player.RemoveOTP(Stat);
				if (Type == 6) Player.RemoveEva(Stat);
				if (Type == 7) Player.RemoveDef(Stat);
				if (Type == 9) Player.RemoveAbsorb(Stat/2);
				if (Type == 10) Player.DecreaseMaxHp(HpPoint*Stat);
				if (Type == 11) Player.DecreaseMaxMp(MpPoint*Stat);
				if (Type == 12) Player.DecreaseMovingSpeed((3*Stat)+0.5);
				if (Type == 13) Player.RemovePhyAttack(MinPAtk*(Stat/5));
				if (Type == 14) Player.RemovePhyAttack(MaxPAtk*(Stat/5));
				if (Type == 15) Player.DecreaseCritRate(Stat);
				if (Type == 16) Player.DecreaseCritDamage(Stat);
				if (Type == 17) Player.DecreaseEBRate(1);
				if (Type == 18) Player.RemoveMagAttack(MinMAtk*(Stat/5));
				if (Type == 19) Player.RemoveMagAttack(MaxMAtk*(Stat/5));
				if (Type == 20) Player.CancelBuff(298);
				if (Type == 21) Player.CancelBuff(330);
			}

			if (Stat && Mirror.CheckIndex() == 3382)
			{
				if (Type == 0) Player.RemoveStr(((Str*Stat)/90)+0.5);
				if (Type == 1) Player.RemoveWis(((Wis*Stat)/90)+0.5);
				if (Type == 2) Player.RemoveInt(((Int*Stat)/90)+0.5);
				if (Type == 3) Player.RemoveHp(((Hth*Stat)/90)+0.5);
				if (Type == 4) Player.RemoveAgi(((Agi*Stat)/90)+0.5);
				if (Type == 5) Player.RemoveOTP(Stat+1);
				if (Type == 6) Player.RemoveEva(Stat+1);
				if (Type == 7) Player.RemoveDef(Stat+1);
				if (Type == 9) Player.RemoveAbsorb(Stat/2);
				if (Type == 10) Player.DecreaseMaxHp(HpPoint*(Stat+1));
				if (Type == 11) Player.DecreaseMaxMp(MpPoint*(Stat+1));
				if (Type == 12) Player.DecreaseMovingSpeed((3*Stat)+1.5);
				if (Type == 13) Player.RemovePhyAttack(MinPAtk*(Stat/5));
				if (Type == 14) Player.RemovePhyAttack(MaxPAtk*(Stat/5));
				if (Type == 15) Player.DecreaseCritRate(Stat+1);
				if (Type == 16) Player.DecreaseCritDamage(Stat+1);
				if (Type == 17) Player.DecreaseEBRate(1);
				if (Type == 18) Player.RemoveMagAttack(MinMAtk*(Stat/5));
				if (Type == 19) Player.RemoveMagAttack(MaxMAtk*(Stat/5));
				if (Type == 20) Player.CancelBuff(298);
				if (Type == 21) Player.CancelBuff(330);
			}

			if (Stat && Mirror.CheckIndex() == 3383)
			{
				if (Type == 0) Player.RemoveStr(((Str*Stat)/80)+0.5);
				if (Type == 1) Player.RemoveWis(((Wis*Stat)/80)+0.5);
				if (Type == 2) Player.RemoveInt(((Int*Stat)/80)+0.5);
				if (Type == 3) Player.RemoveHp(((Hth*Stat)/80)+0.5);
				if (Type == 4) Player.RemoveAgi(((Agi*Stat)/80)+0.5);
				if (Type == 5) Player.RemoveOTP(Stat+2);
				if (Type == 6) Player.RemoveEva(Stat+2);
				if (Type == 7) Player.RemoveDef(Stat+2);
				if (Type == 9) Player.RemoveAbsorb(Stat/2);
				if (Type == 10) Player.DecreaseMaxHp(HpPoint*(Stat+2));
				if (Type == 11) Player.DecreaseMaxMp(MpPoint*(Stat+2));
				if (Type == 12) Player.DecreaseMovingSpeed((3*Stat)+2.5);
				if (Type == 13) Player.RemovePhyAttack(MinPAtk*(Stat/5));
				if (Type == 14) Player.RemovePhyAttack(MaxPAtk*(Stat/5));
				if (Type == 15) Player.DecreaseCritRate(Stat+2);
				if (Type == 16) Player.DecreaseCritDamage(Stat+2);
				if (Type == 17) Player.DecreaseEBRate(1);
				if (Type == 18) Player.RemoveMagAttack(MinMAtk*(Stat/5));
				if (Type == 19) Player.RemoveMagAttack(MaxMAtk*(Stat/5));
				if (Type == 20) Player.CancelBuff(298);
				if (Type == 21) Player.CancelBuff(330);
			}
		}

		if (Item.CheckIndex() >= 3384 && Item.CheckIndex() <= 3386 && CheckHaninMirror.find(Player.GetPID())->second)
		{
			if (!MainSvrT::CPlayerIsWState(Playerx,0,-100)) *(DWORD*)(*(DWORD*)((int)Itemx + 40) + 72) = -97;
			if (!MainSvrT::CPlayerIsWState(Playerx,0,-99)) *(DWORD*)(*(DWORD*)((int)Itemx + 40) + 72) = -96;
			if (!MainSvrT::CPlayerIsWState(Playerx,0,-98)) *(DWORD*)(*(DWORD*)((int)Itemx + 40) + 72) = -95;
			if (!MainSvrT::CPlayerIsWState(Playerx,0,-97)) *(DWORD*)(*(DWORD*)((int)Itemx + 40) + 72) = -94;
		}

		if (Item.CheckIndex() >= 2986 && Item.CheckIndex() <= 3009 && Player.IsBuff(311))
			return 0;

		if (Item.CheckIndex() >= 3018 && Item.CheckIndex() <= 3020)
		{
			if (Player.IsBuff(311) && Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second)
			{
				Player.DecreaseMaxHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][0]);
				Player.DecreaseMaxMp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][1]);
				Player.RemoveMagAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.RemovePhyAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.RemoveStr(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][3]);
				Player.RemoveAgi(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][4]);
				Player.RemoveInt(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][5]);
				Player.RemoveWis(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][6]);
				Player.RemoveHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][7]);
			}

			Taegeuk[*(DWORD*)((int)Itemx + 32)] = 0;
		}

		if (Item.CheckIndex() >= 2946 && Item.CheckIndex() <= 2948)
		{
			int YinYangType = 0;
			if (Item.CheckIndex() == 2947) YinYangType = 1;
			if (Item.CheckIndex() == 2948) YinYangType = 2;
			Player.CancelBuff(311);
			int YinYangGrade = TrigramGrade.find(*(DWORD*)((int)Itemx + 32))->second;
			Player.DecreaseMaxHp(TrigramHP.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramHP.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.DecreaseMaxMp(TrigramMP.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramMP.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemoveMagAttack(TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemovePhyAttack(TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemoveStr(TrigramStr.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramStr.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemoveAgi(TrigramAgi.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramAgi.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemoveInt(TrigramInt.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramInt.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemoveWis(TrigramWis.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramWis.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.RemoveHp(TrigramHth.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramHth.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));

			if (Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second)
			{
				Player.DecreaseMaxHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][0]);
				Player.DecreaseMaxMp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][1]);
				Player.RemoveMagAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.RemovePhyAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.RemoveStr(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][3]);
				Player.RemoveAgi(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][4]);
				Player.RemoveInt(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][5]);
				Player.RemoveWis(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][6]);
				Player.RemoveHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][7]);
			}
		}

		if (Item.CheckIndex() >= 2986 && Item.CheckIndex() <= 3009)
		{
			int GetCurrentGrade = (GetStat % 10000) / 100;

			if (TrigramGrade.count(*(DWORD*)((int)Itemx + 32)) && GetCurrentGrade == TrigramGrade.find(*(DWORD*)((int)Itemx + 32))->second)
				TrigramGrade[*(DWORD*)((int)Itemx + 32)] = GetLowestTrigramGrade(Player.GetPID());

			if (Item.CheckIndex() == 2986) TrigramHP[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2994) TrigramHP[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3002) TrigramHP[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2987) TrigramMP[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2995) TrigramMP[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3003) TrigramMP[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2988) TrigramAtk[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2996) TrigramAtk[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3004) TrigramAtk[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2989) TrigramStr[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2997) TrigramStr[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3005) TrigramStr[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2990) TrigramAgi[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2998) TrigramAgi[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3006) TrigramAgi[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2991) TrigramInt[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2999) TrigramInt[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3007) TrigramInt[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2992) TrigramWis[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3000) TrigramWis[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3008) TrigramWis[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 2993) TrigramHth[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3001) TrigramHth[*(DWORD*)((int)Itemx + 32)] = 0;
			if (Item.CheckIndex() == 3009) TrigramHth[*(DWORD*)((int)Itemx + 32)] = 0;
		}

		if (PetTime.count(Item.CheckIndex()))
		{
			PlayerPet[Player.GetPID()].Owner = 0;
			PlayerPet[Player.GetPID()].IID = 0;
			CPlayer::Write(Player.GetOffset(),0xFF,"dd",221,0);
		}

		if (MonsterPet.count(Item.CheckIndex()))
		{
			PlayerMonsterPet[Player.GetPID()].Index = 0;
			PlayerMonsterPet[Player.GetPID()].IID = 0;
		}

		if (ConfigLimitedPet.count(Item.CheckIndex()))
		{
			PlayerLimitedPet[Player.GetPID()].Index = 0;
			PlayerLimitedPet[Player.GetPID()].IID = 0;
		}

		return CItemOrnament::FreeSpec(Itemx,Playerx);
	} else {
		return 0;
	}
}

int __fastcall OrnamentApplySpec(void *Itemx, void *edx, int Playerx)
{
	IItem Item(Itemx);
	IChar Player((void*)Playerx);

	if (Player.IsOnline())
	{
		int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
		MainSvrT::ReadItemTable(Item.GetIID(),GetDSS,GetStat,GetTime,Lock);

		if (Item.CheckIndex() >= 7883 && Item.CheckIndex() <= 7906)
		{
			int Stat = ItemShow::BattleHorseStat(Player.GetOffset(),Item.GetIID(),Item.CheckIndex(),GetStat,false);
			if (Item.CheckIndex() == 7883 || Item.CheckIndex() == 7889 || Item.CheckIndex() == 7895 || Item.CheckIndex() == 7901) BattleHorse[Player.GetPID()].Str = Stat;
			if (Item.CheckIndex() == 7884 || Item.CheckIndex() == 7890 || Item.CheckIndex() == 7896 || Item.CheckIndex() == 7902) BattleHorse[Player.GetPID()].Int = Stat;
			if (Item.CheckIndex() == 7885 || Item.CheckIndex() == 7891 || Item.CheckIndex() == 7897 || Item.CheckIndex() == 7903) BattleHorse[Player.GetPID()].Def = Stat;
			if (Item.CheckIndex() == 7886 || Item.CheckIndex() == 7892 || Item.CheckIndex() == 7898 || Item.CheckIndex() == 7904) BattleHorse[Player.GetPID()].Hp = Stat;
			if (Item.CheckIndex() == 7887 || Item.CheckIndex() == 7893 || Item.CheckIndex() == 7899 || Item.CheckIndex() == 7905) BattleHorse[Player.GetPID()].Abs = Stat;
			if (Item.CheckIndex() == 7888 || Item.CheckIndex() == 7894 || Item.CheckIndex() == 7900 || Item.CheckIndex() == 7906) BattleHorse[Player.GetPID()].Eva = Stat;

			if (BattleHorse.find(Player.GetPID())->second.Active)
			{
				if (Item.CheckIndex() == 7883 || Item.CheckIndex() == 7889 || Item.CheckIndex() == 7895 || Item.CheckIndex() == 7901) Player.AddStr(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7884 || Item.CheckIndex() == 7890 || Item.CheckIndex() == 7896 || Item.CheckIndex() == 7902) Player.AddInt(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7885 || Item.CheckIndex() == 7891 || Item.CheckIndex() == 7897 || Item.CheckIndex() == 7903) Player.AddDef(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7886 || Item.CheckIndex() == 7892 || Item.CheckIndex() == 7898 || Item.CheckIndex() == 7904) Player.AddHp(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7887 || Item.CheckIndex() == 7893 || Item.CheckIndex() == 7899 || Item.CheckIndex() == 7905) Player.AddAbsorb(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
				if (Item.CheckIndex() == 7888 || Item.CheckIndex() == 7894 || Item.CheckIndex() == 7900 || Item.CheckIndex() == 7906) Player.AddEva(Stat*BattleHorse.find(Player.GetPID())->second.Mul);
			}
		}

		if ((Item.CheckIndex() >= 7700 && Item.CheckIndex() <= 7707) || (Item.CheckIndex() >= 7719 && Item.CheckIndex() <= 7722))
		{
			int FStat = 0, SStat = 0; MainSvrT::ReadMonsterPet(Item.GetIID(),FStat,SStat);
			int Str = Player.GetStr(), Wis = Player.GetWis(), Hth = Player.GetHth(), Int = Player.GetInt(), Agi = Player.GetAgi();
			int Otp = (15*Str/54), MinPAtk = ((11*Str)-80)/30, MaxPAtk = ((8*Str)-25)/15, Eva = Agi/3, HpPoint = ((2*Hth*Hth)/100);
			int MpPoint = ((2*Wis*Wis)/100), MinMAtk = (((7*Int)-20)/12)+(Wis/7), MaxMAtk = ((7*Int)/12)+(14*Wis/45);

			if (FStat)
			{
				int Type = FStat >> 16; int Stat = FStat & 0xFF;
				if (Type == 0) Player.AddStr((Str*Stat)/100);
				if (Type == 1) Player.AddWis((Wis*Stat)/100);
				if (Type == 2) Player.AddInt((Int*Stat)/100);
				if (Type == 3) Player.AddHp((Hth*Stat)/100);
				if (Type == 4) Player.AddAgi((Agi*Stat)/100);
				if (Type == 5) Player.AddOTP(Stat);
				if (Type == 6) Player.AddEva(Stat);
				if (Type == 7) Player.AddDef(Stat);
				if (Type == 9) Player.AddAbsorb(Stat);
				if (Type == 10) Player.IncreaseMaxHp(HpPoint*Stat);
				if (Type == 11) Player.IncreaseMaxMp(MpPoint*Stat);
				if (Type == 13) Player.AddPhyAttack((MinPAtk*Stat)/4);
				if (Type == 14) Player.AddPhyAttack((MaxPAtk*Stat)/4);
				if (Type == 15) Player.IncreaseCritRate(Stat);
				if (Type == 16) Player.IncreaseCritDamage(Stat);
				if (Type == 17) Player.IncreaseEBRate(1);
				if (Type == 18) Player.AddMagAttack((MinMAtk*Stat)/4);
				if (Type == 19) Player.AddMagAttack((MaxMAtk*Stat)/4);
			}

			if (SStat)
			{
				int Type = SStat >> 16; int Stat = SStat & 0xFF;
				if (Type == 0) Player.AddStr((Str*Stat)/100);
				if (Type == 1) Player.AddWis((Wis*Stat)/100);
				if (Type == 2) Player.AddInt((Int*Stat)/100);
				if (Type == 3) Player.AddHp((Hth*Stat)/100);
				if (Type == 4) Player.AddAgi((Agi*Stat)/100);
				if (Type == 5) Player.AddOTP(Stat);
				if (Type == 6) Player.AddEva(Stat);
				if (Type == 7) Player.AddDef(Stat);
				if (Type == 9) Player.AddAbsorb(Stat);
				if (Type == 10) Player.IncreaseMaxHp(HpPoint*Stat);
				if (Type == 11) Player.IncreaseMaxMp(MpPoint*Stat);
				if (Type == 13) Player.AddPhyAttack((MinPAtk*Stat)/4);
				if (Type == 14) Player.AddPhyAttack((MaxPAtk*Stat)/4);
				if (Type == 15) Player.IncreaseCritRate(Stat);
				if (Type == 16) Player.IncreaseCritDamage(Stat);
				if (Type == 17) Player.IncreaseEBRate(1);
				if (Type == 18) Player.AddMagAttack((MinMAtk*Stat)/4);
				if (Type == 19) Player.AddMagAttack((MaxMAtk*Stat)/4);
			}
		}

		if (Item.CheckIndex() >= 3381 && Item.CheckIndex() <= 3383 && !CheckHaninMirror.find(Player.GetPID())->second)
			CheckHaninMirror[Player.GetPID()] = Item.GetIID();

		if (Item.CheckIndex() >= 3384 && Item.CheckIndex() <= 3386 && CheckHaninMirror.find(Player.GetPID())->second)
		{
			int Recheck = 0, Check = 0, Value = 0, Itemy = 0;
			Undefined::CreateMonsterValue((char*)Playerx + 1068, (int)&Value, (int)&CheckHaninMirror.find(Player.GetPID())->second);
			Check = Undefined::Check((int)((char*)Playerx + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return 0;
			Itemy = *(DWORD*)(Undefined::GetValue(&Value) + 4);
			IItem Mirror((void*)Itemy);
			int Valuex = 0, Type = 0; double Stat = 0;
			Valuex = GetStat;
			Type = Valuex % 100;
			Stat = Valuex / 10000;
			int EssenceStat = Player.GetBuffValue(418);
			EssenceStat = EssenceStat | (1 << Type);
			Player.Buff(418,1296000,EssenceStat);
			int Str = Player.GetStr(), Wis = Player.GetWis(), Hth = Player.GetHth(), Int = Player.GetInt(), Agi = Player.GetAgi();
			int Otp = (15*Str/54), MinPAtk = ((11*Str)-80)/30, MaxPAtk = ((8*Str)-25)/15, Eva = Agi/3, HpPoint = ((2*Hth*Hth)/100);
			int MpPoint = ((2*Wis*Wis)/100), MinMAtk = (((7*Int)-20)/12)+(Wis/7), MaxMAtk = ((7*Int)/12)+(14*Wis/45);

			if (Stat && Mirror.CheckIndex() == 3381)
			{
				if (Type == 0) Player.AddStr(((Str*Stat)/100)+0.5);
				if (Type == 1) Player.AddWis(((Wis*Stat)/100)+0.5);
				if (Type == 2) Player.AddInt(((Int*Stat)/100)+0.5);
				if (Type == 3) Player.AddHp(((Hth*Stat)/100)+0.5);
				if (Type == 4) Player.AddAgi(((Agi*Stat)/100)+0.5);
				if (Type == 5) Player.AddOTP(Stat);
				if (Type == 6) Player.AddEva(Stat);
				if (Type == 7) Player.AddDef(Stat);
				if (Type == 9) Player.AddAbsorb(Stat/2);
				if (Type == 10) Player.IncreaseMaxHp(HpPoint*Stat);
				if (Type == 11) Player.IncreaseMaxMp(MpPoint*Stat);
				if (Type == 12) Player.IncreaseMovingSpeed((3*Stat)+0.5);
				if (Type == 13) Player.AddPhyAttack(MinPAtk*(Stat/5));
				if (Type == 14) Player.AddPhyAttack(MaxPAtk*(Stat/5));
				if (Type == 15) Player.IncreaseCritRate(Stat);
				if (Type == 16) Player.IncreaseCritDamage(Stat);
				if (Type == 17) Player.IncreaseEBRate(1);
				if (Type == 18) Player.AddMagAttack(MinMAtk*(Stat/5));
				if (Type == 19) Player.AddMagAttack(MaxMAtk*(Stat/5));
				if (Type == 20) Player.Buff(298,1296000,Stat);
				if (Type == 21) Player.Buff(330,1296000,Stat);
			}

			if (Stat && Mirror.CheckIndex() == 3382)
			{
				if (Type == 0) Player.AddStr(((Str*Stat)/90)+0.5);
				if (Type == 1) Player.AddWis(((Wis*Stat)/90)+0.5);
				if (Type == 2) Player.AddInt(((Int*Stat)/90)+0.5);
				if (Type == 3) Player.AddHp(((Hth*Stat)/90)+0.5);
				if (Type == 4) Player.AddAgi(((Agi*Stat)/90)+0.5);
				if (Type == 5) Player.AddOTP(Stat+1);
				if (Type == 6) Player.AddEva(Stat+1);
				if (Type == 7) Player.AddDef(Stat+1);
				if (Type == 9) Player.AddAbsorb((Stat/2));
				if (Type == 10) Player.IncreaseMaxHp(HpPoint*(Stat+1));
				if (Type == 11) Player.IncreaseMaxMp(MpPoint*(Stat+1));
				if (Type == 12) Player.IncreaseMovingSpeed((3*Stat)+1.5);
				if (Type == 13) Player.AddPhyAttack(MinPAtk*(Stat/5));
				if (Type == 14) Player.AddPhyAttack(MaxPAtk*(Stat/5));
				if (Type == 15) Player.IncreaseCritRate(Stat+1);
				if (Type == 16) Player.IncreaseCritDamage(Stat+1);
				if (Type == 17) Player.IncreaseEBRate(1);
				if (Type == 18) Player.AddMagAttack(MinMAtk*(Stat/5));
				if (Type == 19) Player.AddMagAttack(MaxMAtk*(Stat/5));
				if (Type == 20) Player.Buff(298,1296000,Stat+1);
				if (Type == 21) Player.Buff(330,1296000,Stat+1);
			}

			if (Stat && Mirror.CheckIndex() == 3383)
			{
				if (Type == 0) Player.AddStr(((Str*Stat)/80)+0.5);
				if (Type == 1) Player.AddWis(((Wis*Stat)/80)+0.5);
				if (Type == 2) Player.AddInt(((Int*Stat)/80)+0.5);
				if (Type == 3) Player.AddHp(((Hth*Stat)/80)+0.5);
				if (Type == 4) Player.AddAgi(((Agi*Stat)/80)+0.5);
				if (Type == 5) Player.AddOTP(Stat+2);
				if (Type == 6) Player.AddEva(Stat+2);
				if (Type == 7) Player.AddDef(Stat+2);
				if (Type == 9) Player.AddAbsorb(Stat/2);
				if (Type == 10) Player.IncreaseMaxHp(HpPoint*(Stat+2));
				if (Type == 11) Player.IncreaseMaxMp(MpPoint*(Stat+2));
				if (Type == 12) Player.IncreaseMovingSpeed((3*Stat)+2.5);
				if (Type == 13) Player.AddPhyAttack(MinPAtk*(Stat/5));
				if (Type == 14) Player.AddPhyAttack(MaxPAtk*(Stat/5));
				if (Type == 15) Player.IncreaseCritRate(Stat+2);
				if (Type == 16) Player.IncreaseCritDamage(Stat+2);
				if (Type == 17) Player.IncreaseEBRate(1);
				if (Type == 18) Player.AddMagAttack(MinMAtk*(Stat/5));
				if (Type == 19) Player.AddMagAttack(MaxMAtk*(Stat/5));
				if (Type == 20) Player.Buff(298,1296000,Stat+2);
				if (Type == 21) Player.Buff(330,1296000,Stat+2);
			}
		}

		if (Item.CheckIndex() >= 3018 && Item.CheckIndex() <= 3020 && !Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second)
		{
			int GetTaegeukPrefix = GetStat / 1000;
			if (GetTaegeukPrefix) Taegeuk[*(DWORD*)((int)Itemx + 32)] = GetTaegeukPrefix;

			if (Player.IsBuff(311) && Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second)
			{
				Player.IncreaseMaxHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][0]);
				Player.IncreaseMaxMp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][1]);
				Player.AddMagAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.AddPhyAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.AddStr(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][3]);
				Player.AddAgi(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][4]);
				Player.AddInt(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][5]);
				Player.AddWis(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][6]);
				Player.AddHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][7]);
			}
		}

		if (Item.CheckIndex() >= 2946 && Item.CheckIndex() <= 2948 && TrigramHP.find(*(DWORD*)((int)Itemx + 32))->second
			&& TrigramMP.find(*(DWORD*)((int)Itemx + 32))->second && TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second
			&& TrigramStr.find(*(DWORD*)((int)Itemx + 32))->second && TrigramAgi.find(*(DWORD*)((int)Itemx + 32))->second
			&& TrigramInt.find(*(DWORD*)((int)Itemx + 32))->second && TrigramWis.find(*(DWORD*)((int)Itemx + 32))->second
			&& TrigramHth.find(*(DWORD*)((int)Itemx + 32))->second)
		{
			int YinYangType = 0;
			if (Item.CheckIndex() == 2947) YinYangType = 1;
			if (Item.CheckIndex() == 2948) YinYangType = 2;
			Player.Buff(311,604800,0);
			int YinYangGrade = TrigramGrade.find(*(DWORD*)((int)Itemx + 32))->second;
			Player.IncreaseMaxHp(TrigramHP.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramHP.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.IncreaseMaxMp(TrigramMP.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramMP.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddMagAttack(TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddPhyAttack(TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramAtk.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddStr(TrigramStr.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramStr.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddAgi(TrigramAgi.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramAgi.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddInt(TrigramInt.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramInt.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddWis(TrigramWis.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramWis.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));
			Player.AddHp(TrigramHth.find(*(DWORD*)((int)Itemx + 32))->second + ((TrigramHth.find(*(DWORD*)((int)Itemx + 32))->second * YinYangRate[YinYangType][YinYangGrade]) / 100));

			if (Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second)
			{
				Player.IncreaseMaxHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][0]);
				Player.IncreaseMaxMp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][1]);
				Player.AddMagAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.AddPhyAttack(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][2]);
				Player.AddStr(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][3]);
				Player.AddAgi(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][4]);
				Player.AddInt(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][5]);
				Player.AddWis(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][6]);
				Player.AddHp(TaegeukPrefix[(Taegeuk.find(*(DWORD*)((int)Itemx + 32))->second - 1)][7]);
			}
		}

		if (Item.CheckIndex() >= 2986 && Item.CheckIndex() <= 3009)
		{
			int GetCurrentGrade = (GetStat % 10000) / 100;

			if (TrigramGrade.count(*(DWORD*)((int)Itemx + 32)) && GetCurrentGrade < TrigramGrade.find(*(DWORD*)((int)Itemx + 32))->second)
				TrigramGrade[*(DWORD*)((int)Itemx + 32)] = GetCurrentGrade;

			if (!TrigramGrade.count(*(DWORD*)((int)Itemx + 32)))
				TrigramGrade[*(DWORD*)((int)Itemx + 32)] = GetCurrentGrade;

			if (Item.CheckIndex() == 2986) TrigramHP[*(DWORD*)((int)Itemx + 32)] = TriagramStats[0][GetCurrentGrade];
			if (Item.CheckIndex() == 2994) TrigramHP[*(DWORD*)((int)Itemx + 32)] = TriagramStats[1][GetCurrentGrade];
			if (Item.CheckIndex() == 3002) TrigramHP[*(DWORD*)((int)Itemx + 32)] = TriagramStats[2][GetCurrentGrade];
			if (Item.CheckIndex() == 2987) TrigramMP[*(DWORD*)((int)Itemx + 32)] = TriagramStats[3][GetCurrentGrade];
			if (Item.CheckIndex() == 2995) TrigramMP[*(DWORD*)((int)Itemx + 32)] = TriagramStats[4][GetCurrentGrade];
			if (Item.CheckIndex() == 3003) TrigramMP[*(DWORD*)((int)Itemx + 32)] = TriagramStats[5][GetCurrentGrade];
			if (Item.CheckIndex() == 2988) TrigramAtk[*(DWORD*)((int)Itemx + 32)] = TriagramStats[6][GetCurrentGrade];
			if (Item.CheckIndex() == 2996) TrigramAtk[*(DWORD*)((int)Itemx + 32)] = TriagramStats[7][GetCurrentGrade];
			if (Item.CheckIndex() == 3004) TrigramAtk[*(DWORD*)((int)Itemx + 32)] = TriagramStats[8][GetCurrentGrade];
			if (Item.CheckIndex() == 2989) TrigramStr[*(DWORD*)((int)Itemx + 32)] = TriagramStats[9][GetCurrentGrade];
			if (Item.CheckIndex() == 2997) TrigramStr[*(DWORD*)((int)Itemx + 32)] = TriagramStats[10][GetCurrentGrade];
			if (Item.CheckIndex() == 3005) TrigramStr[*(DWORD*)((int)Itemx + 32)] = TriagramStats[11][GetCurrentGrade];
			if (Item.CheckIndex() == 2990) TrigramAgi[*(DWORD*)((int)Itemx + 32)] = TriagramStats[12][GetCurrentGrade];
			if (Item.CheckIndex() == 2998) TrigramAgi[*(DWORD*)((int)Itemx + 32)] = TriagramStats[13][GetCurrentGrade];
			if (Item.CheckIndex() == 3006) TrigramAgi[*(DWORD*)((int)Itemx + 32)] = TriagramStats[14][GetCurrentGrade];
			if (Item.CheckIndex() == 2991) TrigramInt[*(DWORD*)((int)Itemx + 32)] = TriagramStats[15][GetCurrentGrade];
			if (Item.CheckIndex() == 2999) TrigramInt[*(DWORD*)((int)Itemx + 32)] = TriagramStats[16][GetCurrentGrade];
			if (Item.CheckIndex() == 3007) TrigramInt[*(DWORD*)((int)Itemx + 32)] = TriagramStats[17][GetCurrentGrade];
			if (Item.CheckIndex() == 2992) TrigramWis[*(DWORD*)((int)Itemx + 32)] = TriagramStats[18][GetCurrentGrade];
			if (Item.CheckIndex() == 3000) TrigramWis[*(DWORD*)((int)Itemx + 32)] = TriagramStats[19][GetCurrentGrade];
			if (Item.CheckIndex() == 3008) TrigramWis[*(DWORD*)((int)Itemx + 32)] = TriagramStats[20][GetCurrentGrade];
			if (Item.CheckIndex() == 2993) TrigramHth[*(DWORD*)((int)Itemx + 32)] = TriagramStats[21][GetCurrentGrade];
			if (Item.CheckIndex() == 3001) TrigramHth[*(DWORD*)((int)Itemx + 32)] = TriagramStats[22][GetCurrentGrade];
			if (Item.CheckIndex() == 3009) TrigramHth[*(DWORD*)((int)Itemx + 32)] = TriagramStats[23][GetCurrentGrade];
		}

		if (PetTime.count(Item.CheckIndex()) || (Item.CheckIndex() >= 1747 && Item.CheckIndex() <= 1762) || (Item.CheckIndex() >= 1870 && Item.CheckIndex() <= 1877) || (Item.CheckIndex() >= 2004 && Item.CheckIndex() <= 2007) || (Item.CheckIndex() >= 2421 && Item.CheckIndex() <= 2423) || (Item.CheckIndex() >= 2550 && Item.CheckIndex() <= 2653) || (Item.CheckIndex() >= 6045 && Item.CheckIndex() <= 6052))
		{
			if ((Item.CheckIndex() >= 2004 && Item.CheckIndex() <= 2007) || (Item.CheckIndex() >= 2422 && Item.CheckIndex() <= 2423) || (Item.CheckIndex() >= 4101 && Item.CheckIndex() <= 4102) || (Item.CheckIndex() >= 7124 && Item.CheckIndex() <= 7125))
				CPlayer::Write(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),2);
			else
				CPlayer::Write(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),0);

			PlayerPet[Player.GetPID()].Owner = Item.CheckIndex();
			PlayerPet[Player.GetPID()].IID = Item.GetIID();
			CPlayer::Write(Player.GetOffset(),0xFF,"dd",221,PetTime.find(Item.CheckIndex())->second.Pick);
		}

		if (MonsterPet.count(Item.CheckIndex()))
		{
			PlayerMonsterPet[Player.GetPID()].Index = Item.CheckIndex();
			PlayerMonsterPet[Player.GetPID()].IID = Item.GetIID();
			CPlayer::Write(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),0);
		}

		if (ConfigLimitedPet.count(Item.CheckIndex()))
		{
			PlayerLimitedPet[Player.GetPID()].Index = Item.CheckIndex();
			PlayerLimitedPet[Player.GetPID()].IID = Item.GetIID();
			CPlayer::Write(Player.GetOffset(),221,"dwdb",Player.GetID(),Item.CheckIndex(),Item.GetIID(),0);
		}

		return CItemOrnament::ApplySpec(Itemx,Playerx);
	} else {
		return 0;
	}
}

signed int __fastcall OrnamentChangePrefix(void *Item, void* _edx, int Player, int ID, int Chance, int Argument)
{
	IChar IPlayer((void*)Player);
	IItem IItem(Item);
	int CurrentPrefix = 0, NewPrefix = 0, AddPrefix = 0;
	if (!IPlayer.IsValid()) return 0;
	if (SuitWearFix.count(IItem.CheckIndex())) return 0;
	if (DecoWearFix.count(IItem.CheckIndex())) return 0;
	if (ConfigWeaponSkins.count(IItem.CheckIndex())) return 0;

	if (IItem.GetInfo() & 4194304)
	{
		IPlayer.BoxMsg("Item is locked.");
		return 0;
	}

	if (CItem::IsState((int)IItem.GetOffset(), 1) || ID >= 0 && ID != 2)
		return 0;

	if (IItem.Prefix())
		CurrentPrefix = IItem.PrefixID();

	NewPrefix = CurrentPrefix;
	int Rate = CTools::Rate(1, 100);

	for (signed int i = 0; i < 2; ++i)
	{
		if (Rate <= (int)*(DWORD*)(Chance + 4 * i))
		{
			NewPrefix = *(DWORD *)(Argument + 4 * i);
			break;
		}
	}

	if (CurrentPrefix == NewPrefix)
	{
		CPlayer::Write(IPlayer.GetOffset(), 67, "b", 54);
	} else {
		if (WeaponReplace.count((IItem.CheckIndex() * 1000000) + (NewPrefix * 1000)))
		{
			if (WeaponReplace.find((IItem.CheckIndex() * 1000000) + (NewPrefix * 1000))->second)
			{
				if (CBase::IsDeleted((int)Item)) return 0;

				if (IPlayer.IsBuff(328))
					return 0;
				else
					IPlayer.Buff(328,3,0);

				int ReplacePrefix = 0, ReplaceItem = 0, ReplaceInfo = 0;

				if (*(DWORD *)((int)Item + 44))
					ReplacePrefix = *(DWORD *)(*(DWORD *)((int)Item + 44) + 32);

				if (*(DWORD *)((int)Item + 48))
					ReplaceInfo = *(DWORD *)((int)Item + 48);

				int ItemNewIndex = IItem.CheckIndex();
				int DeleteCheck = (*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);

				if (!DeleteCheck)
				{
					ReplaceItem = CItem::CreateItem(WeaponReplace.find((ItemNewIndex * 1000000) + (NewPrefix * 1000))->second, ReplacePrefix, 1, -1);

					if (ReplaceItem)
					{
						CIOObject::AddRef(ReplaceItem);
						*(DWORD *)(ReplaceItem + 48) = ReplaceInfo;

						if ( CPlayer::_InsertItem(IPlayer.GetOffset(), 27, ReplaceItem) != 1 )
						{
							CConsole::Red("Real time ornament insert item Null error [PID (%d)] ", IPlayer.GetPID());
							CBase::Delete((void *)ReplaceItem);
							CIOCriticalSection::Leave((void*)((char *)IPlayer.GetOffset() + 1020));
							return 0;
						}

						CIOObject::Release((void *)ReplaceItem);
						CDBSocket::Write(21, "dddbb",*(DWORD *)(ReplaceItem + 36),*(DWORD *)(ReplaceItem + 32),*(DWORD *)(ReplaceItem + 48),8,7);
						CPlayer::Write(IPlayer.GetOffset(), 0xFF, "ddddd", 242, 0, 0, 128, 255);
					}
				} else {
					return 0;
				}

				return 1;
			}
		}

		if (WeaponReplace.count((IItem.CheckIndex() * 1000000) + (NewPrefix * 1000)))
		{
			IPlayer.BoxMsg("Enchanting item and the talisman are different.");
			return 0;
		}

		if (CItemReplace.count(NewPrefix))
		{
			IPlayer.BoxMsg("Enchanting item and the talisman are different.");
			return 0;
		}

		AddPrefix = CItem::FindPrefix(NewPrefix);

		if ( !AddPrefix )
			return 0;

		*(DWORD *)((int)IItem.GetOffset() + 44) = AddPrefix;
		CDBSocket::Write(19,"ddbb",IItem.GetIID(),IPlayer.GetID(),NewPrefix);
		CItem::SendItemInfo(IItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
	}

	return 1;
}

int __fastcall OrnamentSetWearState(int Item, void *edx, int Player)
{
	IItem IItem((void*)Item);
	IChar IPlayer((void*)Player);

	if (IItem.CheckIndex() >= 3381 && IItem.CheckIndex() <= 3383 && IItem.GetInfo() & 1)
		CheckHaninMirror[IPlayer.GetPID()] = IItem.GetIID();

	if (IItem.CheckIndex() >= 2448 && IItem.CheckIndex() <= 2450)
		*(DWORD*)(*(DWORD*)(Item + 40) + 72) = -113;

	if (IItem.CheckIndex() >= 2461 && IItem.CheckIndex() <= 2463)
		*(DWORD*)(*(DWORD*)(Item + 40) + 72) = -113;

	if (IItem.CheckIndex() >= 3384 && IItem.CheckIndex() <= 3386 && IItem.GetInfo() & 1)
	{
		CPlayer::Write(IPlayer.GetOffset(),5,"ddw",IPlayer.GetID(),IItem.GetIID(),IItem.CheckIndex());
		if (MainSvrT::CPlayerIsWState(Player,0,-96)) *(DWORD*)(*(DWORD*)(Item + 40) + 72) = -95;
		if (MainSvrT::CPlayerIsWState(Player,0,-97)) *(DWORD*)(*(DWORD*)(Item + 40) + 72) = -96;
		if (MainSvrT::CPlayerIsWState(Player,0,-98)) *(DWORD*)(*(DWORD*)(Item + 40) + 72) = -97;
		if (MainSvrT::CPlayerIsWState(Player,0,-99)) *(DWORD*)(*(DWORD*)(Item + 40) + 72) = -98;
		MainSvrT::CPlayerAddWState(Player,0,*(DWORD*)(*(DWORD*)(Item + 40) + 72) - 2);
		return (*(int (__thiscall**)(DWORD, DWORD))(*(DWORD*)Item + 104))(Item, Player);
	}

	return CItemOrnament::SetWearState(Item,Player);
}

int __fastcall WeaponSetWearState(int Item, void *edx, int Player)
{
	IItem IItem((void*)Item); IChar IPlayer((void*)Player);

	if (IPlayer.IsOnline() && CItem::IsState(Item,1) && ConfigWeaponSkins.count(IItem.CheckIndex()) && !MainSvrT::CPlayerIsWState(Player,0,-116))
	{
		MainSvrT::CPlayerAddWState(Player,0,-116);
		return (*(int (__thiscall**)(DWORD, DWORD))(*(DWORD*)Item + 104))(Item, Player);
	}

	return CItemWeapon::SetWearState(Item,Player);
}

int __fastcall ItemOnTimer(int Item, void *edx, int Argument)
{
	IItem IItem((void*)Item); int Player = 0;
	if (!CBase::IsDeleted(Item) && ItemLifeCheck.count(IItem.GetIID())) Player = CPlayer::ScanPlayer(*(DWORD*)(Item + 32));

	if (Player && !CBase::IsDeleted(Item) && PetTime.count(IItem.CheckIndex()))
	{
		IChar IPlayer((void*)Player);

		if (!CBase::IsDeleted(Item) && IPlayer.IsOnline() && ItemLifeCheck.count(IItem.GetIID()) && (int)time(0) + 3 >= ItemLifeCheck.find(IItem.GetIID())->second)
		{
			*(DWORD*)(Item + 68) = 0;
			if (CItem::IsState(Item,1)) OrnamentPutOff((void*)Item,0,(int)IPlayer.GetOffset());
			ItemLifeCheck.erase(IItem.GetIID());
			MainSvrT::DeleteItemTable(IItem.GetIID());
			CItem::RemoveItem(IPlayer.GetOffset(),Item);	
			IPlayer.BoxMsg("Your pet is vanished since the usage period has been expired.");
			if (Player) CSkill::ObjectRelease((void*)Player, Player + 352);
			return 0;
		}
	}

	if (Player && !CBase::IsDeleted(Item) && RidingLimit.count(IItem.CheckIndex()))
	{
		IChar IPlayer((void*)Player);

		if (!CBase::IsDeleted(Item) && IPlayer.IsOnline() && ItemLifeCheck.count(IItem.GetIID()) && (int)time(0) + 3 >= ItemLifeCheck.find(IItem.GetIID())->second)
		{	
			if (IPlayer.IsBuff(349))
			{
				IPlayer.Buff(313,3,0);
				IPlayer.DisableRiding();
			}

			*(DWORD*)(Item + 68) = 0;
			if (CItem::IsState(Item,1)) OrnamentPutOff((void*)Item,0,(int)IPlayer.GetOffset());
			ItemLifeCheck.erase(IItem.GetIID());
			MainSvrT::DeleteItemTable(IItem.GetIID());
			CItem::RemoveItem(IPlayer.GetOffset(),Item);
			IPlayer.BoxMsg("Your riding is vanished since the usage period has been expired.");
			if (Player) CSkill::ObjectRelease((void*)Player, Player + 352);
			return 0;
		}
	}

	if (Player && !CBase::IsDeleted(Item))
	{
		IChar IPlayer((void*)Player);

		if (!CBase::IsDeleted(Item) && IPlayer.IsOnline() && ItemLifeCheck.count(IItem.GetIID()) && (int)time(0) >= ItemLifeCheck.find(IItem.GetIID())->second)
		{
			*(DWORD*)(Item + 68) = 0;
			if (CItem::IsState(Item,1)) OrnamentPutOff((void*)Item,0,(int)IPlayer.GetOffset());
			ItemLifeCheck.erase(IItem.GetIID());
			MainSvrT::DeleteItemTable(IItem.GetIID());
			CItem::RemoveItem(IPlayer.GetOffset(),Item);
			IPlayer.BoxMsg("Your item is vanished since the usage period has been expired.");
			if (Player) CSkill::ObjectRelease((void*)Player, Player + 352);
			return 0;
		}
	}

	if (Player) CSkill::ObjectRelease((void*)Player, Player + 352);
	return CItem::OnTimer(Item,Argument);
}