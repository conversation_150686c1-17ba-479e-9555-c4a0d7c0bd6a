namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		void *Target;
		int SkillGrade;
		int Count;
	} KnightSD;

	void __cdecl ContinueSwordDance(void *Pack)
	{
		MainSvrT::KnightSD *CKSD = (MainSvrT::KnightSD*)Pack;
		IChar IPlayer(CKSD->Player);

		if (IPlayer.IsValid())
		{
			int nSkillGrade = CKSD->SkillGrade;
			void *pTarget = CKSD->Target; IChar Target(pTarget);

			if (nSkillGrade && pTarget && IPlayer.IsValid())
			{
				for (int i = 0; i < CKSD->Count; i++)
				{
					if (IPlayer.IsValid() && Target.IsValid() && ContinueSkills.count(IPlayer.GetPID()) && ContinueSkills.find(IPlayer.GetPID())->second == 43)
					{
						if (!IPlayer.IsValid()) break; if (!Target.IsValid()) break;
						if (CChar::IsGState((int)Target.GetOffset(),1)) break;
						if (IPlayer.IsOnline() && !IPlayer.IsInRange(Target, 2)) break;

						if (IPlayer.CheckHit(Target, 30))
						{
							int nDmg = (IPlayer.GetAttack() * KSDMul) + (nSkillGrade * CTools::Rate(KSDMin,KSDMax));
							if (Target.GetType() == 0) nDmg = nDmg * KSDReduce / 100;
							IPlayer.OktayDamageSingle(Target,nDmg,43);
						} else {
							IPlayer._ShowBattleMiss(Target,43);
						}
					} else {
						break;
					}

					Sleep(800);
				}
			}
		}

		free(CKSD);
	}

	void __fastcall SwordDance(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(43);

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nTargetID = 0, nMana = 0, nSkillGrade = 0; char bType = 0; void *pTarget = 0;
			nSkillGrade = xSkill.GetGrade();
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);

			if (nSkillGrade <= 1)
				nMana = 245;
			else if (nSkillGrade == 2)
				nMana = 283;
			else if (nSkillGrade == 3)
				nMana = 325;
			else if (nSkillGrade == 4)
				nMana = 371;
			else if (nSkillGrade >= 5)
				nMana = 420;

			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (nSkillGrade && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < nMana)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						IPlayer.DecreaseMana(nMana);
						MainSvrT::KnightSD *SDContinue;
						SDContinue = (MainSvrT::KnightSD*)malloc(sizeof(MainSvrT::KnightSD));
						SDContinue->Player = IPlayer.GetOffset();
						SDContinue->Target = Target.GetOffset();
						SDContinue->SkillGrade = nSkillGrade;
						SDContinue->Count = nSkillGrade + 1;
						_beginthread(MainSvrT::ContinueSwordDance,0,(void*)SDContinue);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}