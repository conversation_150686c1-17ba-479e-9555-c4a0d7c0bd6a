namespace MainSvrT
{
	int __fastcall WarEnd(void *Value, void *edx)
	{
		int Castle = CCastle::GetCastle(1), CastleOwner = 0;
		if (Castle) CastleOwner = *((DWORD*)Castle + 8);

		if (CastleOwner)
		{
			CIOCriticalSection::Enter((void*)0x4e2078);
			CIOCriticalSection::Enter((void*)0x4e2098);
			CLink::MoveTo((void*)0x4e200c,(int)0x4e2004);
			CIOCriticalSection::Leave((void*)0x4e2098);
			for ( DWORD i = *(DWORD*)0x4E2004; i != 0x4E2004; i = *(DWORD*)i )
			{
				IChar IWar((void*)(i - 428));

				if (IWar.IsOnline() && IWar.GetGID() && IWar.IsBuff(377))
				{
					if (IWar.GetGID() == CastleOwner && IWar.GetPID() == CastleOwner) CItem::InsertItem((int)IWar.GetOffset(),27,CWOII,0,CWOIA,-1);

					if (IWar.GetGID() == CastleOwner)
					{
						CItem::InsertItem((int)IWar.GetOffset(),27,CWWII,0,CWWIA,-1);
						CheckHonor[IWar.GetPID()].RPx += CWWRP;
						CheckHonor[IWar.GetPID()].HPx += CWWHP;
					}

					if (IWar.GetAlliance() == CastleOwner && IWar.GetGID() != CastleOwner)
					{
						CItem::InsertItem((int)IWar.GetOffset(),27,CWWII,0,CWWIA,-1);
						CheckHonor[IWar.GetPID()].RPx += CWWRP;
						CheckHonor[IWar.GetPID()].HPx += CWWHP;
					}

					if (IWar.GetGID() != CastleOwner && IWar.GetAlliance() != CastleOwner)
					{
						CItem::InsertItem((int)IWar.GetOffset(),27,CWLII,0,CWLIA,-1);
						CheckHonor[IWar.GetPID()].RPx += CWLRP;
						CheckHonor[IWar.GetPID()].HPx += CWLHP;
					}
				}
			}
			CIOCriticalSection::Leave((void*)0x4e2078);
		}

		return CCastle::WarEnd(Value);
	}

	void MyWarEnd()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x0041BDF4,MainSvrT::WarEnd);
	}
}