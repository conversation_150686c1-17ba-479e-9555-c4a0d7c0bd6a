namespace MainSvrT
{
	int __fastcall CPlayerAddWState(int Player, void *edx, int Value)
	{
		IChar IPlayer((void*)Player);
		if (IPlayer.IsOnline()) MainSvrT::UpdateWearState(IPlayer.GetPID(),Value,1);
		return 1;
	}

	int __fastcall CPlayerSubWState(int Player, void *edx, int Value)
	{
		IChar IPlayer((void*)Player);
		if (IPlayer.IsOnline()) MainSvrT::UpdateWearState(IPlayer.GetPID(),Value,0);
		return 1;
	}

	int __fastcall CPlayerIsWState(int Player, void *edx, int Value)
	{
		IChar IPlayer((void*)Player);
		if (IPlayer.IsOnline()) return MainSvrT::GetWearState(IPlayer.GetPID(),Value);
		return 0;
	}
}