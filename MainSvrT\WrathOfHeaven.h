namespace MainSvrT
{
	void __fastcall WrathOfHeaven(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(33);

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			int nMana = (IPlayer.GetLevel() + nSkillGrade) * 3 + 50;
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (nSkillGrade && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < nMana)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						if (IPlayer.CheckHit(Target, 30))
						{
							int nDmg = (IPlayer.GetAttack() * TWOHMul) + (nSkillGrade * CTools::Rate(TWOHMin,TWOHMax));
							if (Target.GetType() == 0) nDmg = nDmg * TWOHReduce / 100;
							IPlayer.OktayDamageSingle(Target,nDmg,33);
						} else {
							IPlayer._ShowBattleMiss(Target, 33);
						}

						IPlayer.SetDirection(Target);
						IPlayer.DecreaseMana(nMana);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}