namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		void *Target;
		int Count;
	} EggZP;

	void __cdecl ContinueZilPoong(void *Pack)
	{
		MainSvrT::EggZP *CEZP = (MainSvrT::EggZP*)Pack;
		IChar IPlayer(CEZP->Player);

		if (IPlayer.IsValid() && CChar::IsGState((int)IPlayer.GetOffset(),512))
		{
			void *pTarget = CEZP->Target; IChar Target(pTarget);

			if (pTarget && CEZP->Count)
			{
				if (IPlayer.IsValid() && Target.IsValid())
				{
					for (int i = 0; i < CEZP->Count; i++)
					{
						if (!IPlayer.IsValid() || !Target.IsValid()) break;
						if (!IPlayer.IsInRange(Target, 2)) break;
						if (!ContinueSkills.count(IPlayer.GetPID()) || ContinueSkills.find(IPlayer.GetPID())->second != 111) break;

						if (IPlayer.CheckHit(Target, 20))
						{
							int nDmg = (IPlayer.GetAttack() * TEZilMul);
							if(Target.GetType() == 0) nDmg = nDmg * TEZilReduce / 100;
							IPlayer.OktayDamageSingle(Target,nDmg,6);
						} else {
							IPlayer._ShowBattleMiss(Target, 6);
						}

						Sleep(800);
					}
				}
			}
		}

		free(CEZP);
	}

	void __fastcall ZilPoong(IChar IPlayer, int pPacket, int pPos)
	{
		if (IPlayer.IsValid() && IPlayer.GetRage() >= 4000)
		{
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						IPlayer.DecreaseRage(4000);
						MainSvrT::EggZP *ZPContinue;
						ZPContinue = (MainSvrT::EggZP*)malloc(sizeof(MainSvrT::EggZP));
						ZPContinue->Player = IPlayer.GetOffset();
						ZPContinue->Target = Target.GetOffset();
						ZPContinue->Count = 6;
						IPlayer._ShowBattleAnimation(IPlayer, 111);
						_beginthread(MainSvrT::ContinueZilPoong,0,(void*)ZPContinue);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}