namespace MainSvrT
{
	void __fastcall ShootingSword(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(17);
		if (ControlBlade.find(IPlayer.GetPID())->second < 1) return;

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (nSkillGrade && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < 50)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if(IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						int nDmg = (IPlayer.GetAttack() * STSSMul) + (nSkillGrade * CTools::Rate(STSSMin,STSSMax));
						if (Target.GetType() == 0) nDmg = nDmg * STSSReduce / 100;
						IPlayer.SetDirection(Target);
						IPlayer.DecreaseMana(50);
						ControlBlade.find(IPlayer.GetPID())->second -= 1;
						CChar::WriteInSight(IPlayer.GetOffset(),235,"dbw",IPlayer.GetID(),51,ControlBlade.find(IPlayer.GetPID())->second);
						IPlayer.OktayDamageSingle(Target,nDmg,17);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}