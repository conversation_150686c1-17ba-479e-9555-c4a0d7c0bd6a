namespace MainSvrT
{
	static char *wide_to_utf8(wchar_t *w)
	{
		char *utf8; int len = 0;
		len = WideCharToMultiByte(CP_UTF8,0,w,-1,0,0,0,0);
		if (len <= 0) return 0;
		utf8 = new char[len];
		if (!utf8) return 0;

		if (WideCharToMultiByte(CP_UTF8,0,w,-1,utf8,len,0,0) <= 0)
		{
			delete[] utf8;
			return 0;
		}

		return utf8;
	}

	char *READ_FILE(char *address)
	{
		CComPtr<IXMLHTTPRequest> request;
		HRESULT hr = CoInitialize(NULL);
		if (hr == S_FALSE) return 0;
		hr = request.CoCreateInstance(CLSID_XMLHTTP60);
		if (hr == S_FALSE) return 0;
		hr = request->open(_bstr_t("GET"),_bstr_t(address),_variant_t(VARIANT_FALSE),_variant_t(),_variant_t());
		if (hr == S_FALSE) return 0;
		hr = request->send(_variant_t());
		if (hr == S_FALSE) return 0;
		long status = 0;
		hr = request->get_status(&status);
		if (hr == S_FALSE) return 0;

		if (status == 200)
		{
			wchar_t *data;
			hr = request->get_responseText(&data);
			if (hr == S_FALSE) return 0;
			char *utf = MainSvrT::wide_to_utf8(data);
			CoUninitialize();
			return utf;
		}

		return 0;
	}
}