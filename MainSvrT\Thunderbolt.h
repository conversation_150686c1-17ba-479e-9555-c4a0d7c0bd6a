namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		void *Target;
		int SkillGrade;
		int Count;
	} MageTB;

	void __cdecl ContinueThunderbolt(void *Pack)
	{
		MainSvrT::MageTB *CMTB = (MainSvrT::MageTB*)Pack;
		IChar IPlayer(CMTB->Player);

		if (IPlayer.IsValid())
		{
			void *pTarget = CMTB->Target; IChar Target(pTarget);

			if (pTarget && CMTB->SkillGrade && CMTB->Count && Target.IsValid())
			{
				for (int i = 0; i < CMTB->Count; i++)
				{
					if (!IPlayer.IsInRange(Target, 70)) break;
					if (!IPlayer.IsValid() || !Target.IsValid()) break;
					if (!ContinueSkills.count(IPlayer.GetPID()) || ContinueSkills.find(IPlayer.GetPID())->second != 75) break;
					int nDmg = (IPlayer.GetMagic() * MTBMul) + (CMTB->SkillGrade * CTools::Rate(MTBMin,MTBMax));
					if (Target.GetType() == 0) nDmg = nDmg * MTBReduce / 100;
					IPlayer.OktayDamageSingle(Target,nDmg,75);
					IPlayer.SetDirection(Target);
					Sleep(200);
				}
			}
		}

		free(CMTB);
	}

	void __fastcall Thunderbolt(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(75);

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nTargetID = 0; char bType = 0; void* pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			int nMana = (int)(((((xSkill.GetGrade()-1)+45) * xSkill.GetGrade())+280) * 0.75);
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (xSkill.GetGrade() && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < nMana)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						IPlayer.DecreaseMana(nMana);
						MainSvrT::MageTB *TBContinue;
						TBContinue = (MainSvrT::MageTB*)malloc(sizeof(MainSvrT::MageTB));
						TBContinue->Player = IPlayer.GetOffset();
						TBContinue->Target = Target.GetOffset();
						TBContinue->SkillGrade = xSkill.GetGrade();
						TBContinue->Count = 6;
						_beginthread(MainSvrT::ContinueThunderbolt,0,(void*)TBContinue);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}