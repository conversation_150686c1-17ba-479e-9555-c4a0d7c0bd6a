#include <Windows.h>
#include <sstream>
#include <strstream>
#include <stdlib.h>
#include <direct.h>
#include <io.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include <stdio.h>
#include <stdarg.h>
#include <time.h>
#include <math.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
#include <string>
#include <list>
#include <map>
#include "Lisp.h"
#include "XMacro.h"
#include "Utility.h"
#pragma warning (disable : 4996)

namespace MacroGenVoyager
{
	static AttributeTable g_vAttribute[] = 
	{
		{ "id",		A_ID },
		{ "sell",	A_SELL }
	};
}

MacroGenVoyager::ATTRIBUTE MacroGenVoyager::FindAttribute(const char *pString)
{
	AttributeTable *pTable = equal(&g_vAttribute[0], &g_vAttribute[sizeof(g_vAttribute)/sizeof(AttributeTable)], pString);
	if (pTable != &g_vAttribute[sizeof(g_vAttribute)/sizeof(AttributeTable)])  return pTable->nAttribute;
	return A_NULL;
}

BOOL MacroGenVoyager::CGenVoyagerDB::Set(int attribute, lisp::var param)
{
	switch (attribute)
	{
		case A_ID:
			this->ID = (int)param.pop();
			this->m_dwKey = ID;
			break;
		case A_SELL:
			int a = (int)param.pop();
			int b = (int)param.pop();
			int c = (int)param.pop();
			if (a && b && c)
			{
				this->SELL.push_back(a);
				this->SELL.push_back(b);
				this->SELL.push_back(c);
			}
			break;
	}
	
	return TRUE;
}

namespace MacroGenMonster
{
	static AttributeTable g_vAttribute[] = 
	{
		{ "area",		A_AREA },
		{ "cycle",		A_CYCLE },
		{ "index",		A_INDEX },
		{ "map",		A_MAP },
		{ "max",		A_MAX },
		{ "rect",		A_RECT }
	};
}

MacroGenMonster::ATTRIBUTE MacroGenMonster::FindAttribute(const char *pString)
{
	AttributeTable *pTable = equal(&g_vAttribute[0], &g_vAttribute[sizeof(g_vAttribute)/sizeof(AttributeTable)], pString);
	if (pTable != &g_vAttribute[sizeof(g_vAttribute)/sizeof(AttributeTable)])  return pTable->nAttribute;
	return A_NULL;
}

BOOL MacroGenMonster::GenMonsterDB::Set(int attribute, lisp::var param)
{
	switch (attribute)
	{
		case A_INDEX:
			this->Index = (int)param.pop();
			break;
		case A_MAP:
			this->Map = (int)param.pop();
			break;
		case A_AREA:
			this->Area = (int)param.pop();
			this->m_dwKey = Area;
			break;
		case A_MAX:
			this->Max = (int)param.pop();
			break;
		case A_CYCLE:
			this->Cycle = (int)param.pop();
			break;
		case A_RECT:
			this->X1 = (int)param.pop();
			this->Y1 = (int)param.pop();
			this->X2 = (int)param.pop();
			this->Y2 = (int)param.pop();
			break;
	}
	
	return TRUE;
}