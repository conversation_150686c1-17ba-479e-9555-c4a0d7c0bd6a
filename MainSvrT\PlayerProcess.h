namespace MainSvrT
{
	void __fastcall PlayerProcess(__int32 Player, void *edx, int packet, void *pPacket, int pPos)
	{
		IChar IPlayer((void*)Player);

		if ((packet == 31 || packet == 61) && IPlayer.IsBuff(349)) return;
		if (packet == 137 || packet == 167 || packet == 171 || packet == 177 || packet == 190 || packet == 193 || packet == 203) return;

		if (packet == 204)
		{
			int Type = 0; BYTE Value = 0; CPacket::Read((char*)pPacket,(char*)pPos,"db",&Type,&Value);
			
			if (Type == 7 && Suits.count(IPlayer.GetPID()) && Suits.find(IPlayer.GetPID())->second.Index)
			{
				CChar::WriteInSight(IPlayer.GetOffset(),234,"ddwb",IPlayer.GetID(),Type,Suits.find(IPlayer.GetPID())->second.Index,Value);
				if (Value == 1) Suits[IPlayer.GetPID()].Active = 0;
				if (Value == 0) Suits[IPlayer.GetPID()].Active = 1;
			}

			if (Type == 27 && WSuit.count(IPlayer.GetPID()) && WSuit.find(IPlayer.GetPID())->second.Index)
			{
				CChar::WriteInSight(IPlayer.GetOffset(),234,"ddwb",IPlayer.GetID(),Type,WSuit.find(IPlayer.GetPID())->second.Index,Value);
				if (Value == 1) WSuit[IPlayer.GetPID()].Active = 0;
				if (Value == 0) WSuit[IPlayer.GetPID()].Active = 1;
			}

			if (Type == 38 && Decos.count(IPlayer.GetPID()) && Decos.find(IPlayer.GetPID())->second.Index)
			{
				CChar::WriteInSight(IPlayer.GetOffset(),234,"ddwb",IPlayer.GetID(),Type,Decos.find(IPlayer.GetPID())->second.Index,Value);
				if (Value == 1) Decos[IPlayer.GetPID()].Active = 0;
				if (Value == 0) Decos[IPlayer.GetPID()].Active = 1;
			}

			return;
		}

		if (packet == 46)
		{
			BYTE Type = 0, Exp = 0; CPacket::Read((char*)pPacket,(char*)pPos,"bb",&Type,&Exp);
			
			if (Type == 1)
			{		
				Interface<ITools> Tools; BYTE a = 0, b = 0; char *c;
				Tools->ParseData((char*)pPacket,"bsb",&a,&c,&b);
				if (IPlayer.IsBuff(436) && b == 1) return;

				if (IPlayer.IsOnline())
				{
					int Castle = 0, CastleOwner = 0; Castle = CCastle::GetCastle(1); if (Castle) CastleOwner = *((DWORD*)Castle+8);
					MainSvrT::SetGuildPacket(IPlayer,b,CastleOwner);
				}

				if (IPlayer.GetGID() && b == 1)
				{
					int Guild = CPlayer::GetGuild(IPlayer.GetOffset());

					if (Guild)
					{
						CGuild::SendMemberInfo((void*)Guild, (int)IPlayer.GetOffset(), IPlayer.GetGID());
						CSkill::ObjectRelease((void*)Guild,(LONG)((char*)Guild + 40));
					}
				}

				if (IPlayer.GetPID() == IPlayer.GetGID() && b == 1) MainSvrT::SetJoinRequestPacket(IPlayer.GetGID(),IPlayer.GetOffset());
				IPlayer.Buff(436,3,0);
				return;
			}

			if (Type == 8)
			{
				if (!IPlayer.GetGID())
				{
					if (IPlayer.IsBuff(436)) return;
					BYTE Type = 0; int GID = 0; CPacket::Read((char*)pPacket,(char*)pPos,"bd",&Type,&GID);
					if (GID)
					{
						int Level = 0, Time = 0, xGID = 0, Count = 0, PID = 0;
						MainSvrT::GetJoinRequest(IPlayer.GetName(),Level,Time,xGID,Count,PID);
						if ((Time + 86400) > (int)time(0))
						{
							time_t Left = Time + 259200;
							char *Show = ctime(&Left); Show[24] = 46;
							std::string Msg = "Join request did not expired. Please try again at " + (std::string)Show;
							IPlayer.BoxMsg(Msg.c_str());
						} else {
							MainSvrT::UpdateJoinRequest(IPlayer.GetName(),IPlayer.GetLevel(),(int)time(0),GID,0,IPlayer.GetPID());
							time_t Left = time(0) + 259200;
							char *Show = ctime(&Left); Show[24] = 32;
							std::string Msg = "Join request sent. Request expiration time is " + (std::string)Show + "or right after guild leader deny.";
							IPlayer.BoxMsg(Msg.c_str());
						}
						IPlayer.Buff(436,3,0);
					}
				}
			}

			if (Type == 9)
			{
				if (IPlayer.GetPID() == IPlayer.GetGID())
				{
					BYTE Type = 0, Accept = 0; int Count = 0; std::string GetName;
					CPacket::Read((char*)pPacket,(char*)pPos,"bdb",&Type,&Count,&Accept);	
					MainSvrT::GetJoinRequestName(IPlayer.GetGID(),Count,GetName);

					if (GetName != "none")
					{
						if (Accept)
						{
							int Level = 0, Time = 0, GID = 0, Count = 0, PID = 0, Msg = 0;
							MainSvrT::GetJoinRequest(GetName.c_str(),Level,Time,GID,Count,PID);
							int Player = CPlayer::ScanPlayer(PID);				
							if (Player)
							{
								Msg = CGuild::ConfluxAnswer((void*)Player,GID,GID,1);
								if (Msg == 23) Msg = 25;
								if (Msg == 24) Msg = 26;
								if (Msg == 29) Msg = 31;
								if (Msg == 94) Msg = 104;
							} else {
								IPlayer.BoxMsg("Player is offline. Acception will send to player on first login.");
								MainSvrT::UpdateAcceptJoinRequest(PID,GID);
							}
							MainSvrT::DeleteJoinRequest(GetName.c_str());
							CPlayer::Write(IPlayer.GetOffset(),94,"bdsbdb",8,Count,GetName.c_str(),0,0,0);
							if (Player) CPlayer::Write((void*)Player,94,"bb",0,Msg);
							if (Player) CSkill::ObjectRelease((void*)Player, Player + 352);
						} else {
							MainSvrT::DeleteJoinRequest(GetName.c_str());
							CPlayer::Write(IPlayer.GetOffset(),94,"bdsbdb",8,Count,GetName.c_str(),0,0,0);
						}
					}
				}
			}

			if (Type == 19)
			{
				if (Exp == 0 && IPlayer.GetGID())
				{
					int Day = MainSvrT::ReadGuildRewards(IPlayer.GetPID());

					if (Day != String2Int(Time::GetDay()))
					{
						if (IPlayer.IsOnline() && CPlayer::GetInvenSize((int)Player) > IPlayer.MaxInventorySize())
						{
							IPlayer.BoxMsg("Your inventory is full!");
							return;
						}

						if (GuildReward.find(String2Int(Time::GetDay()))->second.Size > IPlayer.GuildSize())
						{
							IPlayer.BoxMsg("Your guild size is not enough!");
							return;
						}

						if (GuildReward.count(String2Int(Time::GetDay())))
						{
							int Index = GuildReward.find(String2Int(Time::GetDay()))->second.Index;
							int Prefix = GuildReward.find(String2Int(Time::GetDay()))->second.Prefix;
							int Amount = GuildReward.find(String2Int(Time::GetDay()))->second.Amount;
							int Item = CItem::CreateItem(Index,Prefix,Amount,-1);
			
							if (Item)
							{
								if (CPlayer::_InsertItem(IPlayer.GetOffset(),7,Item) != 1)
								{
									CBase::Delete((void*)Item);
									return;
								}
							}

							MainSvrT::SetGuildRewards(IPlayer.GetPID(),String2Int(Time::GetDay()));
						}
					} else {
						IPlayer.BoxMsg("You already got guild daily reward!");
					}
				}
			}

			if (Type == 20)
			{
				if (Exp == 1)
				{
					if (IPlayer.DecreaseItemAmount(3360,135))
					{
						std::string Name, LName, BuffName; int Level = 0, Exp = 0, Point = 0, Attendance = 0, BuffType = 0, Time = 0, Size = 0;
						MainSvrT::ReadGuild(IPlayer.GetGID(),Name,LName,Level,Exp,Point,Attendance,BuffType,BuffName,Time,Size);				
						if (Time > (int)time(0)) Time += 86400; else Time = (int)time(0) + 86400; BuffName = "Guild";		
						MainSvrT::UpdateGuild(IPlayer.GetGID(),Name.c_str(),LName.c_str(),Level,Exp,Point,Attendance,BuffType,BuffName.c_str(),Time,Size);
						IPlayer.BoxMsg("1 day blessing of guild charged.");
					} else {
						IPlayer.BoxMsg("You do not have enough jewels!");
					}
				}

				if (Exp == 3)
				{
					if (IPlayer.DecreaseItemAmount(3360,284))
					{
						std::string Name, LName, BuffName; int Level = 0, Exp = 0, Point = 0, Attendance = 0, BuffType = 0, Time = 0, Size = 0;
						MainSvrT::ReadGuild(IPlayer.GetGID(),Name,LName,Level,Exp,Point,Attendance,BuffType,BuffName,Time,Size);				
						if (Time > (int)time(0)) Time += 259200; else Time = (int)time(0) + 259200; BuffName = "Guild";		
						MainSvrT::UpdateGuild(IPlayer.GetGID(),Name.c_str(),LName.c_str(),Level,Exp,Point,Attendance,BuffType,BuffName.c_str(),Time,Size);
						IPlayer.BoxMsg("3 days blessing of guild charged.");
					} else {
						IPlayer.BoxMsg("You do not have enough jewels!");
					}
				}

				if (Exp == 7)
				{
					if (IPlayer.DecreaseItemAmount(3360,473))
					{
						std::string Name, LName, BuffName; int Level = 0, Exp = 0, Point = 0, Attendance = 0, BuffType = 0, Time = 0, Size = 0;
						MainSvrT::ReadGuild(IPlayer.GetGID(),Name,LName,Level,Exp,Point,Attendance,BuffType,BuffName,Time,Size);				
						if (Time > (int)time(0)) Time += 604800; else Time = (int)time(0) + 604800; BuffName = "Guild";		
						MainSvrT::UpdateGuild(IPlayer.GetGID(),Name.c_str(),LName.c_str(),Level,Exp,Point,Attendance,BuffType,BuffName.c_str(),Time,Size);
						IPlayer.BoxMsg("7 days blessing of guild charged.");
					} else {
						IPlayer.BoxMsg("You do not have enough jewels!");
					}
				}
			}

			if (Type == 2) Type = 1;
			else if (Type == 4) Type = 3;
			else if (Type == 6) Type = 5;
			else if (Type == 8) Type = 7;
			else if (Type == 9) Type = 8;
			else if (Type == 13) Type = 12;
			else if (Type == 16) Type = 10;
			else if (Type == 17) Type = 11;
			else if (Type == 22) Type = 13;
			else if (Type == 24) Type = 15;
			else if (Type == 25) Type = 16;
			else if (Type == 27) Type = 18;
			else if (Type == 28) Type = 19;
			else if (Type == 29) Type = 20;
			else if (Type == 30) Type = 21;
			else if (Type == 31) Type = 22;
			else if (Type == 32) Type = 23;
			else if (Type == 33) Type = 24;
			else if (Type == 34) Type = 25;
			else if (Type == 35) Type = 26;
			else if (Type == 36) Type = 27;
			else if (Type == 37) Type = 28;
			else if (Type == 38) Type = 29;
			Interface<ITools> Tools; Tools->Compile((char*)pPacket,"b",Type);

			if (Type == 12)
			{
				if (IPlayer.GetGID())
				{
					if (IPlayer.GetGID() == IPlayer.GetPID())
					{
						std::string Name, LName, BuffName; int Level = 0, Exp = 0, Point = 0, Attendance = 0, BuffType = 0, Time = 0, Size = 0;
						MainSvrT::ReadGuild(IPlayer.GetGID(),Name,LName,Level,Exp,Point,Attendance,BuffType,BuffName,Time,Size);
						if (Exp < 16000000) Tools->Compile((char*)pPacket,"bb",Type,10);
						else return;
					} else {
						return;
					}
				} else {
					return;
				}
			}
		}

		if (packet == 202)
		{
			int Level = 0, Count = 0, IID = 0, NextIID = 0, Value = 0, Check = 0;
			int ReNextCheck = 0, NextCheck = 0, NextValue = 0, Recheck = 0;
			CPacket::Read((char*)pPacket,(char*)pPos,"dddd",&Level,&Count,&IID,&NextIID);
			if (IID == NextIID) return; int NextItemGet = 0, ItemGet = 0;
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&NextValue, (int)&NextIID);
			NextCheck = Undefined::Check((int)((char *)Player + 1068), (int)&ReNextCheck);
			if (!Undefined::CheckValues(&NextValue, NextCheck)) return;
			NextItemGet = *(DWORD*)(Undefined::GetValue(&NextValue) + 4);
			IItem ConsumeItem((void*)NextItemGet);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			ItemGet = *(DWORD*)(Undefined::GetValue(&Value) + 4);
			IItem MainItem((void*)ItemGet);
			if (ConsumeItem.CheckIndex() != 9800) return;
			if (MainItem.CheckIndex() < 9801 || MainItem.CheckIndex() > 9839) return;
			return;
		}

		if (packet == 200)
		{
			int Type = 0, IID = 0, Value = 0, Item = 0;
			CPacket::Read((char*)pPacket,(char*)pPos,"dd",&IID,&Type);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			int Recheck = 0, Check = 0, SetType = 200, JewelCalculation = 0;
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Item = *(DWORD *)(Undefined::GetValue(&Value) + 4);
			IItem MainItem((void*)Item);
			if (MainItem.CheckIndex() < 9801 || MainItem.CheckIndex() > 9840) return;
			return;
		}

		if (packet == 178)
		{
			unsigned char Type = 0, bType = 0; int IID = 0, SIID = 0, EIID = 0;
			CPacket::Read((char*)pPacket,(char*)pPos,"bdddb",&Type,&IID,&SIID,&EIID,&bType);
			if (IID == SIID || IID == EIID) return;
			if (SIID == IID || SIID == EIID) return;
			if (EIID == SIID || EIID == SIID) return;

			if (Type == 3 && bType == 1)
			{
				int Value = 0, Item = 0, Recheck = 0, Check = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				Item = *(DWORD *)(Undefined::GetValue(&Value) + 4);
				IItem MainItem((void*)Item);
				if (CItem::IsState(Item,1)) return;
				int sValue = 0, sItem = 0, sRecheck = 0, sCheck = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&sValue, (int)&SIID);
				sCheck = Undefined::Check((int)((char *)Player + 1068), (int)&sRecheck);
				if (!Undefined::CheckValues(&sValue, sCheck)) return;
				sItem = *(DWORD *)(Undefined::GetValue(&sValue) + 4);
				IItem Solvent((void*)sItem);
				if (Solvent.CheckIndex() != 3202) return;
				if (CPlayer::FindItem(IPlayer.Offset, 3202, 1))
					IPlayer.DecreaseItemAmount(3202,1);
				else
					return;
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);
				int FirstDemonGongType = 0, SecondDemonGongType = 0, FirstDemonGongStat = 0, SecondDemonGongStat = 0;

				if (GetStat)
				{
					FirstDemonGongType = (GetStat % 100000000) / 10000000;
					SecondDemonGongType = (GetStat % 1000000) / 100000;
					FirstDemonGongStat = (GetStat % 10000000) / 1000000;
					SecondDemonGongStat = (GetStat % 100000) / 10000;
				}

				if (EIID && FirstDemonGongType)
				{
					int eValue = 0, eItem = 0, eRecheck = 0, eCheck = 0;
					Undefined::CreateMonsterValue((char *)Player + 1068, (int)&eValue, (int)&EIID);
					eCheck = Undefined::Check((int)((char *)Player + 1068), (int)&eRecheck);
					if (!Undefined::CheckValues(&eValue, eCheck)) return;
					eItem = *(DWORD *)(Undefined::GetValue(&eValue) + 4);
					IItem Elaborate((void*)eItem);
					if (Elaborate.CheckIndex() != 3203) return;
					if (CPlayer::FindItem(IPlayer.Offset, 3203, 1))
						IPlayer.DecreaseItemAmount(3203,1);
					else
						return;
					int GiveBackIndex = 48;
					if (FirstDemonGongType == 1) GiveBackIndex = 3199;
					if (FirstDemonGongType == 2) GiveBackIndex = 3201;
					if (FirstDemonGongType == 3) GiveBackIndex = 3200;
					CItem::InsertItem((int)IPlayer.Offset,27,GiveBackIndex,FirstDemonGongStat+1,1,-1);
					MainSvrT::UpdateItemTable(MainItem.GetIID(),GetDSS,GetStat-((10000000*FirstDemonGongType) + (FirstDemonGongStat*1000000)),GetTime,Lock);
					IPlayer.CloseWindow("mstone_clear");
					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					return;
				}

				if (!EIID && FirstDemonGongType)
				{
					MainSvrT::UpdateItemTable(MainItem.GetIID(),GetDSS,GetStat-((10000000*FirstDemonGongType) + (FirstDemonGongStat*1000000)),GetTime,Lock);
					IPlayer.CloseWindow("mstone_clear");
					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					return;
				}
			}

			if (Type == 3 && bType == 2)
			{
				int Value = 0, Item = 0, Recheck = 0, Check = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				Item = *(DWORD *)(Undefined::GetValue(&Value) + 4);
				IItem MainItem((void*)Item);
				if (CItem::IsState(Item,1)) return;
				int sValue = 0, sItem = 0, sRecheck = 0, sCheck = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&sValue, (int)&SIID);
				sCheck = Undefined::Check((int)((char *)Player + 1068), (int)&sRecheck);
				if (!Undefined::CheckValues(&sValue, sCheck)) return;
				sItem = *(DWORD *)(Undefined::GetValue(&sValue) + 4);
				IItem Solvent((void*)sItem);
				if (Solvent.CheckIndex() != 3202) return;
				if (CPlayer::FindItem(IPlayer.Offset, 3202, 1))
					IPlayer.DecreaseItemAmount(3202,1);
				else
					return;
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);
				int FirstDemonGongType = 0, SecondDemonGongType = 0, FirstDemonGongStat = 0, SecondDemonGongStat = 0;

				if (GetStat)
				{
					FirstDemonGongType = (GetStat % 100000000) / 10000000;
					SecondDemonGongType = (GetStat % 1000000) / 100000;
					FirstDemonGongStat = (GetStat % 10000000) / 1000000;
					SecondDemonGongStat = (GetStat % 100000) / 10000;
				}

				if (EIID && SecondDemonGongType)
				{
					int eValue = 0, eItem = 0, eRecheck = 0, eCheck = 0;
					Undefined::CreateMonsterValue((char *)Player + 1068, (int)&eValue, (int)&EIID);
					eCheck = Undefined::Check((int)((char *)Player + 1068), (int)&eRecheck);
					if (!Undefined::CheckValues(&eValue, eCheck)) return;
					eItem = *(DWORD *)(Undefined::GetValue(&eValue) + 4);
					IItem Elaborate((void*)eItem);
					if (Elaborate.CheckIndex() != 3203) return;
					if (CPlayer::FindItem(IPlayer.Offset, 3203, 1))
						IPlayer.DecreaseItemAmount(3203,1);
					else
						return;
					int GiveBackIndex = 48;
					if (SecondDemonGongType == 1) GiveBackIndex = 3199;
					if (SecondDemonGongType == 2) GiveBackIndex = 3201;
					if (SecondDemonGongType == 3) GiveBackIndex = 3200;
					CItem::InsertItem((int)IPlayer.Offset,27,GiveBackIndex,SecondDemonGongStat+1,1,-1);
					MainSvrT::UpdateItemTable(MainItem.GetIID(),GetDSS,GetStat-((100000*SecondDemonGongType) + (SecondDemonGongStat*10000)),GetTime,Lock);
					IPlayer.CloseWindow("mstone_clear");
					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					return;
				}

				if (!EIID && SecondDemonGongType)
				{
					MainSvrT::UpdateItemTable(MainItem.GetIID(),GetDSS,GetStat-((100000*SecondDemonGongType) + (SecondDemonGongStat*10000)),GetTime,Lock);
					IPlayer.CloseWindow("mstone_clear");
					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					return;
				}
			}
		}

		if (packet == 91)
		{
			char Type = 0; int IID = 0, Jewel = 0; CPacket::Read((char*)pPacket,(char*)pPos,"bdd",&Type,&IID,&Jewel);

			if (Type == 9 && IID)
			{
				int Value = 0, Item = 0, Recheck = 0, Check = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem IItem((void*)Item);
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);

				if (GetStat >= 10)
				{
					IPlayer.BoxMsg("This item cannot do more an enchant.");
				} else {
					int ItemIndex = 0; if (IItem.CheckIndex() < 7883 && IItem.CheckIndex() > 7906) return;
					if (IItem.CheckIndex() >= 7883 && IItem.CheckIndex() <= 7888) ItemIndex = 7907;
					if (IItem.CheckIndex() >= 7889 && IItem.CheckIndex() <= 7894) ItemIndex = 7908;
					if (IItem.CheckIndex() >= 7895 && IItem.CheckIndex() <= 7900) ItemIndex = 7909;
					if (IItem.CheckIndex() >= 7901 && IItem.CheckIndex() <= 7906) ItemIndex = 7910;

					if (ItemIndex && CPlayer::RemoveItem(IPlayer.GetOffset(),9,ItemIndex,10))
					{
						if (ItemIndex == 7907 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,30000))
						{
							if (BattleHorseEnchantRate[0][GetStat] >= CTools::Rate(1,10000000))
							{
								MainSvrT::UpdateItemTable(IID,GetDSS,GetStat+1,GetTime,Lock);
								ItemShow::BattleHorseStat(IPlayer.GetOffset(),IID,IItem.CheckIndex(),GetStat+1,true);
								IPlayer.BoxMsg("It has succeeded to enchant and item's grade has increased.");
							} else {
								IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
							}
						}

						if (ItemIndex == 7908 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,60000))
						{
							if (Jewel && !CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,19)) return;

							if (BattleHorseEnchantRate[0][GetStat] >= CTools::Rate(1,10000000))
							{
								MainSvrT::UpdateItemTable(IID,GetDSS,GetStat+1,GetTime,Lock);
								ItemShow::BattleHorseStat(IPlayer.GetOffset(),IID,IItem.CheckIndex(),GetStat+1,true);
								IPlayer.BoxMsg("It has succeeded to enchant and item's grade has increased.");
							} else {
								if (CTools::Rate(1,100) > 90)
								{
									if (Jewel)
									{
										IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
									} else {
										IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items and battle horse equipment together.");
										(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);
									}
								} else {
									IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
								}
							}
						}

						if (ItemIndex == 7909 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,100000))
						{
							if (Jewel && !CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,39)) return;

							if (BattleHorseEnchantRate[0][GetStat] >= CTools::Rate(1,10000000))
							{
								MainSvrT::UpdateItemTable(IID,GetDSS,GetStat+1,GetTime,Lock);
								ItemShow::BattleHorseStat(IPlayer.GetOffset(),IID,IItem.CheckIndex(),GetStat+1,true);
								IPlayer.BoxMsg("It has succeeded to enchant and item's grade has increased.");
							} else {
								if (CTools::Rate(1,100) > 90)
								{
									if (Jewel)
									{
										IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
									} else {
										IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items and battle horse equipment together.");
										(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);
									}
								} else {
									IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
								}
							}
						}

						if (ItemIndex == 7910 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,200000))
						{
							if (Jewel && !CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,59)) return;

							if (BattleHorseEnchantRate[0][GetStat] >= CTools::Rate(1,10000000))
							{
								MainSvrT::UpdateItemTable(IID,GetDSS,GetStat+1,GetTime,Lock);
								ItemShow::BattleHorseStat(IPlayer.GetOffset(),IID,IItem.CheckIndex(),GetStat+1,true);
								IPlayer.BoxMsg("It has succeeded to enchant and item's grade has increased.");
							} else {
								if (CTools::Rate(1,100) > 90)
								{
									if (Jewel)
									{
										IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
									} else {
										IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items and battle horse equipment together.");
										(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);
									}
								} else {
									IPlayer.BoxMsg("A failed a battle horse item enchant. Deducted an enchant material items only.");
								}
							}
						}
					}
				}

				return;
			}

			if (Type == 5 && IID)
			{
				int Value = 0, Item = 0, Recheck = 0, Check = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem IItem((void*)Item);
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);

				if (IPlayer.IsOnline() && CPlayer::GetInvenSize((int)Player) > IPlayer.MaxInventorySize())
				{
					IPlayer.BoxMsg("Your inventory is full.");
					return;
				}

				if (GetStat < 10)
				{
					IPlayer.BoxMsg("This item enchant level is low, so please enchant to level 10 first.");
					return;
				}

				if (IItem.CheckIndex() >= 7901 && IItem.CheckIndex() <= 7906)
				{
					IPlayer.BoxMsg("This item is max grade. Please check item and try again later.");
				} else {
					int ItemIndex = 0; if (IItem.CheckIndex() < 7883 && IItem.CheckIndex() > 7906) return;
					if (IItem.CheckIndex() >= 7883 && IItem.CheckIndex() <= 7888) ItemIndex = 7943;
					if (IItem.CheckIndex() >= 7889 && IItem.CheckIndex() <= 7894) ItemIndex = 7944;
					if (IItem.CheckIndex() >= 7895 && IItem.CheckIndex() <= 7900) ItemIndex = 7945;

					if (ItemIndex)
					{
						if (ItemIndex == 7943 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,1000000) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,ItemIndex,50))
						{
							if (3000000 >= CTools::Rate(1,10000000))
							{
								CPlayer::Write(IPlayer.GetOffset(),255,"ddddd",242,0,0,128,255);
								IPlayer.CloseWindow("enchantbattleride");
								MainSvrT::DeleteItemTable(IID);
								CItem::InsertItem((int)Player,27,IItem.CheckIndex()+6,0,1,-1);
								(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);
							} else {
								IPlayer.BoxMsg("A failed a battle horse item upgrade. Deducted an enchant material items only.");
							}
						}

						if (ItemIndex == 7944 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,3000000) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,ItemIndex,30))
						{
							if (2000000 >= CTools::Rate(1,10000000))
							{
								CPlayer::Write(IPlayer.GetOffset(),255,"ddddd",242,0,0,128,255);
								IPlayer.CloseWindow("enchantbattleride");
								MainSvrT::DeleteItemTable(IID);
								CItem::InsertItem((int)Player,27,IItem.CheckIndex()+6,0,1,-1);
								(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);
							} else {
								IPlayer.BoxMsg("A failed a battle horse item upgrade. Deducted an enchant material items only.");
							}
						}

						if (ItemIndex == 7945 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,4000000) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,ItemIndex,20))
						{
							if (1500000 >= CTools::Rate(1,10000000))
							{
								CPlayer::Write(IPlayer.GetOffset(),255,"ddddd",242,0,0,128,255);
								IPlayer.CloseWindow("enchantbattleride");
								MainSvrT::DeleteItemTable(IID);
								CItem::InsertItem((int)Player,27,IItem.CheckIndex()+6,0,1,-1);
								(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(*(DWORD*)Item + 120))((int)Item,IPlayer.GetOffset(),9,-1);
							} else {
								IPlayer.BoxMsg("A failed a battle horse item upgrade. Deducted an enchant material items only.");
							}
						}
					}	
				}

				return;
			}
		}

		if (packet == 195)
		{
			int Class = 0, Type = 0; CPacket::Read((char*)pPacket,(char*)pPos,"dd",&Class,&Type);
			if (BattleHorse.find(IPlayer.GetPID())->second.Index < 7880 || BattleHorse.find(IPlayer.GetPID())->second.Index > 7882) return;

			if (IPlayer.IsBuff(7880) || IPlayer.IsBuff(7881) || IPlayer.IsBuff(7882))
			{
				CPlayer::Write(IPlayer.GetOffset(),252,"d",2);
				return;
			}
			
			if (Class == 1 && Type == 0)
			{
				if (CheckHonor.find(IPlayer.GetPID())->second.RPx < 300)
				{
					CPlayer::Write(IPlayer.GetOffset(),252,"d",4);
					return;
				} else {
					if (BattleHorse.find(IPlayer.GetPID())->second.Satiety >= 259200) return;
					CheckHonor[IPlayer.GetPID()].RPx -= 300;
					BattleHorse[IPlayer.GetPID()].Satiety += (1728*5);
					CPlayer::Write(IPlayer.GetOffset(),251,"dd",BattleHorse.find(IPlayer.GetPID())->second.IID,BattleHorse.find(IPlayer.GetPID())->second.Satiety);
					MainSvrT::UpdateBattleHorse(IPlayer.GetPID(),BattleHorse.find(IPlayer.GetPID())->second.IID,BattleHorse.find(IPlayer.GetPID())->second.Satiety);
				}

				return;
			}

			if (Class == 2 && Type == 0)
			{
				if ((BattleHorse.find(IPlayer.GetPID())->second.Index == 7880 || BattleHorse.find(IPlayer.GetPID())->second.Index == 7882) && BattleHorse.find(IPlayer.GetPID())->second.Satiety >= 345600)
				{
					CPlayer::Write(IPlayer.GetOffset(),252,"d",1);
					return;
				}

				if (BattleHorse.find(IPlayer.GetPID())->second.Index == 7881 && BattleHorse.find(IPlayer.GetPID())->second.Satiety >= 518400)
				{
					CPlayer::Write(IPlayer.GetOffset(),252,"d",1);
					return;
				}

				if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,6))
				{
					BattleHorse[IPlayer.GetPID()].Satiety += (1728*30);
					if ((BattleHorse.find(IPlayer.GetPID())->second.Index == 7880 || BattleHorse.find(IPlayer.GetPID())->second.Index == 7882) && BattleHorse.find(IPlayer.GetPID())->second.Satiety > 345600) BattleHorse[IPlayer.GetPID()].Satiety = 345600;
					if (BattleHorse.find(IPlayer.GetPID())->second.Index == 7881 && BattleHorse.find(IPlayer.GetPID())->second.Satiety > 518400) BattleHorse[IPlayer.GetPID()].Satiety = 518400;
					CPlayer::Write(IPlayer.GetOffset(),251,"dd",BattleHorse.find(IPlayer.GetPID())->second.IID,BattleHorse.find(IPlayer.GetPID())->second.Satiety);
					MainSvrT::UpdateBattleHorse(IPlayer.GetPID(),BattleHorse.find(IPlayer.GetPID())->second.IID,BattleHorse.find(IPlayer.GetPID())->second.Satiety);			
				} else {
					CPlayer::Write(IPlayer.GetOffset(),252,"d",5);
					return;
				}
			}

			if (Class == 2 && Type == 1)
			{
				if ((BattleHorse.find(IPlayer.GetPID())->second.Index == 7880 || BattleHorse.find(IPlayer.GetPID())->second.Index == 7882) && BattleHorse.find(IPlayer.GetPID())->second.Satiety >= 345600)
				{
					CPlayer::Write(IPlayer.GetOffset(),252,"d",1);
					return;
				}

				if (BattleHorse.find(IPlayer.GetPID())->second.Index == 7881 && BattleHorse.find(IPlayer.GetPID())->second.Satiety >= 518400)
				{
					CPlayer::Write(IPlayer.GetOffset(),252,"d",1);
					return;
				}

				if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,42))
				{
					if (BattleHorse.find(IPlayer.GetPID())->second.Index == 7880 || BattleHorse.find(IPlayer.GetPID())->second.Index == 7882) BattleHorse[IPlayer.GetPID()].Satiety = 345600;
					if (BattleHorse.find(IPlayer.GetPID())->second.Index == 7881) BattleHorse[IPlayer.GetPID()].Satiety = 518400;
					CPlayer::Write(IPlayer.GetOffset(),251,"dd",BattleHorse.find(IPlayer.GetPID())->second.IID,BattleHorse.find(IPlayer.GetPID())->second.Satiety);
					MainSvrT::UpdateBattleHorse(IPlayer.GetPID(),BattleHorse.find(IPlayer.GetPID())->second.IID,BattleHorse.find(IPlayer.GetPID())->second.Satiety);			
				} else {
					CPlayer::Write(IPlayer.GetOffset(),252,"d",5);
					return;
				}
			}

			return;
		}

		if (packet == 24)
		{
			int BuyItemFix = 0;
			pPacket = (void*)CPacket::Read((char*)pPacket,(char*)pPos,"d",&BuyItemFix);
		}

		if (packet == 81 && BetDuel.count(IPlayer.GetID()))
		{
			char Type = 0; int nID = 0; CPacket::Read((char*)pPacket,(char*)pPos,"bd",&Type,&nID);

			if (!Type)
			{
				if (BetDuel.count(IPlayer.GetID())) BetDuel.erase(IPlayer.GetID());
				if (BetDuel.count(nID)) BetDuel.erase(nID);
			}
			
			if (Type && BetDuel.find(IPlayer.GetID())->second == nID)
			{
				void *pTarget = CPlayer::FindPlayer(nID); IChar Target(pTarget);
				if (pTarget && Target.IsOnline() && IPlayer.IsOnline()) CPlayer::StartTrade((int)IPlayer.GetOffset(),(int)Target.GetOffset());
				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
				return;
			}
		}

		if (packet == 196)
		{
			int Color = 0; CPacket::Read((char*)pPacket,(char*)pPos,"d",&Color);

			if (PlayerColor.count(IPlayer.GetPID()) && PlayerColor.find(IPlayer.GetPID())->second == Color)
			{
				IPlayer.SystemMessage("You selected same color! Please select different one to change it!",TEXTCOLOR_RED);
				return;
			}

			if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,30))
			{
				PlayerColor[IPlayer.GetPID()] = Color;
				CChar::WriteInSight(IPlayer.GetOffset(),223,"dd",IPlayer.GetID(),Color);
				MainSvrT::UpdatePlayerColor(IPlayer.GetPID(),Color);
				IPlayer.SystemMessage("Name color changed successfully!",TEXTCOLOR_GREEN);
			} else {
				IPlayer.SystemMessage("You do not have enough jewel.",TEXTCOLOR_RED);
			}

			return;
		}

		if (packet == 71 && EnableFake)
		{
			int ID = 0, Value = 0; unsigned char Size = 0;
			int Check = CPacket::Read((char*)pPacket,(char*)pPos,"ddb",&ID,&Value,&Size);		
			if (IPlayer.IsOnline() && SFP.count(ID) && Size == 1)
			{
				int IID = 0, Amount = 0; CPacket::Read((char*)Check, (char*)pPos, "dd", &IID, &Amount);
				if (IPlayer.IsOnline() && SFP.count(IID))
				{
					if (Amount > SFP.find(IID)->second.ShopAmount) return;
					int Check = 2147483647 / SFP.find(IID)->second.ShopPrice;
					if (Amount > Check) return;
					int Price = Amount * SFP.find(IID)->second.ShopPrice;
					if (*(DWORD*)(*(DWORD*)((int)Player + 1096) + 52) < Price) return;
					SFP[ID].ShopAmount -= Amount;
					if (!(*(int (__thiscall**)(DWORD,void*,signed int,signed int))(**((DWORD**)Player + 274) + 120))(*((DWORD*)Player + 274),IPlayer.GetOffset(),9,-Price)) CPlayer::_OutOfInven(IPlayer.GetOffset(),*((DWORD *)Player + 274));
					CItem::InsertItem((int)Player,27,SFP.find(IID)->second.ShopIndex,0,Amount,-1);
					return;
				}
			}
		}

		if (packet == 80 && EnableFake)
		{
			int ID = 0; CPacket::Read((char*)pPacket,(char*)pPos,"d",&ID);
			if (IPlayer.IsOnline() && SFP.count(ID)) return;
		}

		if (packet == 44 && EnableFake)
		{
			int ID = 0; CPacket::Read((char*)pPacket,(char*)pPos,"d",&ID);
			if (IPlayer.IsOnline() && SFP.count(ID))
			{
				std::string msg = "The party request has been made to " + SFP.find(ID)->second.Name + ".";
				IPlayer.SystemMessage(msg.c_str(),TEXTCOLOR_INFOMSG);
				return;
			}
		}

		if (packet == 64 && EnableFake)
		{
			char Type = 0; int Check = CPacket::Read((char*)pPacket,(char*)pPos,"b",&Type);
			if (IPlayer.IsOnline() && Type == 0)
			{
				char *Name; Interface<ITools> Tools; Tools->ParseData((char*)Check,"s",&Name);
				if (strlen(Name) && SFPN.count(Name))
				{
					CDBSocket::Write(29,"dbds",IPlayer.GetPID(),0,SFPN.find(Name)->second,Name);
					CPlayer::Write(IPlayer.GetOffset(),18,"bdsbdb",0,SFPN.find(Name)->second,Name,1,0,0);
					return;
				}
			}
		}

		if (packet == 17)
		{
			Interface<ITools> Tools; char *Msg; Tools->ParseData((char*)pPacket,"s",&Msg); if (!strlen(Msg)) return; 

			if (IPlayer.IsOnline() && (int)Msg[0] == 64)
			{
				size_t valid_len = strcspn(Msg," ");  if (valid_len < 2) return;
				std::string SentMsg = Msg; std::string GetName = SentMsg.substr(1,(valid_len-1));
				if (!strlen(GetName.c_str())) return;

				if (MainSvrT::CountIgnoreList(GetName.c_str(),IPlayer.GetName()))
				{
					IPlayer.SystemMessage("Player ignored you!",TEXTCOLOR_INFOMSG);
					return;
				}
			}
		}

		if (packet == 17 && EnableFake)
		{
			Interface<ITools> Tools; char *Msg; Tools->ParseData((char*)pPacket,"s",&Msg); if (!strlen(Msg)) return;
			std::string CheckMsg = (std::string)Msg; size_t valid_len = strcspn(Msg," ");
			if (IPlayer.IsOnline() && (int)Msg[0] == 64)
			{
				if (SFPN.count(CheckMsg.substr(1,(valid_len-1))))
				{
					CPlayer::Write(IPlayer.GetOffset(),60,"ss",IPlayer.GetName(),Msg);
					return;
				}
			}
		}

		if (packet == 70 && EnableFake)
		{
			char Type = 0; int ID = 0; CPacket::Read((char*)pPacket, (char*)pPos, "bd", &Type, &ID);

			if (IPlayer.IsOnline())
			{
				MacroGenVoyager::CGenVoyagerDB &db = MacroGenVoyager::CGenVoyagerDB::s_map.find(ID)->second;

				if (db.SELL.size() > 0)
				{
					Interface<ITools> Tools; char Packet[7100];

					for (int i = 0; i < db.SELL.size(); i += 3)
					{
						Tools->Compile(Packet+(i/3)*71,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbdd",db.SELL[i],ID,0,0,db.SELL[i+1],0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,db.SELL[i+2]);
					}

					CPlayer::Write(IPlayer.GetOffset(),13,"bddbbm",0,ID,0,db.SELL.size() / 3,0,Packet,(db.SELL.size() / 3)*71);
					return;
				}
			}

			if (IPlayer.IsOnline() && SFP.count(ID))
			{
				Interface<ITools> Tools; char Packet[142];
				Tools->Compile(Packet,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbdd",47,ID,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,999999999);
				if (SFP.find(ID)->second.ShopAmount)
				{
					Tools->Compile(Packet+71,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbdd",SFP.find(ID)->second.ShopIndex,ID,0,0,SFP.find(ID)->second.ShopAmount,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,SFP.find(ID)->second.ShopPrice);
					CPlayer::Write(IPlayer.GetOffset(),13,"bddbbm",0,ID,0,2,0,Packet,142);
				} else {
					CPlayer::Write(IPlayer.GetOffset(),13,"bddbbm",0,ID,0,1,0,Packet,71);
				}

				return;
			}
		}

		if (packet == 191)
		{
			int Type = 0, Index = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "dd", &Type, &Index);

			if (Type == 0 && MonsterPet.count(Index))
			{
				if (CPlayer::GetInvenSize((int)IPlayer.GetOffset()) < IPlayer.MaxInventorySize())
				{
					if (CPlayer::FindItem(IPlayer.GetOffset(),31,100000) && CPlayer::FindItem(IPlayer.GetOffset(),MonsterPet.find(Index)->second.FirstItem,MonsterPet.find(Index)->second.FirstItemCount) && CPlayer::FindItem(IPlayer.GetOffset(),MonsterPet.find(Index)->second.SecondItem,MonsterPet.find(Index)->second.SecondItemCount))
					{
						if (IPlayer.DecreaseItemAmount(31,100000) && IPlayer.DecreaseItemAmount(MonsterPet.find(Index)->second.FirstItem,MonsterPet.find(Index)->second.FirstItemCount) && IPlayer.DecreaseItemAmount(MonsterPet.find(Index)->second.SecondItem,MonsterPet.find(Index)->second.SecondItemCount))
						{
							IPlayer.CloseWindow("CreateMonsterPet");
							int xItem = CItem::CreateItem(Index,0,1,-1);
							if (xItem)
							{
								IItem IItem((void*)xItem);
								if (CPlayer::_InsertItem(IPlayer.GetOffset(),27,xItem) != 1) return;
								int aRate = CTools::Rate(0,19), bRate = CTools::Rate(0,19);
								int xRate = CTools::Rate(1,3), yRate = CTools::Rate(1,3);
								if (aRate == 12) aRate = 13; if (bRate == 12) bRate = 13;
								if ((Index >= 7704 && Index <= 7707) || (Index >= 7721 && Index <= 7722))
								{
									CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),aRate,xRate*10);
									CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),bRate,yRate*10);
									MainSvrT::UpdateMonsterPet(IItem.GetIID(),(aRate << 16 | xRate),(bRate << 16 | yRate));
								} else {
									CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),aRate,xRate*10);
									MainSvrT::UpdateMonsterPet(IItem.GetIID(),(aRate << 16 | xRate),0);
								}
							}
						}
					}
				}
			}

			return;
		}

		if (packet == 99)
		{
			char  mtype = 0, yintype = 0; int key = 0, TargetIID = 0, Value = 0, Item = 0, CheckWater = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bbdd", &mtype, &yintype, &key, &TargetIID);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&TargetIID);
			int Recheck = 0, Check = 0;
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);
			IItem MainItem((void*)Item);

			if (key > 1000)
			{
				key -= 1000;
				CheckWater = 1;
			}

			if (mtype == 10 && yintype == 1)
			{
				if (key > 28)
					key -=6;
				else if (key > 18)
					key -=4;
				else if (key > 8)
					key -=2;

				if (IPlayer.IsOnline() && CPlayer::GetInvenSize((int)IPlayer.GetOffset()) < IPlayer.MaxInventorySize())
				{
					if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,(key+2953),2))
					{
						CItem::InsertItem((int)IPlayer.GetOffset(),27,(key+2985),0,1,-1);
						IPlayer.CloseWindow("yinyang_enchant");
						IPlayer.OpenWindow("yinyang_enchant", 0, 0);
					}
				} else {
					IPlayer.SystemMessage("Inventory is full.",TEXTCOLOR_RED);
				}
			}

			if (mtype == 20 && yintype == 2)
			{
				if (key > 128)
					key -=6;
				else if (key > 118)
					key -=4;
				else if (key > 108)
					key -=2;

				if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,(key+2853),1))
				{
					if (CheckWater == 1)
					{
						if (!IPlayer.DecreaseItemAmount(3024,1)) return;
					}

					int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
					MainSvrT::ReadItemTable(TargetIID,GetDSS,GetStat,GetTime,Lock);
					int GetCurrentGrade = (GetStat % 10000) / 100;
					if (GetCurrentGrade == 0) GetCurrentGrade = 1;
					int GetRate = TriagramUpgradeRate[0][GetCurrentGrade-1], Rate = CTools::Rate(1,10000);
					if (GetCurrentGrade >= 14) return;

					if (Rate <= GetRate+(CheckWater*500))
					{
						GetStat = GetStat + 100;
						MainSvrT::UpdateItemTable(TargetIID,GetDSS,GetStat,GetTime,Lock);
						CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
						CPlayer::Write(IPlayer.GetOffset(),176,"bbdd",0,2,0,MainItem.GetIID());
					} else {
						if (GetCurrentGrade > 4)
						{
							int Rate = CTools::Rate(0,2);

							if (!Rate && !CheckWater)
							{
								CPlayer::Write(IPlayer.GetOffset(),176,"bb",0,3);
								GetStat = GetStat - 100;
								MainSvrT::UpdateItemTable(TargetIID,GetDSS,GetStat,GetTime,Lock);
								CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
							} else {
								CPlayer::Write(IPlayer.GetOffset(),176,"bb",0,4);
							}
						} else {
							CPlayer::Write(IPlayer.GetOffset(),176,"bb",0,4);
						}
					}
				}
			}

			if (mtype == 30 && yintype == 3)
			{
				if (key == 2000 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,5000) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2655,3)
					&& CPlayer::RemoveItem(IPlayer.GetOffset(),9,2659,3) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2660,1))
				{
					CItem::InsertItem((int)IPlayer.GetOffset(),27,3018,0,1,-1);
					return;
				}

				if (key == 2001 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,1000000) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2655,36)
					&& CPlayer::RemoveItem(IPlayer.GetOffset(),9,2659,36) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2660,9)
					&& CPlayer::RemoveItem(IPlayer.GetOffset(),9,2671,9))
				{
					CItem::InsertItem((int)IPlayer.GetOffset(),27,3019,0,1,-1);
					return;
				}

				if (key == 2002 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,14000000) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2655,144)
					&& CPlayer::RemoveItem(IPlayer.GetOffset(),9,2659,144) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2660,24)
					&& CPlayer::RemoveItem(IPlayer.GetOffset(),9,2671,24) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,2673,4))
				{
					CItem::InsertItem((int)IPlayer.GetOffset(),27,3020,0,1,-1);
					return;
				}
			}

			if (mtype == 100 && yintype == 10 && MainItem.CheckIndex() >= 3018 && MainItem.CheckIndex() <= 3020)
			{
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(TargetIID,GetDSS,GetStat,GetTime,Lock);

				if (TrigramGrade.find(IPlayer.GetPID())->second >= 4 && TrigramHP.find(IPlayer.GetPID())->second
					&& TrigramMP.find(IPlayer.GetPID())->second && TrigramAtk.find(IPlayer.GetPID())->second
					&& TrigramStr.find(IPlayer.GetPID())->second && TrigramAgi.find(IPlayer.GetPID())->second
					&& TrigramInt.find(IPlayer.GetPID())->second && TrigramWis.find(IPlayer.GetPID())->second
					&& TrigramHth.find(IPlayer.GetPID())->second && !GetStat)
				{
					int SetPrefix = 0, CurrentGrade = (TrigramGrade.find(IPlayer.GetPID())->second + 1);

					if (MainItem.CheckIndex() == 3020)
					{
						if (CurrentGrade == 5) SetPrefix = CTools::Rate(97,104);
						if (CurrentGrade == 6) SetPrefix = CTools::Rate(105,112);
						if (CurrentGrade == 7) SetPrefix = CTools::Rate(121,128);
						if (CurrentGrade == 8) SetPrefix = CTools::Rate(153,160);
						if (CurrentGrade == 9) SetPrefix = CTools::Rate(113,120);
						if (CurrentGrade == 10) SetPrefix = CTools::Rate(129,136);
						if (CurrentGrade == 11) SetPrefix = CTools::Rate(161,168);
						if (CurrentGrade == 12) SetPrefix = CTools::Rate(137,144);
						if (CurrentGrade >= 13) SetPrefix = CTools::Rate(169,176);
					} else {
						if (CurrentGrade == 5) SetPrefix = CTools::Rate(1,8);
						if (CurrentGrade == 6) SetPrefix = CTools::Rate(9,16);
						if (CurrentGrade == 7) SetPrefix = CTools::Rate(33,40);
						if (CurrentGrade == 8) SetPrefix = CTools::Rate(65,72);
						if (CurrentGrade == 9) SetPrefix = CTools::Rate(17,24);
						if (CurrentGrade == 10) SetPrefix = CTools::Rate(41,18);
						if (CurrentGrade == 11) SetPrefix = CTools::Rate(73,80);
						if (CurrentGrade == 12) SetPrefix = CTools::Rate(49,56);
						if (CurrentGrade == 13) SetPrefix = CTools::Rate(81,88);
						if (CurrentGrade == 14) SetPrefix = CTools::Rate(145,152);
						if (CurrentGrade == 15) SetPrefix = CTools::Rate(177,184);
					}

					CPlayer::Write(IPlayer.GetOffset(),176,"bbdd",0,7,SetPrefix,MainItem.GetIID());
					GetStat = (1000 * SetPrefix);
					MainSvrT::UpdateItemTable(TargetIID,GetDSS,GetStat,GetTime,Lock);
					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
				}
			}

			if (mtype == 110 && yintype == 11 && MainItem.CheckIndex() >= 3018 && MainItem.CheckIndex() <= 3020 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,3023,1))
			{
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(TargetIID,GetDSS,GetStat,GetTime,Lock);
				CPlayer::Write(IPlayer.GetOffset(),176,"bbdd",0,6);
				MainSvrT::UpdateItemTable(TargetIID,GetDSS,0,GetTime,Lock);
				CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
			}

			return;
		}

		if (packet == 180)
		{
			if (CPlayer::FindItem(IPlayer.GetOffset(),3167,1))
			{
				if (CPlayer::GetInvenSize((int)IPlayer.GetOffset()) > IPlayer.MaxInventorySize())
				{
					IPlayer.SystemMessage("Your inventory is full.", TEXTCOLOR_RED);
					return;
				}

				if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,3167,1))
				{
					unsigned char Info = 0; int Rate = CTools::Rate(1,1000);
					CPacket::Read((char*)pPacket, (char*)pPos, "b", &Info);

					if (ExpertTalisman.count(Info))
					{
						if (Rate >= TOESR)
						{
							int Item = CPlayer::FindItem(IPlayer.GetOffset(),ExpertTalisman.find(Info)->second.Out,1);
							IItem IItem((void*)Item);

							if (Item)
							{
								if (IItem.GetInfo() & 4194304)
								{
									IPlayer.SystemMessage("You can not use talisman of expert on locked items.", TEXTCOLOR_RED);
									return;
								}

								int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
								MainSvrT::ReadItemTable(IItem.GetIID(),GetDSS,GetStat,GetTime,Lock);
								int ReplacePrefix = 0, NewItem = 0, ReplaceInfo = 0, ReplaceDef = 0, ReplaceEva = 0, ReplaceA = 0, ReplaceM = 0, ReplaceTOA = 0, ReplaceEB = 0;			
								if (*(DWORD*)(Item + 44)) ReplacePrefix = *(DWORD*)(*(DWORD*)(Item + 44) + 32);
								if (*(DWORD*)(Item + 48)) ReplaceInfo = *(DWORD*)(Item + 48);
								if (*(DWORD*)(Item + 108)) ReplaceDef = *(DWORD*)(Item + 108);
								if (*(DWORD*)(Item + 116)) ReplaceEva = *(DWORD*)(Item + 116);
								if (*(DWORD*)(Item + 100)) ReplaceA = *(DWORD*)(Item + 100);
								if (*(DWORD*)(Item + 104)) ReplaceM = *(DWORD*)(Item + 104);
								if (*(DWORD*)(Item + 112)) ReplaceTOA = *(DWORD*)(Item + 112);
								if (*(DWORD*)(Item + 124)) ReplaceEB = *(DWORD*)(Item + 124);

								if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,ExpertTalisman.find(Info)->second.Out,1))
								{
									NewItem = CItem::CreateItem(ExpertTalisman.find(Info)->second.In,ReplacePrefix,1,-1);
									
									if (NewItem)
									{
										CIOObject::AddRef(NewItem);

										if (CPlayer::_InsertItem(IPlayer.GetOffset(),27,NewItem) != 1)
										{
											CConsole::Red("Real time armor insert item Null error [PID (%d)] ", IPlayer.GetPID());
											CBase::Delete((void*)NewItem);
											CIOCriticalSection::Leave((void*)((char*)IPlayer.GetOffset() + 1020));
											return;
										}

										CIOObject::Release((void*)NewItem);
										
										if (ReplaceInfo)
										{
											*(DWORD*)(NewItem + 48) = ReplaceInfo;
											CDBSocket::Write(21,"dddbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),*(DWORD*)(NewItem + 48),8,7);
										}

										if (ReplaceDef)
										{
											*(DWORD*)(NewItem + 108) = ReplaceDef;
											CDBSocket::Write(17,"ddbbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),15,*(DWORD*)(NewItem + 108),0);
										}

										if (ReplaceEva)
										{
											*(DWORD*)(NewItem + 116) = ReplaceEva;
											CDBSocket::Write(17,"ddbbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),10,*(DWORD*)(NewItem + 116),0);
										}

										if (ReplaceA)
										{
											*(DWORD*)(NewItem + 100) = ReplaceA;
											CDBSocket::Write(17,"ddbbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),27,*(DWORD*)(NewItem + 100),0);
										}

										if (ReplaceM)
										{
											*(DWORD*)(NewItem + 104) = ReplaceM;
											CDBSocket::Write(17,"ddbbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),28,*(DWORD*)(NewItem + 104),0);
										}

										if (ReplaceTOA)
										{
											*(DWORD*)(NewItem + 112) = ReplaceTOA;
											CDBSocket::Write(17,"ddbbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),9,*(DWORD*)(NewItem + 112),0);
										}

										if (ReplaceEB)
										{
											*(DWORD*)(NewItem + 124) = ReplaceEB;
											CDBSocket::Write(28,"ddbb",*(DWORD*)(NewItem + 36),*(DWORD*)(NewItem + 32),2,*(DWORD*)(NewItem + 124));
										}

										MainSvrT::UpdateItemTable(*(DWORD*)(NewItem + 36),GetDSS,GetStat,GetTime,Lock);
										CItem::SendItemInfo((void*)NewItem,(int)IPlayer.GetOffset(),92);
										CPlayer::Write(IPlayer.GetOffset(),185,"bd",0,*(DWORD*)(NewItem + 36));
									}
								}
							}
							
							return;
						} else {
							CPlayer::Write(IPlayer.GetOffset(),185,"b",1);
							return;
						}
					}
				}
			}

			return;
		}

		if (packet == 181)
		{
			int Type = 0, IID = 0, PerfIID = 0, JewelCheck = 0, Argument = 0, JewelAmount = 0, JewelCalculation = 0, GongValue = 0, Value = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "dddbwb", &IID, &PerfIID, &Type, &JewelCheck, &Argument, &JewelAmount);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&GongValue, (int)&PerfIID);
			int PerfRecheck = 0, PerfCheck = 0, PerfItem = 0, ItemGet = 0;
			PerfCheck = Undefined::Check((int)((char *)Player + 1068), (int)&PerfRecheck);
			if (IID == PerfIID) return;
			if (!Undefined::CheckValues(&GongValue, PerfCheck)) return;
			PerfItem = *(DWORD*)(Undefined::GetValue(&GongValue) + 4);
			IItem PerfItemx((void*)PerfItem);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			int Recheck = 0, Check = 0;
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			ItemGet = *(DWORD*)(Undefined::GetValue(&Value) + 4);
			IItem MainItem((void*)ItemGet);
			int GetRate = 0, PrefixMainItem = MainItem.PrefixID(), PrefixStone = PerfItemx.PrefixID(), Rate = (int)CTools::Rate(0,10000);
			if (PrefixStone == 0) PrefixStone = 1;
			if (PrefixMainItem == 0) PrefixMainItem = 1;
			GetRate = DemonGongStoneEnchant[PrefixMainItem-1][PrefixStone-1];

			if (MainItem.GetInfo() & 4194304)
			{
				IPlayer.SystemMessage("Refining the Stone of Demon Gong can not be use on locked items.",TEXTCOLOR_RED);
				return;
			}

			if (PerfItemx.CheckIndex() < 3199 || PerfItemx.CheckIndex() > 3201)
			{
				IPlayer.BoxMsg("This is wrong material item.");
				return;
			}

			if (MainItem.CheckIndex() < 3199 || MainItem.CheckIndex() > 3201)
			{
				IPlayer.BoxMsg("This is wrong material item.");
				return;
			}

			if (MainItem.PrefixID() >= 10)
			{
				IPlayer.BoxMsg("Material grade already maximum.");
				return;
			}

			if (!CBase::IsDeleted((int)PerfItemx.GetOffset()))
				CItem::RemoveItem(IPlayer.GetOffset(), (int)PerfItemx.GetOffset());

			if (JewelCheck == 1 && JewelAmount >= 1 && JewelAmount <= 10)
			{
				if (CPlayer::FindItem(IPlayer.Offset, 3360, (50*JewelAmount)))
				{
					CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 3360, (50*JewelAmount));
					JewelCalculation = JewelAmount;
					GetRate += (JewelCalculation*500);
				} else {
					return;
				}
			}

			if (Rate <= GetRate)
			{
				int AddPrefix = CItem::FindPrefix(PrefixMainItem+1);
				if (!AddPrefix) return;
				*(DWORD *)((int)MainItem.GetOffset() + 44) = AddPrefix;
				CDBSocket::Write(19,"ddbb",MainItem.GetIID(),IPlayer.GetID(),PrefixMainItem+1);
				CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
				CPlayer::Write(IPlayer.GetOffset(),255,"dbdb",225,0,MainItem.GetIID(),PrefixMainItem+1);
			} else {
				IPlayer.BoxMsg("Refining the Stone of Demon Gong has failed.");
				CPlayer::Write(IPlayer.GetOffset(),255,"dbdb",225,1,20);
			}

			return;
		}

		if (packet == 178)
		{
			int Type = 0, IID = 0, PerfIID = 0, JewelCheck = 0, Argument = 0, JewelAmount = 0, JewelCalculation = 0, PerfValue = 0, Value = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bddbwb", &Type, &IID, &PerfIID, &JewelCheck, &Argument, &JewelAmount);
			if (IID == PerfIID) return;

			if (Type == 0)
			{
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&PerfValue, (int)&PerfIID);
				int PerfRecheck = 0, PerfCheck = 0, PerfItem = 0, ItemGet = 0;
				PerfCheck = Undefined::Check((int)((char *)Player + 1068), (int)&PerfRecheck);
				if (!Undefined::CheckValues(&PerfValue, PerfCheck)) return;
				PerfItem = *(DWORD*)(Undefined::GetValue(&PerfValue) + 4);
				IItem PerfItemx((void*)PerfItem);
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				int Recheck = 0, Check = 0;
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				ItemGet = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem MainItem((void*)ItemGet);
				int PerfGradeCheck = 0;
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);
				if (GetStat) PerfGradeCheck = GetStat / 100000000;

				if (PerfGradeCheck >= 1 && PerfItemx.CheckIndex() == 3057)
				{
					IPlayer.SystemMessage("Perforation shot can not be use on item anymore.",TEXTCOLOR_RED);
					return;
				}

				if (MainItem.GetInfo() & 4194304 && PerfItemx.CheckIndex() == 3057)
				{
					IPlayer.SystemMessage("You can not add perforation shot on locked items.",TEXTCOLOR_RED);
					return;
				}

				if (PerfGradeCheck < 1 && PerfItemx.CheckIndex() == 3166) return;
				if (MainItem.GetGrade() < 65 && PerfItemx.CheckIndex() == 3166) return;

				if (PerfGradeCheck >= 3 && PerfItemx.CheckIndex() == 3166)
				{
					IPlayer.SystemMessage("Gun of demon gong can not be use on item anymore.",TEXTCOLOR_RED);
					return;
				}

				if (MainItem.GetInfo() & 4194304 && PerfItemx.CheckIndex() == 3166)
				{
					IPlayer.SystemMessage("You can not add demon gong ability on locked items.",TEXTCOLOR_RED);
					return;
				}

				if (JewelCheck == 1 && JewelAmount >= 1 && JewelAmount <= 10)
				{
					int Multiply = 10;
					if (PerfItemx.CheckIndex() == 3166) Multiply = 20;

					if (CPlayer::FindItem(IPlayer.Offset, 3360, (Multiply*JewelAmount)))
					{
						CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 3360, (Multiply*JewelAmount));
						JewelCalculation = JewelAmount;
					} else {
						return;
					}
				}

				if (PerfItemx.CheckIndex() == 3057 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,3057,1))
				{
					int PerfRate = (int)CTools::Rate(1,1000);

					if (PerfRate >= (900-(JewelCalculation*70)))
					{
						MainSvrT::UpdateItemTable(IID,GetDSS,GetStat + 100000000,GetTime,Lock);
						if (CItem::IsState((int)MainItem.GetOffset(), 64)) CItem::SubState((int)MainItem.GetOffset(),64);
						CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
						CPlayer::Write(IPlayer.GetOffset(), 0xFF, "ddddd", 242, 0, 0, 128, 255);
						return;
					} else {
						if (CItem::IsState((int)MainItem.GetOffset(), 64))
						{
							CDBSocket::Write(21, "dddbb",MainItem.GetIID(),IPlayer.GetID(),64,0,255);
							CItem::SubState((int)MainItem.GetOffset(),64);
							CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
							return;
						}

						if (MainItem.GetEndurance() >= 5)
							MainItem.DecreaseEndurance(5);
						else
							MainItem.DecreaseEndurance(MainItem.GetEndurance());

						if (MainItem.GetEndurance() <= 0)
						{
							CPlayer::Write(IPlayer.GetOffset(),91,"db",MainItem.GetIID(),MainItem.GetEndurance());
							CDBSocket::Write(3,"ddwdbddd",MainItem.GetIID(),IPlayer.GetID(),MainItem.CheckIndex(),1,27,0,0,0);
							if (CItem::GetLevel((int)MainItem.GetOffset()) >= 40) CItem::InsertItem((int)IPlayer.GetOffset(), 27, 517, 0, 15, -1);
							CBase::Delete(MainItem.GetOffset());
						} else {
							CPlayer::Write(IPlayer.GetOffset(), 91, "db",MainItem.GetIID(),MainItem.GetEndurance());
							CDBSocket::Write(18,"ddb",MainItem.GetIID(),IPlayer.GetID(),MainItem.GetEndurance());
						}

						return;
					}
				}

				if (PerfItemx.CheckIndex() == 3166 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,3166,1))
				{
					int PerfRate = CTools::Rate(1,1000);

					if (PerfRate >= (900-(JewelCalculation*70)))
					{
						MainSvrT::UpdateItemTable(IID,GetDSS,GetStat + 100000000,GetTime,Lock);
						if (CItem::IsState((int)MainItem.GetOffset(), 64)) CItem::SubState((int)MainItem.GetOffset(),64);
						CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
						CPlayer::Write(IPlayer.GetOffset(), 0xFF, "ddddd", 242, 0, 0, 128, 255);
						return;
					} else {
						if (CItem::IsState((int)MainItem.GetOffset(), 64))
						{
							CDBSocket::Write(21, "dddbb",MainItem.GetIID(),IPlayer.GetID(),64,0,255);
							CItem::SubState((int)MainItem.GetOffset(),64);
							CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
							return;
						}

						if (MainItem.GetEndurance() >= 5)
							MainItem.DecreaseEndurance(5);
						else
							MainItem.DecreaseEndurance(MainItem.GetEndurance());

						if (MainItem.GetEndurance() <= 0)
						{
							CPlayer::Write(IPlayer.GetOffset(), 91, "db",MainItem.GetIID(),MainItem.GetEndurance());
							CDBSocket::Write(3,"ddwdbddd",MainItem.GetIID(),IPlayer.GetID(),MainItem.CheckIndex(),1,27,0,0,0);
							if (CItem::GetLevel((int)MainItem.GetOffset()) >= 40) CItem::InsertItem((int)IPlayer.GetOffset(), 27, 517, 0, 15, -1);
							CBase::Delete(MainItem.GetOffset());
						} else {
							CPlayer::Write(IPlayer.GetOffset(), 91, "db",MainItem.GetIID(),MainItem.GetEndurance());
							CDBSocket::Write(18,"ddb",MainItem.GetIID(),IPlayer.GetID(),MainItem.GetEndurance());
						}

						return;
					}
				}
			}

			if (Type == 1)
			{
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&PerfValue, (int)&PerfIID);
				int PerfRecheck = 0, PerfCheck = 0, PerfItem = 0, ItemGet = 0, SetType = 0;
				PerfCheck = Undefined::Check((int)((char *)Player + 1068), (int)&PerfRecheck);
				if (!Undefined::CheckValues(&PerfValue, PerfCheck)) return;
				PerfItem = *(DWORD*)(Undefined::GetValue(&PerfValue) + 4);
				IItem QigongItemx((void*)PerfItem);
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				int Recheck = 0, Check = 0;
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				ItemGet = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem MainItem((void*)ItemGet);
				int QigongGradeCheck = 0;
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);
				if (SuitWearFix.count(MainItem.CheckIndex())) return;
				if (DecoWearFix.count(MainItem.CheckIndex())) return;
				if (ConfigWeaponSkins.count(MainItem.CheckIndex())) return;

				if (MainItem.GetInfo() & 4194304)
				{
					IPlayer.SystemMessage("Qigong can not be use on locked items.",TEXTCOLOR_RED);
					return;
				}

				if (GetStat) QigongGradeCheck = GetStat % 100;

				if (JewelCheck == 1 && JewelAmount >= 1 && JewelAmount <= 10)
				{
					if (CPlayer::FindItem(IPlayer.Offset, 3360, (10*JewelAmount)))
					{
						CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 3360, (10*JewelAmount));
						JewelCalculation = JewelAmount;
					} else {
						return;
					}
				}

				if (QigongItemx.CheckIndex() == 3056 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,3056,1))
				{
					int QigongRate = CTools::Rate(1,1000);

					if (QigongRate >= (550-(JewelCalculation*50)))
					{
						if (QigongRate >= (750-(JewelCalculation*60)))
						{
							if (QigongRate >= (850-(JewelCalculation*60)))
							{
								if (QigongRate >= (950-(JewelCalculation*70)))
								{
									if (QigongRate >= (970-(JewelCalculation*70)))
									{
										if (QigongRate >= (995-(JewelCalculation*80)))
										{
											if (QigongRate >= (999-(JewelCalculation*80)))
											{
												int Rate = CTools::Rate(71,80);
												SetType += Rate;
												std::string msg = (std::string)IPlayer.GetName();
												msg = msg + " has been fused with the Black Spirit Of Insanity!";
												CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 8);
											} else {
												int Rate = CTools::Rate(61,70);
												SetType += Rate;
												std::string msg = (std::string)IPlayer.GetName();
												msg = msg + " has been fused with the Black Spirit Of Thunder!";
												CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 8);
											}	
										} else {
											int Rate = CTools::Rate(51,60);
											SetType += Rate;
										}
									} else {
										int Rate = CTools::Rate(41,50);
										SetType += Rate;
									}
								} else {
									int Rate = CTools::Rate(31,40);
									SetType += Rate;
								}
							} else {
								int Rate = CTools::Rate(21,30);
								SetType += Rate;
							}
						} else {
							int Rate = CTools::Rate(11,20);
							SetType += Rate;
						}
					} else {
						int Rate = CTools::Rate(1,10);
						SetType += Rate;
					}

					MainSvrT::UpdateItemTable(IID,GetDSS,(GetStat + SetType) - QigongGradeCheck,GetTime,Lock);
					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					CPlayer::Write(IPlayer.GetOffset(),0xFF,"ddd",235,MainItem.GetIID(),SetType);
					return;
				}
			}

			if (Type == 2)
			{
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&PerfValue, (int)&PerfIID);
				int PerfRecheck = 0, PerfCheck = 0, PerfItem = 0, ItemGet = 0, SetType = 0;
				PerfCheck = Undefined::Check((int)((char *)Player + 1068), (int)&PerfRecheck);
				if (!Undefined::CheckValues(&PerfValue, PerfCheck)) return;
				PerfItem = *(DWORD*)(Undefined::GetValue(&PerfValue) + 4);
				IItem DemonGongItemx((void*)PerfItem);
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				int Recheck = 0, Check = 0;
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				ItemGet = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem MainItem((void*)ItemGet);
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);
				int FirstDemonGongType = 0, DemonGongStyle = 0, GetStonePrefix = 1, FirstDemonGongStat = 0;

				if (MainItem.GetInfo() & 4194304)
				{
					IPlayer.SystemMessage("Demon Gong can not be use on locked items.",TEXTCOLOR_RED);
					return;
				}

				if (DemonGongItemx.CheckIndex() < 3199 || DemonGongItemx.CheckIndex() > 3201)
				{
					IPlayer.BoxMsg("This is wrong material item.");
					return;
				}

				if (DemonGongItemx.PrefixID() > 1) GetStonePrefix = DemonGongItemx.PrefixID();
				if (DemonGongItemx.CheckIndex() == 3199) DemonGongStyle = 1;
				if (DemonGongItemx.CheckIndex() == 3200) DemonGongStyle = 3;
				if (DemonGongItemx.CheckIndex() == 3201) DemonGongStyle = 2;
				if (!DemonGongStyle) return;
				if (GetStat) FirstDemonGongType = (GetStat % 100000000) / 10000000;

				if (JewelCheck == 2 && MainItem.GetGrade() >= 90)
				{
					int SecondDemonGongType = (GetStat % 1000000) / 100000, SecondDemonGongStat = 0;

					if (SecondDemonGongType)
					{
						IPlayer.SystemMessage("This is wrong material item.",TEXTCOLOR_RED);
						return;
					}

					if (SecondDemonGongType && SecondDemonGongType != DemonGongStyle)
					{
						IPlayer.SystemMessage("This is wrong material item.",TEXTCOLOR_RED);
						return;
					}

					if (!CBase::IsDeleted((int)DemonGongItemx.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(),(int)DemonGongItemx.GetOffset());
					if (!SecondDemonGongType) GetStat = GetStat + (DemonGongStyle*100000);
					SecondDemonGongStat = (GetStat % 100000) / 10000;
					if (SecondDemonGongStat) return;
					MainSvrT::UpdateItemTable(IID,GetDSS,GetStat + (GetStonePrefix*10000) - 10000,GetTime,Lock);

					if (DemonGongStyle == 1)
						CPlayer::Write(IPlayer.GetOffset(),255,"dddb",224,MainItem.GetIID(),10,JewelCheck);
					else
						CPlayer::Write(IPlayer.GetOffset(),255,"dddb",224,MainItem.GetIID(),(DemonGongStyle - 1) << 24,JewelCheck);

					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					return;
				}

				if (JewelCheck == 1)
				{
					if (FirstDemonGongType)
					{
						IPlayer.SystemMessage("This is wrong material item.",TEXTCOLOR_RED);
						return;
					}

					if (FirstDemonGongType && FirstDemonGongType != DemonGongStyle)
					{
						IPlayer.SystemMessage("This is wrong material item.",TEXTCOLOR_RED);
						return;
					}

					if (!CBase::IsDeleted((int)DemonGongItemx.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)DemonGongItemx.GetOffset());
					if (!FirstDemonGongType) GetStat = GetStat + (DemonGongStyle*10000000);
					FirstDemonGongStat = (GetStat % 10000000) / 1000000;
					if (FirstDemonGongStat && MainItem.GetGrade() < 90) return;
					MainSvrT::UpdateItemTable(IID,GetDSS,GetStat + (GetStonePrefix*1000000) - 1000000,GetTime,Lock);

					if (DemonGongStyle == 1)
						CPlayer::Write(IPlayer.GetOffset(),255,"dddb",224,MainItem.GetIID(),10,JewelCheck);
					else
						CPlayer::Write(IPlayer.GetOffset(),255,"dddb",224,MainItem.GetIID(),(DemonGongStyle - 1) << 24,JewelCheck);

					CItem::SendItemInfo(MainItem.GetOffset(), (int)IPlayer.GetOffset(), 92);
					return;
				}

				return;
			}

			return;
		}

		if (packet == 142)
		{
			int MSSIID = 0, MSSX = 0, MSSY = 0, MSSMap = 0, MSSValue = 0, Value = 0, Item = 0;
			CPacket::Read((char*)pPacket, (char*)pPos,"ddddd", &MSSIID, &MSSMap, &MSSX, &MSSY, &MSSValue);

			if (MSSIID)
			{
				if (!IPlayer.IsValid()) return;

				if (IPlayer.IsOnline() && MSSExp.count(IPlayer.GetPID()) && MSSExp.find(IPlayer.GetPID())->second.Level == IPlayer.GetLevel() && MSSExp.find(IPlayer.GetPID())->second.Exp > _ExpTable[IPlayer.GetLevel()] / 3)
				{
					IPlayer.SystemMessage("You already exceeded maximum monster summoning scroll exp limit.",TEXTCOLOR_RED);
					return;
				}

				if (CSMap::IsOnTile(*(void **)((int)IPlayer.Offset + 320), (int)IPlayer.Offset + 332, 131072) || CSMap::IsOnTile(*(void **)((int)IPlayer.Offset + 320), (int)IPlayer.Offset + 332, 1048576))
				{
					IPlayer.SystemMessage("Can not be use in safezones.",TEXTCOLOR_ORANGE);
					return;
				}

				if (MSSMap)
				{
					IPlayer.SystemMessage("Can not be use in dungeon and battle areas.",TEXTCOLOR_ORANGE);
					return;
				}

				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&MSSIID);
				int Recheck = 0, Check = 0;
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
				if (!Undefined::CheckValues(&Value, Check)) return;
				Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem MainItem((void*)Item);
				int ItemIndex = MainItem.CheckIndex();

				if (IPlayer.IsValid() && Item && MainItem.GetAmount() >= 1 && CPlayer::RemoveItem(IPlayer.GetOffset(), 9, ItemIndex, 1))
				{
					auto range = MSS.equal_range(ItemIndex);

					for (auto i = range.first; i != range.second; i++)
					{		
						int a = i->second.Index;
						int b = i->second.Amount;
						int c = i->second.Disappear;
						
						for (int i = 0; i < b; i++)
						{
							IChar Monster((void*)Summon(0,MSSMap,MSSX,MSSY,a,1,1,0,c,0));				
							if (Monster.IsValid() && IPlayer.IsValid()) Monster.Buff(367,1296000,(int)IPlayer.GetOffset());
						}
					}
				}
			}

			return;
		}

		if (packet == 254)
		{
			int Type = 100; char *Caller, *Recall; Interface<ITools> Tools;
			Tools->ParseData((char*)pPacket, "dss", &Type, &Caller, &Recall);

			if (strlen(Caller) > 0 && strlen(Recall) > 0)
			{
				void *MyCaller = (void*)CPlayer::FindPlayerByName(Caller);
				void *MyRecall = (void*)CPlayer::FindPlayerByName(Recall);
				IChar RCT(MyCaller); IChar RC(MyRecall);

				if (Type == 1)
				{
					if (RCT.IsValid() && RC.IsValid() && RCT.IsBuff(304))
					{
						RC.Teleport(RCT.GetMap(),RCT.GetX(),RCT.GetY());
						if (MyCaller) CSkill::ObjectRelease(MyCaller, (int)MyCaller + 352);
						if (MyRecall) CSkill::ObjectRelease(MyRecall, (int)MyRecall + 352);
						return;
					}

					if (RCT.IsValid() && !RC.IsValid())
					{
						std::string name = Recall;
						std::string msg = name + " is not valid or offline.";
						RCT.BoxMsg(msg);
						if (MyCaller) CSkill::ObjectRelease(MyCaller, (int)MyCaller + 352);
						if (MyRecall) CSkill::ObjectRelease(MyRecall, (int)MyRecall + 352);
						return;
					}

					if (!RCT.IsValid() && RC.IsValid())
					{
						std::string name = Caller;
						std::string msg = name + " is not valid or offline.";
						RC.BoxMsg(msg);
						if (MyCaller) CSkill::ObjectRelease(MyCaller, (int)MyCaller + 352);
						if (MyRecall) CSkill::ObjectRelease(MyRecall, (int)MyRecall + 352);
						return;
					}

					if (MyCaller) CSkill::ObjectRelease(MyCaller, (int)MyCaller + 352);
					if (MyRecall) CSkill::ObjectRelease(MyRecall, (int)MyRecall + 352);
					return;
				}

				if (Type == 0 && RCT.IsValid())
				{
					std::string name = Recall;
					std::string msg = name + " refused your recall.";
					RCT.BoxMsg(msg);
					if (MyCaller) CSkill::ObjectRelease(MyCaller, (int)MyCaller + 352);
					if (MyRecall) CSkill::ObjectRelease(MyRecall, (int)MyRecall + 352);
					return;
				}

				if (MyCaller) CSkill::ObjectRelease(MyCaller, (int)MyCaller + 352);
				if (MyRecall) CSkill::ObjectRelease(MyRecall, (int)MyRecall + 352);
			}

			return;
		}

		if (packet == 26)
		{
			int IID = 0, Amount = 0, Value = 0, Item = 0, Recheck = 0, Check = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "dd", &IID, &Amount);

			if (Amount > 0 && IID)
			{
				if (strlen(PlayerCheck) && ((std::string)PlayerCheck == "disable" || (std::string)PlayerCheck == "Disable")) return;
				if (IPlayer.IsBuff(372)) return;
				IPlayer.Buff(372,2,0);
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
				Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);

				if (Undefined::CheckValues(&Value, Check))
				{
					Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);

					if (Item && IID)
					{
						IItem Itemx((void*)Item);
						if (Itemx.GetInfo() & 4194304) return;
					}
				}
			}
		}

		if (packet == 187)
		{
			char Type = 0, Jewel = 0, Amount = 0; int IID = 0, xIIDx = 0; unsigned short Class = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bddbwb", &Type, &IID, &xIIDx, &Jewel, &Class, &Amount);
			int Recheck = 0, Check = 0, Value = 0, Item = 0;
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (IID == xIIDx) return;
			if (!Undefined::CheckValues(&Value, Check)) return;
			Item = *(DWORD *)(Undefined::GetValue(&Value) + 4);
			IItem MainItem((void*)Item);
			int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
			MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);

			if (Type == 2 && MainItem.CheckIndex() == 3381 && GetStat >= 2)
			{
				CPlayer::Write(IPlayer.GetOffset(),192,"b",8);
				return;
			}

			if (Type == 2 && MainItem.CheckIndex() == 3382 && GetStat >= 3)
			{
				CPlayer::Write(IPlayer.GetOffset(),192,"b",8);
				return;
			}

			if (Type == 2 && GetStat >= 4) return;

			if (Type == 0 && IID && xIIDx && Class == 102)
			{
				int Recheckx = 0, Checkx = 0, Valuex = 0, Itemx = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Valuex, (int)&xIIDx);
				Checkx = Undefined::Check((int)((char *)Player + 1068), (int)&Recheckx);
				if (!Undefined::CheckValues(&Valuex, Checkx)) return;
				Itemx = *(DWORD *)(Undefined::GetValue(&Valuex) + 4);
				IItem NextItem((void*)Itemx);
				if (NextItem.CheckIndex() != MainItem.CheckIndex()) return;

				if (Jewel == 1 && Amount == 1)
				{
					int JewelAmount = 1;
					if (MainItem.CheckIndex() == 3385) JewelAmount = 10;
					if (MainItem.CheckIndex() == 3386) JewelAmount = 50;
					int JewelCheck = CPlayer::FindItem(IPlayer.GetOffset(),3360,JewelAmount);
				
					if (JewelCheck && CPlayer::FindItem(IPlayer.GetOffset(),3360,JewelAmount))
						IPlayer.DecreaseItemAmount(3360,JewelAmount);
					else
						return;

					if (!CBase::IsDeleted((int)NextItem.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)NextItem.GetOffset());

					if (MainItem.CheckIndex() == 3384)
					{
						int Value = CTools::Rate(0,12); int Add = 50;
						if (Value == 12) Add = CTools::Rate(10,100);
						CPlayer::Write(IPlayer.GetOffset(),194,"dd",MainItem.GetIID(),(GetStat % 100));
						CPlayer::Write(IPlayer.GetOffset(),193,"ddd",MainItem.GetIID(),Value,Add);
						CPlayer::Write(IPlayer.GetOffset(),192,"bddd",3,MainItem.GetIID(),Value,Add);
						MainSvrT::UpdateItemTable(IID,GetDSS,Value+(Add*1000),GetTime,Lock);
					}

					if (MainItem.CheckIndex() == 3385)
					{
						int Value = CTools::Rate(0,19); int Add = CTools::Rate(50,100);
						if (Value == 12) Add = CTools::Rate(50,200);
						if (Value == 15) Add = CTools::Rate(10,20);
						if (Value == 16) Add = CTools::Rate(10,20);
						if (Value == 17) Add = CTools::Rate(10,20);
						CPlayer::Write(IPlayer.GetOffset(),194,"dd",MainItem.GetIID(),(GetStat % 100));
						CPlayer::Write(IPlayer.GetOffset(),193,"ddd",MainItem.GetIID(),Value,Add);
						CPlayer::Write(IPlayer.GetOffset(),192,"bddd",3,MainItem.GetIID(),Value,Add);
						MainSvrT::UpdateItemTable(IID,GetDSS,Value+(Add*1000),GetTime,Lock);
					}

					if (MainItem.CheckIndex() == 3386)
					{
						int Value = CTools::Rate(0,21); int Add = CTools::Rate(100,150);
						if (Value == 12) Add = CTools::Rate(100,300);
						if (Value == 15) Add = CTools::Rate(20,30);
						if (Value == 16) Add = CTools::Rate(20,30);
						if (Value == 17) Add = CTools::Rate(20,30);
						if (Value == 20) Add = CTools::Rate(10,100);
						if (Value == 21) Add = CTools::Rate(10,100);
						CPlayer::Write(IPlayer.GetOffset(),194,"dd",MainItem.GetIID(),(GetStat % 100));
						CPlayer::Write(IPlayer.GetOffset(),193,"ddd",MainItem.GetIID(),Value,Add);
						CPlayer::Write(IPlayer.GetOffset(),192,"bddd",3,MainItem.GetIID(),Value,Add);
						MainSvrT::UpdateItemTable(IID,GetDSS,Value+(Add*1000),GetTime,Lock);
					}
				} else {
					if (!CBase::IsDeleted((int)NextItem.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)NextItem.GetOffset());
					int SuccessRate = CTools::Rate(1,1000);

					if (MainItem.CheckIndex() == 3384)
					{
						if (SuccessRate >= 550)
						{
							int Value = CTools::Rate(0,12); int Add = 50;
							if (Value == 12) Add = CTools::Rate(10,100);
							CPlayer::Write(IPlayer.GetOffset(),194,"dd",MainItem.GetIID(),(GetStat % 100));
							CPlayer::Write(IPlayer.GetOffset(),193,"ddd",MainItem.GetIID(),Value,Add);
							CPlayer::Write(IPlayer.GetOffset(),192,"bddd",3,MainItem.GetIID(),Value,Add);
							MainSvrT::UpdateItemTable(IID,GetDSS,Value+(Add*1000),GetTime,Lock);
						} else {
							CPlayer::Write(IPlayer.GetOffset(),192,"b",4);
						}
					}

					if (MainItem.CheckIndex() == 3385)
					{
						if (SuccessRate >= 550)
						{
							int Value = CTools::Rate(0,19); int Add = CTools::Rate(50,100);
							if (Value == 12) Add = CTools::Rate(50,200);
							if (Value == 15) Add = CTools::Rate(10,20);
							if (Value == 16) Add = CTools::Rate(10,20);
							if (Value == 17) Add = CTools::Rate(10,20);
							CPlayer::Write(IPlayer.GetOffset(),194,"dd",MainItem.GetIID(),(GetStat % 100));
							CPlayer::Write(IPlayer.GetOffset(),193,"ddd",MainItem.GetIID(),Value,Add);
							CPlayer::Write(IPlayer.GetOffset(),192,"bddd",3,MainItem.GetIID(),Value,Add);
							MainSvrT::UpdateItemTable(IID,GetDSS,Value+(Add*1000),GetTime,Lock);
						} else {
							CPlayer::Write(IPlayer.GetOffset(),192,"b",4);
						}
					}

					if (MainItem.CheckIndex() == 3386)
					{
						if (SuccessRate >= 550)
						{
							int Value = CTools::Rate(0,21); int Add = CTools::Rate(100,150);
							if (Value == 12) Add = CTools::Rate(100,300);
							if (Value == 15) Add = CTools::Rate(20,30);
							if (Value == 16) Add = CTools::Rate(20,30);
							if (Value == 17) Add = CTools::Rate(20,30);
							if (Value == 20) Add = CTools::Rate(10,100);
							if (Value == 21) Add = CTools::Rate(10,100);
							CPlayer::Write(IPlayer.GetOffset(),194,"dd",MainItem.GetIID(),(GetStat % 100));
							CPlayer::Write(IPlayer.GetOffset(),193,"ddd",MainItem.GetIID(),Value,Add);
							CPlayer::Write(IPlayer.GetOffset(),192,"bddd",3,MainItem.GetIID(),Value,Add);
							MainSvrT::UpdateItemTable(IID,GetDSS,Value+(Add*1000),GetTime,Lock);
						} else {
							CPlayer::Write(IPlayer.GetOffset(),192,"b",4);
						}
					}
				}
			}

			if (Type == 1 && IID && xIIDx && Class == 103)
			{
				int Recheckx = 0, Checkx = 0, Valuex = 0, Itemx = 0;
				Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Valuex, (int)&xIIDx);
				Checkx = Undefined::Check((int)((char *)Player + 1068), (int)&Recheckx);
				if (!Undefined::CheckValues(&Valuex, Checkx)) return;
				Itemx = *(DWORD *)(Undefined::GetValue(&Valuex) + 4);
				IItem NextItem((void*)Itemx);
				if (NextItem.CheckIndex() != MainItem.CheckIndex()) return;
				if (MainItem.CheckIndex() == 3386) return;
				if (NextItem.CheckIndex() == 3386) return;

				if (Jewel == 1 && Amount == 1)
				{
					int JewelAmount = 5; int Index = MainItem.CheckIndex();
					if (MainItem.CheckIndex() == 3385) JewelAmount = 10;
					int JewelCheck = CPlayer::FindItem(IPlayer.GetOffset(),3360,JewelAmount);
					
					if (CPlayer::GetInvenSize((int)IPlayer.GetOffset()) >= IPlayer.MaxInventorySize())
					{
						IPlayer.SystemMessage("Inventory is full.", TEXTCOLOR_PINK);
						return;
					}
				
					if (JewelCheck && CPlayer::FindItem(IPlayer.GetOffset(),3360,JewelAmount))
						IPlayer.DecreaseItemAmount(3360,JewelAmount);
					else
						return;

					if (!CBase::IsDeleted((int)MainItem.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)MainItem.GetOffset());
					if (!CBase::IsDeleted((int)NextItem.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)NextItem.GetOffset());

					if (Index == 3384)
					{
						int xItem = CItem::CreateItem(3385, 0, 1, -1);

						if (xItem)
						{
							IItem IItem((void*)xItem);
							if (CPlayer::_InsertItem(IPlayer.GetOffset(), 27, xItem) != 1) return;
							int Value = CTools::Rate(0,19); int Add = CTools::Rate(50,100);
							if (Value == 12) Add = CTools::Rate(50,200);
							if (Value == 15) Add = CTools::Rate(10,20);
							if (Value == 16) Add = CTools::Rate(10,20);
							if (Value == 17) Add = CTools::Rate(10,20);
							CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),Value,Add);
							CPlayer::Write(IPlayer.GetOffset(),192,"bd",5,IItem.GetIID());
							MainSvrT::UpdateItemTable(IItem.GetIID(),0,Value+(Add*1000),0,"0");
						}
					}

					if (Index == 3385)
					{
						int xItem = CItem::CreateItem(3386, 0, 1, -1);

						if (xItem)
						{
							IItem IItem((void*)xItem);
							if (CPlayer::_InsertItem(IPlayer.GetOffset(), 27, xItem) != 1) return;
							int Value = CTools::Rate(0,21); int Add = CTools::Rate(100,150);
							if (Value == 12) Add = CTools::Rate(100,300);
							if (Value == 15) Add = CTools::Rate(20,30);
							if (Value == 16) Add = CTools::Rate(20,30);
							if (Value == 17) Add = CTools::Rate(20,30);
							if (Value == 20) Add = CTools::Rate(10,100);
							if (Value == 21) Add = CTools::Rate(10,100);
							CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),Value,Add);
							CPlayer::Write(IPlayer.GetOffset(),192,"bd",5,IItem.GetIID());
							MainSvrT::UpdateItemTable(IItem.GetIID(),0,Value+(Add*1000),0,"0");
						}
					}
				} else {
					int Index = MainItem.CheckIndex();
					if (CPlayer::GetInvenSize((int)IPlayer.GetOffset()) >= IPlayer.MaxInventorySize())
					{
						IPlayer.SystemMessage("Inventory is full.", TEXTCOLOR_PINK);
						return;
					}
					if (!CBase::IsDeleted((int)MainItem.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)MainItem.GetOffset());
					if (!CBase::IsDeleted((int)NextItem.GetOffset())) CItem::RemoveItem(IPlayer.GetOffset(), (int)NextItem.GetOffset());
					int SuccessRate = CTools::Rate(1,1000);

					if (Index == 3384)
					{
						if (SuccessRate >= 550)
						{
							int xItem = CItem::CreateItem(3385, 0, 1, -1);

							if (xItem)
							{
								IItem IItem((void*)xItem);
								if (CPlayer::_InsertItem(IPlayer.GetOffset(), 27, xItem) != 1) return;
								int Value = CTools::Rate(0,19); int Add = CTools::Rate(50,100);
								if (Value == 12) Add = CTools::Rate(50,200);
								if (Value == 15) Add = CTools::Rate(10,20);
								if (Value == 16) Add = CTools::Rate(10,20);
								if (Value == 17) Add = CTools::Rate(10,20);
								CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),Value,Add);
								CPlayer::Write(IPlayer.GetOffset(),192,"bd",5,IItem.GetIID());
								MainSvrT::UpdateItemTable(IItem.GetIID(),0,Value+(Add*1000),0,"0");
							}
						} else {
							CPlayer::Write(IPlayer.GetOffset(),192,"b",6);
						}
					}

					if (Index == 3385)
					{
						if (SuccessRate >= 550)
						{
							int xItem = CItem::CreateItem(3386, 0, 1, -1);

							if (xItem)
							{
								IItem IItem((void*)xItem);
								if (CPlayer::_InsertItem(IPlayer.GetOffset(), 27, xItem) != 1) return;
								int Value = CTools::Rate(0,21); int Add = CTools::Rate(100,150);
								if (Value == 12) Add = CTools::Rate(100,300);
								if (Value == 15) Add = CTools::Rate(20,30);
								if (Value == 16) Add = CTools::Rate(20,30);
								if (Value == 17) Add = CTools::Rate(20,30);
								if (Value == 20) Add = CTools::Rate(10,100);
								if (Value == 21) Add = CTools::Rate(10,100);
								CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IItem.GetIID(),Value,Add);
								CPlayer::Write(IPlayer.GetOffset(),192,"bd",5,IItem.GetIID());
								MainSvrT::UpdateItemTable(IItem.GetIID(),0,Value+(Add*1000),0,"0");
							}
						} else {
							CPlayer::Write(IPlayer.GetOffset(),192,"b",6);
						}
					}
				}
			}

			if (Type == 2 && IID && MainItem.CheckIndex() == 3383 && GetStat == 0) GetStat = 1;

			if (Type == 2 && IID && GetStat >= 1)
			{
				int JewelCheck = CPlayer::FindItem(IPlayer.GetOffset(),3360,GetStat*60);
				
				if (JewelCheck && CPlayer::FindItem(IPlayer.GetOffset(),3360,GetStat*60))
				{
					MainSvrT::UpdateItemTable(IID,GetDSS,GetStat+1,GetTime,Lock);
					IPlayer.DecreaseItemAmount(3360,GetStat*60);
					CPlayer::Write(IPlayer.GetOffset(),194,"dd",IID,100);
					CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IID,100,GetStat+2);			
					CPlayer::Write(IPlayer.GetOffset(),192,"b",7);
				} else {
					IPlayer.BoxMsg("You do not have enough jewels.");
				}
			}

			if (Type == 2 && IID && GetStat == 0)
			{
				if (CPlayer::FindItem(IPlayer.GetOffset(),31,1000000))
				{
					if (!(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(**((DWORD**)Player + 274) + 120))(*((DWORD *)Player + 274),IPlayer.GetOffset(),9,-1000000))
						CPlayer::_OutOfInven(IPlayer.GetOffset(),*((DWORD *)Player + 274));

					MainSvrT::UpdateItemTable(IID,GetDSS,GetStat+1,GetTime,Lock);
					CPlayer::Write(IPlayer.GetOffset(),194,"dd",IID,100);
					CPlayer::Write(IPlayer.GetOffset(),193,"ddd",IID,100,GetStat+2);		
					CPlayer::Write(IPlayer.GetOffset(),192,"b",7);
				} else {
					IPlayer.BoxMsg("You do not have enough zamogeon.");
				}
			}

			return;
		}

		if (packet == 109)
		{
			char *Password, *Question, *Answer; Interface<ITools> Tools;
			int LockIID = 0, IID = 0, LockValue = 0, Value = 0, LockItem = 0, Itemx = 0, Recheckx = 0, Checkx = 0;
			Tools->ParseData((char*)pPacket, "ddsss", &LockIID, &IID, &Password, &Question, &Answer);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&LockValue, (int)&LockIID);
			Checkx = Undefined::Check((int)((char *)Player + 1068), (int)&Recheckx);
			if (IID == LockIID) return;
			if (!strlen(Password) || !strlen(Question) || !strlen(Answer)) return;
			if (HaveSpecialCharacters(Password)) return;
			if (!Undefined::CheckValues(&LockValue, Checkx)) return;
			LockItem = *(DWORD*)(Undefined::GetValue(&LockValue) + 4);
			IItem ItemLock((void*)LockItem);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			int Recheck = 0, Check;
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Itemx = *(DWORD *)(Undefined::GetValue(&Value) + 4);
			IItem xItem((void*)Itemx);
			if (xItem.GetInfo() & 4194304) return;

			if (ItemLock.CheckIndex() == 1181)
			{
				CItem::RemoveItem((void*)Player, (int)ItemLock.GetOffset());
			} else {
				return;
			}	

			if (xItem.CheckIndex())
			{
				int CurrentInfo = *(DWORD *)(Itemx + 48) + 4194304;
				CDBSocket::Write(21, "dddbb",xItem.GetIID(),IPlayer.GetID(),xItem.GetInfo(),0,0);
				*(DWORD *)(Itemx + 48) = CurrentInfo;
				CDBSocket::Write(21, "dddbb",xItem.GetIID(),*(DWORD*)(Itemx + 28),CurrentInfo,8,7);
				CItem::SendItemInfo((void *)Itemx, (int)IPlayer.GetOffset(), 92);
				IPlayer.BoxMsg("You have successfully locked your item.");
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(xItem.GetIID(),GetDSS,GetStat,GetTime,Lock);
				MainSvrT::UpdateItemTable(xItem.GetIID(),GetDSS,GetStat,GetTime,Password);
			} else {
				return;
			}

			return;
		}

		if (packet == 110)
		{
			char *Password; Interface<ITools> Tools;
			int KeyIID = 0, IID = 0, KeyValue = 0, Value = 0, KeyItem = 0, Itemx = 0, Recheckx = 0, Checkx = 0;
			Tools->ParseData((char*)pPacket, "dds", &KeyIID, &IID, &Password);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&KeyValue, (int)&KeyIID);
			Checkx = Undefined::Check((int)((char *)Player + 1068), (int)&Recheckx);
			if (IID == KeyIID) return;
			if (!strlen(Password)) return;
			if (!Undefined::CheckValues(&KeyValue, Checkx)) return;
			KeyItem = *(DWORD *)(Undefined::GetValue(&KeyValue) + 4);
			IItem KeyLock((void*)KeyItem);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			int Recheck = 0, Check;
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Itemx = *(DWORD *)(Undefined::GetValue(&Value) + 4);
			IItem xItem((void*)Itemx);

			if (KeyLock.CheckIndex() == 1182)
			{
				CItem::RemoveItem((void*)Player, (int)KeyLock.GetOffset());
			} else {
				return;
			}

			if (xItem.CheckIndex() && (xItem.GetInfo() & 4194304))
			{
				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(xItem.GetIID(),GetDSS,GetStat,GetTime,Lock);

				if (Password != Lock)
				{
					IPlayer.BoxMsg("You typed wrong password. A Key is consumed.");
					return;
				}

				int CurrentInfo = *(DWORD *)(Itemx + 48) - 4194304;
				CDBSocket::Write(21, "dddbb",xItem.GetIID(),IPlayer.GetID(),xItem.GetInfo(),0,0);
				*(DWORD *)(Itemx + 48) = CurrentInfo;
				CDBSocket::Write(21, "dddbb",xItem.GetIID(),*(DWORD*)(Itemx + 28),CurrentInfo,8,7);
				CItem::SendItemInfo((void *)Itemx, (int)IPlayer.GetOffset(), 92);
				IPlayer.BoxMsg("You completely unlocked your item. A Key is consumed.");
				MainSvrT::UpdateItemTable(xItem.GetIID(),GetDSS,GetStat,GetTime,"0");
			} else {
				return;
			}

			return;
		}

		if (packet == 96)
		{
			void *xpPacket = pPacket;
			int xpPos = pPos, Npc = 0, Amount = 0, Index = 0, Item = 0, ValidItem = 0;
			unsigned __int16 ItemIndex = 0, ItemAmount = 0;
			unsigned __int8 Tax = 0, Size = 0;
			int NewpPacket = CPacket::Read((char*)xpPacket, (char*)xpPos, "dbb", &Npc, &Tax, &Size);

			if (Size > 0 && Size < 100)
			{
				for (int i = 0; *((DWORD*)Player + 274) && i < Size; i++)
				{
					NewpPacket = CPacket::Read((char*)NewpPacket, (char*)xpPos, (const char*)0x004BADB4, &ItemIndex, &ItemAmount);
					Amount = ItemAmount; Index = ItemIndex;

					if (Amount > 0 && HonorIndex.count(ItemIndex) && HonorIndex.find(ItemIndex)->second)
					{
						int TotalHonorRemove = Amount * HonorIndex.find(ItemIndex)->second;

						if (TotalHonorRemove > CheckHonor[IPlayer.GetPID()].RPx)
						{
							IPlayer.SystemMessage("You do not have enough reward points.",TEXTCOLOR_RED);
							return;
						} else {
							if (CPlayer::GetInvenSize(Player) >= IPlayer.MaxInventorySize())
							{
								IPlayer.SystemMessage("There is no free space on the storage.",TEXTCOLOR_RED);
								return;
							} else {
								Item = CItem::CreateItem(ItemIndex, 0, ItemAmount, -1);

								if (Item)
								{
									IItem IItem((void*)Item);
									ValidItem = CPlayer::_InsertItem(IPlayer.GetOffset(), 7, Item);

									if (ValidItem == 1)
									{
										CheckHonor[IPlayer.GetPID()].RPx -= TotalHonorRemove;
										*(DWORD*)(Item + 48) = 128;
										CDBSocket::Write(21, "dddbb",IItem.GetIID(),*(DWORD*)(Item + 28),128,8,7);
										CItem::SendItemInfo((void *)Item, (int)IPlayer.GetOffset(), 92);
									}
								}

								return;
							}
						}
					}
				}
			}
		}

		if (packet == 162)
		{
			int Type = 0, IID = 0, Value = 0, item = 0, ItemIndex = 0, JewelCheck = 0, Argument = 0, JewelAmount = 0;
			Interface<ITools> Tools; Tools->ParseData((char*)pPacket,"bddbwb",&Type, &IID, &ItemIndex, &JewelCheck, &Argument, &JewelAmount);

			if (Type == 1 && IID && ItemIndex)
			{
				CPlayer::Write(IPlayer.GetOffset(),0xFF,"dbd",240,1,1000);
				return;
			}

			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			int Recheck = 0, Check = 0, SetType = 200, JewelCalculation = 0;
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			item = *(DWORD *)(Undefined::GetValue(&Value) + 4);
			IItem MainItem((void*)item);
			if (SuitWearFix.count(MainItem.CheckIndex())) return;
			if (DecoWearFix.count(MainItem.CheckIndex())) return;
			if (ConfigWeaponSkins.count(MainItem.CheckIndex())) return;

			if (JewelCheck == 1 && JewelAmount >= 1 && JewelAmount <= 10)
			{
				int Multiply = 10;
				if (MainItem.GetType() >= 1 && MainItem.GetType() <= 6) Multiply = 5;

				if (CPlayer::FindItem(IPlayer.Offset, 3360, (Multiply*JewelAmount)))
				{
					CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 3360, (Multiply*JewelAmount));
					JewelCalculation = JewelAmount;
				} else {
					return;
				}
			}

			if (ItemIndex == 2365 && CPlayer::FindItem(IPlayer.Offset, 2365, 1))
			{
				int Type = 0, DssRate = CTools::Rate(1, 1000);

				if (DssRate >= (550-(JewelCalculation*50)))
				{
					if (DssRate >= (750-(JewelCalculation*60)))
					{
						if (DssRate >= (850-(JewelCalculation*60)))
						{
							if (DssRate >= (950-(JewelCalculation*70)))
							{
								if (DssRate >= (970-(JewelCalculation*70)))
								{
									if (DssRate >= (995-(JewelCalculation*80)))
									{
										if (DssRate >= (999-(JewelCalculation*80)))
										{
											int Rate = CTools::Rate(36,40);
											SetType += Rate;
											Type = 1;
											std::string msg = (std::string)IPlayer.GetName();
											msg = msg + " has been fused with the Spirit Of Insanity!";
											CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 8);
										} else {
											int Rate = CTools::Rate(31,35);
											SetType += Rate;
											Type = 2;
											std::string msg = (std::string)IPlayer.GetName();
											msg = msg + " has been fused with the Spirit Of Thunder!";
											CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 8);
										}	
									} else {
										int Rate = CTools::Rate(26,30);
										SetType += Rate;
										Type = 3;
									}
								} else {
									int Rate = CTools::Rate(21,25);
									SetType += Rate;	
									Type = 4;
								}
							} else {
								int Rate = CTools::Rate(16,20);
								SetType += Rate;
								Type = 5;
							}
						} else {
							int Rate = CTools::Rate(11,15);
							SetType += Rate;
							Type = 6;
						}
					} else {
						int Rate = CTools::Rate(6,10);
						SetType += Rate;
						Type = 7;
					}
				} else {
					int Rate = CTools::Rate(1,5);
					SetType += Rate;
					Type = 8;
				}		

				int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock;
				MainSvrT::ReadItemTable(IID,GetDSS,GetStat,GetTime,Lock);
				int SetDSS = MainItem.GetType() << 16 | SetType;
				MainSvrT::UpdateItemTable(MainItem.GetIID(),SetDSS,GetStat,GetTime,"0");
				CItem::SendItemInfo((void *)item, (int)IPlayer.GetOffset(), 92);
				CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 2365, 1);
				CPlayer::Write(IPlayer.GetOffset(),0xFF,"ddb",241,MainItem.GetIID(),Type);
				CPlayer::Write(IPlayer.GetOffset(),0xFF,"db",240,2);
				return;
			}

			return;
		}

		if (packet == 54)
		{
			char Type = 0; int Key = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &Type, &Key);

			if (CPlayer::GetInvenSize(Player) >= IPlayer.MaxInventorySize())
			{
				IPlayer.BoxMsg("Your inventory is full.");
				return;
			}

			if (MiningListMix.count(Key)) CQuest::Start(MiningListMix.find(Key)->second,(int)IPlayer.GetOffset());
			if (MiningListMakeItem.count(Key)) CQuest::Start(MiningListMakeItem.find(Key)->second,(int)IPlayer.GetOffset());
		}

		if (packet == 186)
		{
			char Type = 0; int Index = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "wb", &Index, &Type);

			if (Index && Type)
			{
				if (CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,1))
				{
					int Chance = 0, Type = 0;
					Chance = CTools::Rate(0,1000);
					if (Chance < 5) Type = 2;
					if (Chance < 200) Type = 1;

					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2524)
						CItem::InsertItem((int)Player,27,NormalPickaxe[Type][CTools::Rate(0,2)],0,1,-1);

					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2525)
						CItem::InsertItem((int)Player,27,BlueDragonPickaxe[Type][CTools::Rate(0,10)],0,1,-1);

					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2526)
					{
						CItem::InsertItem((int)Player,27,WhiteTigerPickaxe[Type][CTools::Rate(0,10)],0,1,-1);
						CItem::InsertItem((int)Player,27,WhiteTigerPickaxe[Type][CTools::Rate(0,10)],0,1,-1);
						CItem::InsertItem((int)Player,27,WhiteTigerPickaxe[Type][CTools::Rate(0,10)],0,1,-1);
					}

					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2527)
					{
						CItem::InsertItem((int)Player,27,RedBirdPickaxe[Type][CTools::Rate(0,10)],0,1,-1);
						if (CTools::Rate(0,2) == 2) CItem::InsertItem((int)Player,27,RedBirdPickaxe[Type][CTools::Rate(0,10)],0,1,-1);
					}

					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2528)
					{
						CItem::InsertItem((int)Player,27,BlackTorotisePickaxe[Type][CTools::Rate(0,10)],0,1,-1);
						CItem::InsertItem((int)Player,27,BlackTorotisePickaxe[Type][CTools::Rate(0,10)],0,1,-1);
					}

					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2529)
					{
						CItem::InsertItem((int)Player,27,MysteriousPickaxe[Type][CTools::Rate(0,10)],0,1,-1);
					}

					CheckMining[IPlayer.GetPID()].Cycle = CheckMining.find(IPlayer.GetPID())->second.Cycle + 1;
					int SetTheCycle = 0;
					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2524) SetTheCycle = 1;
					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2525) SetTheCycle = 6;
					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2526) SetTheCycle = 2;
					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2527) SetTheCycle = 6;
					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2528) SetTheCycle = 6;
					if (CheckMining.find(IPlayer.GetPID())->second.Index == 2529) SetTheCycle = 6;

					if (CheckMining.find(IPlayer.GetPID())->second.Cycle >= SetTheCycle)
					{
						if (CheckMining.find(IPlayer.GetPID())->second.Amount >= 1 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,CheckMining.find(IPlayer.GetPID())->second.Index,1))
						{
							CheckMining[IPlayer.GetPID()].Amount = CheckMining.find(IPlayer.GetPID())->second.Amount - 1;
							CheckMining[IPlayer.GetPID()].Cycle = 0;	
						} else {
							IPlayer.CloseWindow("minebar");
							CheckMining[IPlayer.GetPID()].Time = 0;
							CheckMining[IPlayer.GetPID()].Cycle = 0;
							CheckMining[IPlayer.GetPID()].Index = 0;
							CheckMining[IPlayer.GetPID()].Amount = 0;
							CPlayer::Write(IPlayer.GetOffset(),220,"bb",0,3);
							return;
						}
					}

					CPlayer::Write(IPlayer.GetOffset(),220,"bbw",0,4,CheckMining.find(IPlayer.GetPID())->second.Index);
					CheckMining[IPlayer.GetPID()].Time = GetTickCount() + 300000;
					return;
				} else {
					IPlayer.CloseWindow("minebar");
					CheckMining[IPlayer.GetPID()].Time = 0;
					CheckMining[IPlayer.GetPID()].Cycle = 0;
					CheckMining[IPlayer.GetPID()].Index = 0;
					CheckMining[IPlayer.GetPID()].Amount = 0;
					return;
				}
			}

			return;
		}

		if (packet == 118)
		{
			int Type = 0, IID = 0; char *Name; Interface<ITools> Tools;
			Tools->ParseData((char*)pPacket,"bds", &Type, &IID, &Name);
			int Recheck = 0, Check = 0, Value = 0, Item = 0;
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Item = *(DWORD *)(Undefined::GetValue(&Value) + 4);
			IItem Itemx((void*)Item);

			if (strlen(Name) > 0 && Item && Itemx.CheckIndex() == 1472)
			{
				void *MyPlayer = (void*)CPlayer::FindPlayerByName(Name);
				IChar Target(MyPlayer);

				if (Target.IsValid() && IPlayer.IsValid())
				{
					if (CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 1472, 1))
					{
						std::string getname = IPlayer.GetName();
						std::string send = getname + " will recall you to him(her). Do you accept the recall?";
						CPlayer::Write(Target.GetOffset(), 0xFF, "ddsss", 228, 1000, send.c_str(), IPlayer.GetName(), Target.GetName());
						IPlayer.Buff(304,1296000,0);
						IPlayer.SystemMessage("Recall scroll used.",TEXTCOLOR_GREEN);
					}
				} else {
					std::string getname = Name;
					std::string send = getname + " is not valid or offline.";
					IPlayer.BoxMsg(send);
				}

				if (MyPlayer) CSkill::ObjectRelease(MyPlayer, (int)MyPlayer + 352);
				return;
			}

			if (Itemx.CheckIndex() == 1473)
			{
				if (IPlayer.IsValid() && IPlayer.IsParty())
				{
					IPlayer.SystemMessage("Recall scroll used.",TEXTCOLOR_GREEN);
					IPlayer.Buff(304,1296000,0);

					if (CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 1473, 1))
					{
						int Party = CParty::FindParty(IPlayer.GetPartyID());

						for (int i = CParty::GetPlayerList((void*)Party); i; i = CBaseList::Pop((void *)i))
						{
							IChar Member((void*)*(DWORD*)((void*)i));

							if (Member.IsValid() && IPlayer.IsValid() && Member.GetOffset() != IPlayer.GetOffset())
							{
								std::string getname = IPlayer.GetName();
								std::string send = getname + " will recall you to him(her). Do you accept the recall?";
								CPlayer::Write(Member.GetOffset(), 0xFF, "ddsss", 228, 1000, send.c_str(), IPlayer.GetName(), Member.GetName());
							}
						}

						if (Party) CIOObject::Release((void*)Party);
					}
				} else {
					IPlayer.BoxMsg("You are not in party.");
				}

				return;
			}

			if (Itemx.CheckIndex() == 1474)
			{
				if (IPlayer.IsValid() && IPlayer.GetGID())
				{
					IPlayer.SystemMessage("Recall scroll used.",TEXTCOLOR_GREEN);
					IPlayer.Buff(304,1296000,0);

					if (CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 1474, 1))
					{
						CIOCriticalSection::Enter((void*)0x004e2078);
						CIOCriticalSection::Enter((void*)0x004e2098);
						CLink::MoveTo((void*)0x004e200c,(int)0x004e2004);
						CIOCriticalSection::Leave((void*)0x004e2098);
						for (DWORD i = *(DWORD*)0x004E2004; i != 0x004E2004; i = *(DWORD*)i)
						{
							if ((void*)(i - 428))
							{
								IChar Check((void*)(i - 428));

								if (Check.IsValid() && Check.GetGID() && Check.GetGID() == IPlayer.GetGID() && Check.GetOffset() != IPlayer.GetOffset())
								{
									std::string getname = IPlayer.GetName();
									std::string send = getname + " will recall you to him(her). Do you accept the recall?";
									CPlayer::Write(Check.GetOffset(), 0xFF, "ddsss", 228, 1000, send.c_str(), IPlayer.GetName(), Check.GetName());
								}
							}
						}
					}
				} else {
					IPlayer.BoxMsg("You do not have a guild.");
				}

				return;
			}

			return;
		}

		if (packet == 17 && Mute.count(IPlayer.GetPID()) && Mute.find(IPlayer.GetPID())->second)
		{
			if (Mute.find(IPlayer.GetPID())->second > GetTickCount())
			{
				std::string msg = "You are muted. You can talk again in " + Int2String(IPlayer.GetBuffRemain(164)) + " seconds.";
				IPlayer.BoxMsg(msg);
				return;
			} else {
				Mute.erase(IPlayer.GetPID());
			}
		}

		if (packet == 35 || packet == 45 || packet == 81 || packet == 44 || packet == 80 || packet == 85 || packet == 64 || packet == 52 || packet == 79)
		{
			if (IPlayer.IsBuff(285)) return;
			IPlayer.Buff(285,2,0);
		}

		if (packet == 45)
		{
			char Type = 0, nID = 0; CPacket::Read((char*)pPacket,(char*)pPos,"bb",&Type,&nID);

			if (Type == 1 && nID >= 0 && nID <= 5)
			{
				if (IPlayer.IsOnline() && !IPlayer.IsParty() && IPlayer.IsBuff(120))
				{
					int AsadalStat = IPlayer.GetBuffValue(429);
					
					if (nID == 0 && !(AsadalStat & (1 << 0)) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,150000))
					{
						AsadalStat = AsadalStat | (1 << 0);
						IPlayer.Buff(429,1800,AsadalStat);
						IPlayer.AddStr(5);
						IPlayer.SystemMessage("You received Energy of Asadal : Belligerence(Str+5)",TEXTCOLOR_INFOMSG);
						return;
					}

					if (nID == 1 && !(AsadalStat & (1 << 1)) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,150000))
					{
						AsadalStat = AsadalStat | (1 << 1);
						IPlayer.Buff(429,1800,AsadalStat);
						IPlayer.AddAgi(5);
						IPlayer.SystemMessage("You received Energy of Asadal : Determination(Agi+5)",TEXTCOLOR_INFOMSG);
						return;
					}

					if (nID == 2 && !(AsadalStat & (1 << 2)) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,150000))
					{
						AsadalStat = AsadalStat | (1 << 2);
						IPlayer.Buff(429,1800,AsadalStat);
						IPlayer.AddWis(10);
						IPlayer.SystemMessage("You received Energy of Asadal : Virtuality(Wis+10)",TEXTCOLOR_INFOMSG);
						return;
					}

					if (nID == 3 && !(AsadalStat & (1 << 3)) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,150000))
					{
						AsadalStat = AsadalStat | (1 << 3);
						IPlayer.Buff(429,1800,AsadalStat);
						IPlayer.AddInt(5);
						IPlayer.SystemMessage("You received Energy of Asadal : Quintessence(Int+5)",TEXTCOLOR_INFOMSG);
						return;
					}

					if (nID == 4 && !(AsadalStat & (1 << 4)) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,150000))
					{
						AsadalStat = AsadalStat | (1 << 4);
						IPlayer.Buff(429,1800,AsadalStat);
						IPlayer.AddHp(10);
						IPlayer.SystemMessage("You received Energy of Asadal : Vitality(Hth+10)",TEXTCOLOR_INFOMSG);
						return;
					}

					if (nID == 5 && !(AsadalStat & (1 << 5)) && CPlayer::RemoveItem(IPlayer.GetOffset(),9,31,500000))
					{
						AsadalStat = AsadalStat | (1 << 5);
						IPlayer.Buff(429,1800,AsadalStat);
						IPlayer.AddStr(5);
						IPlayer.AddAgi(5);
						IPlayer.AddWis(5);
						IPlayer.AddInt(5);
						IPlayer.AddHp(5);
						IPlayer.SystemMessage("You received Energy of Asadal : Enlightenment(All Attributes+5)",TEXTCOLOR_INFOMSG);
						return;
					}

					return;
				}
			}
		}

		if (packet == 34)
		{
			char Type = 0; int Value = 0; CPacket::Read((char*)pPacket,(char*)pPos,"bd",&Type,&Value);
			
			if (Type == 2 && Value >= 65536 && IPlayer.IsOnline())
			{
				int RegisterCount = MainSvrT::CheckAuctionHouse(IPlayer.GetPID());
				int Page = Value >> 16;

				if (!RegisterCount)
				{
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwb",2,0,1,0);
				} else {
					int IID = 0, Index = 0, Prefix = 0, Info = 0, Amount = 0, MaxEnd = 0, CurEnd = 0, SetGem = 0, XAtk = 0, XMag = 0, XDef = 0;
					int Upgrl = 0, Upgrr = 0, Time = 0, Bid = 0, Buy = 0, XHit = 0, XEva = 0; std::string Seller, Bidder; sqlite3_stmt *stmt;
					char *RealAuctionPacket = new char[8000]; Interface<ITools> Tools; size_t nSize = 0; int xItemCount = 0;
					std::stringstream Query; Query << "SELECT * FROM Auction WHERE PID = '" << IPlayer.GetPID() << "' LIMIT 8 OFFSET '" << (8 * (Page - 1)) << "'";

					if (sqlite3_prepare_v2(R3volutioN,Query.str().c_str(),-1,&stmt,0) == SQLITE_OK)
					{
						while (sqlite3_step(stmt) == SQLITE_ROW)
						{
							IID = sqlite3_column_int(stmt,0);
							Index = sqlite3_column_int(stmt,2);
							Prefix = sqlite3_column_int(stmt,3);
							Info = sqlite3_column_int(stmt,4);
							Amount = sqlite3_column_int(stmt,5);
							MaxEnd = sqlite3_column_int(stmt,6);
							CurEnd = sqlite3_column_int(stmt,7);
							SetGem = sqlite3_column_int(stmt,8);
							XAtk = sqlite3_column_int(stmt,9);
							XMag = sqlite3_column_int(stmt,10);
							XDef = sqlite3_column_int(stmt,11);
							XHit = sqlite3_column_int(stmt,12);
							XEva = sqlite3_column_int(stmt,13);
							Upgrl = sqlite3_column_int(stmt,14);
							Upgrr = sqlite3_column_int(stmt,15);
							Time = sqlite3_column_int(stmt,16);
							Bid = sqlite3_column_int(stmt,17);
							Buy = sqlite3_column_int(stmt,18);
							char *xxx = (char*)sqlite3_column_text(stmt,19);
							if (xxx != NULL) Seller = xxx; else Seller = "none";
							char *yyy = (char*)sqlite3_column_text(stmt,20);
							if (yyy != NULL) Bidder = yyy; else Bidder = "none";
							int RemainTime = (Time + (3600*6)) - (int)time(0);

							if (RemainTime > 0)
							{
								int Amount = 67 + strlen(Seller.c_str()) + 1 + strlen(Bidder.c_str()) + 1 + 2 + 4 + 4 + 4;
								unsigned long remaintime=0,QigongGrade=0; unsigned short phyatk=0,magatk=0,def=0,absorb=0;
								unsigned char prefix=0,x=0,y=0,z=0,dsstype=0,eva=0,otp=0,hpinc=0,mpinc=0,str=0,hp=0,intel=0,wis=0,agi=0,a=0,dg1stat=0,dg1type=0,dg2stat=0,dg2type=0,PerfShotCheck=0;
								ItemShow::GetItemStat(IPlayer.GetOffset(),IID,Index,prefix,x,y,z,dsstype,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,a,dg1stat,dg1type,dg2stat,dg2type,PerfShotCheck,remaintime,QigongGrade,phyatk,magatk,def,absorb);		
								if (Prefix > prefix) prefix = Prefix;
								char AuctionPacket[67] = {0}; ZeroMemory(AuctionPacket,67);
								Tools->Compile(AuctionPacket,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbd",Index,IID,prefix,Info,Amount,MaxEnd,CurEnd,SetGem,XAtk,XMag,XDef,XHit,XEva,0,Upgrl,Upgrr,x,y,z,remaintime,dsstype,phyatk,magatk,def,absorb,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,PerfShotCheck,QigongGrade,dg1stat,dg1type,a,dg2stat,dg2type,0);			
								Tools->Compile(RealAuctionPacket+nSize,"swdsdmd",Seller.c_str(),(RemainTime/3600),Buy,Bidder.c_str(),Bid,AuctionPacket,67,0);
								nSize += Amount; xItemCount++;
							} else {
								if (RemainTime < 0 && Bidder == "none")
								{
									int MakeItem = CItem::CreateItem(31,0,97000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,97000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,97000,0,"Auction closed without bidder");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),IID,0,0,Prefix,Amount,0,"Auction closed without bidder");
									MainSvrT::DeleteAuctionHouse(IID);
								}

								if (RemainTime < 0 && Bidder != "none")
								{
									int MakeItem = CItem::CreateItem(31,0,Bid+100000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,Bid+100000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,Bid+100000,0,"Auction ended");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Bidder.c_str(),IID,0,0,Prefix,Amount,0,"You won the auction");
									MainSvrT::DeleteAuctionHouse(IID);
								}
							}
						}
					}

					sqlite3_finalize(stmt);
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwbm",2,RegisterCount,Page,xItemCount,RealAuctionPacket,8000);
					delete[] RealAuctionPacket;
				}

				return;
			}

			if (Type == 1 && Value >= 65536 && IPlayer.IsOnline())
			{
				int BidCount = MainSvrT::CheckAuctionHouseBid(IPlayer.GetName());
				int Page = Value >> 16;

				if (!BidCount)
				{
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwb",1,0,1,0);
				} else {
					int IID = 0, Index = 0, Prefix = 0, Info = 0, Amount = 0, MaxEnd = 0, CurEnd = 0, SetGem = 0, XAtk = 0, XMag = 0, XDef = 0;
					int Upgrl = 0, Upgrr = 0, Time = 0, Bid = 0, Buy = 0, XHit = 0, XEva = 0; std::string Seller, Bidder; sqlite3_stmt *stmt;
					char *RealAuctionPacket = new char[8000]; Interface<ITools> Tools; size_t nSize = 0; int xItemCount = 0; std::stringstream Select;
					std::stringstream Query; Query << "SELECT * FROM Auction WHERE Bidder = '" << IPlayer.GetName() << "' LIMIT 8 OFFSET '" << (8 * (Page - 1)) << "'";

					if (sqlite3_prepare_v2(R3volutioN,Query.str().c_str(),-1,&stmt,0) == SQLITE_OK)
					{
						while (sqlite3_step(stmt) == SQLITE_ROW)
						{
							IID = sqlite3_column_int(stmt,0);
							Index = sqlite3_column_int(stmt,2);
							Prefix = sqlite3_column_int(stmt,3);
							Info = sqlite3_column_int(stmt,4);
							Amount = sqlite3_column_int(stmt,5);
							MaxEnd = sqlite3_column_int(stmt,6);
							CurEnd = sqlite3_column_int(stmt,7);
							SetGem = sqlite3_column_int(stmt,8);
							XAtk = sqlite3_column_int(stmt,9);
							XMag = sqlite3_column_int(stmt,10);
							XDef = sqlite3_column_int(stmt,11);
							XHit = sqlite3_column_int(stmt,12);
							XEva = sqlite3_column_int(stmt,13);
							Upgrl = sqlite3_column_int(stmt,14);
							Upgrr = sqlite3_column_int(stmt,15);
							Time = sqlite3_column_int(stmt,16);
							Bid = sqlite3_column_int(stmt,17);
							Buy = sqlite3_column_int(stmt,18);
							char *xxx = (char*)sqlite3_column_text(stmt,19);
							if (xxx != NULL) Seller = xxx; else Seller = "none";
							char *yyy = (char*)sqlite3_column_text(stmt,20);
							if (yyy != NULL) Bidder = yyy; else Bidder = "none";
							int RemainTime = (Time + (3600*6)) - (int)time(0);

							if (RemainTime > 0)
							{
								int Amount = 67 + strlen(Seller.c_str()) + 1 + strlen(Bidder.c_str()) + 1 + 2 + 4 + 4 + 4;
								unsigned long remaintime=0,QigongGrade=0; unsigned short phyatk=0,magatk=0,def=0,absorb=0;
								unsigned char prefix=0,x=0,y=0,z=0,dsstype=0,eva=0,otp=0,hpinc=0,mpinc=0,str=0,hp=0,intel=0,wis=0,agi=0,a=0,dg1stat=0,dg1type=0,dg2stat=0,dg2type=0,PerfShotCheck=0;
								ItemShow::GetItemStat(IPlayer.GetOffset(),IID,Index,prefix,x,y,z,dsstype,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,a,dg1stat,dg1type,dg2stat,dg2type,PerfShotCheck,remaintime,QigongGrade,phyatk,magatk,def,absorb);		
								if (Prefix > prefix) prefix = Prefix;
								char AuctionPacket[67] = {0}; ZeroMemory(AuctionPacket,67);
								Tools->Compile(AuctionPacket,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbd",Index,IID,prefix,Info,Amount,MaxEnd,CurEnd,SetGem,XAtk,XMag,XDef,XHit,XEva,0,Upgrl,Upgrr,x,y,z,remaintime,dsstype,phyatk,magatk,def,absorb,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,PerfShotCheck,QigongGrade,dg1stat,dg1type,a,dg2stat,dg2type,0);			
								Tools->Compile(RealAuctionPacket+nSize,"swdsdmd",Seller.c_str(),(RemainTime/3600),Buy,Bidder.c_str(),Bid,AuctionPacket,67,0);
								nSize += Amount; xItemCount++;
							} else {
								if (RemainTime < 0 && Bidder == "none")
								{
									int MakeItem = CItem::CreateItem(31,0,97000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,97000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,97000,0,"Auction closed without bidder");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),IID,0,0,Prefix,Amount,0,"Auction closed without bidder");
									MainSvrT::DeleteAuctionHouse(IID);
								}

								if (RemainTime < 0 && Bidder != "none")
								{
									int MakeItem = CItem::CreateItem(31,0,Bid+100000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,Bid+100000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,Bid+100000,0,"Auction ended");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Bidder.c_str(),IID,0,0,Prefix,Amount,0,"You won the auction");
									MainSvrT::DeleteAuctionHouse(IID);
								}
							}
						}
					}

					sqlite3_finalize(stmt);
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwbm",1,BidCount,Page,xItemCount,RealAuctionPacket,8000);
					delete[] RealAuctionPacket;
				}

				return;
			}

			if (Type == 0 && Value >= 65536 && IPlayer.IsOnline())
			{
				int ItemCount = MainSvrT::CountAuctionHouse();
				int Page = Value >> 16;

				if (!ItemCount)
				{
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwb",0,0,1,0);
				} else {
					int IID = 0, Index = 0, Prefix = 0, Info = 0, Amount = 0, MaxEnd = 0, CurEnd = 0, SetGem = 0, XAtk = 0, XMag = 0, XDef = 0;
					int Upgrl = 0, Upgrr = 0, Time = 0, Bid = 0, Buy = 0, XHit = 0, XEva = 0; std::string Seller, Bidder; sqlite3_stmt *stmt;
					char *RealAuctionPacket = new char[8000]; Interface<ITools> Tools; size_t nSize = 0; int xItemCount = 0;
					std::stringstream Query; Query << "SELECT * FROM Auction LIMIT 8 OFFSET '" << (8 * (Page - 1)) << "'";

					if (sqlite3_prepare_v2(R3volutioN,Query.str().c_str(),-1,&stmt,0) == SQLITE_OK)
					{
						while (sqlite3_step(stmt) == SQLITE_ROW)
						{
							IID = sqlite3_column_int(stmt,0);
							Index = sqlite3_column_int(stmt,2);
							Prefix = sqlite3_column_int(stmt,3);
							Info = sqlite3_column_int(stmt,4);
							Amount = sqlite3_column_int(stmt,5);
							MaxEnd = sqlite3_column_int(stmt,6);
							CurEnd = sqlite3_column_int(stmt,7);
							SetGem = sqlite3_column_int(stmt,8);
							XAtk = sqlite3_column_int(stmt,9);
							XMag = sqlite3_column_int(stmt,10);
							XDef = sqlite3_column_int(stmt,11);
							XHit = sqlite3_column_int(stmt,12);
							XEva = sqlite3_column_int(stmt,13);
							Upgrl = sqlite3_column_int(stmt,14);
							Upgrr = sqlite3_column_int(stmt,15);
							Time = sqlite3_column_int(stmt,16);
							Bid = sqlite3_column_int(stmt,17);
							Buy = sqlite3_column_int(stmt,18);
							char *xxx = (char*)sqlite3_column_text(stmt,19);
							if (xxx != NULL) Seller = xxx; else Seller = "none";
							char *yyy = (char*)sqlite3_column_text(stmt,20);
							if (yyy != NULL) Bidder = yyy; else Bidder = "none";
							int RemainTime = (Time + (3600*6)) - (int)time(0);
							
							if (RemainTime > 0)
							{
								int Amount = 67 + strlen(Seller.c_str()) + 1 + strlen(Bidder.c_str()) + 1 + 2 + 4 + 4 + 4;
								unsigned long remaintime=0,QigongGrade=0; unsigned short phyatk=0,magatk=0,def=0,absorb=0;
								unsigned char prefix=0,x=0,y=0,z=0,dsstype=0,eva=0,otp=0,hpinc=0,mpinc=0,str=0,hp=0,intel=0,wis=0,agi=0,a=0,dg1stat=0,dg1type=0,dg2stat=0,dg2type=0,PerfShotCheck=0;
								ItemShow::GetItemStat(IPlayer.GetOffset(),IID,Index,prefix,x,y,z,dsstype,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,a,dg1stat,dg1type,dg2stat,dg2type,PerfShotCheck,remaintime,QigongGrade,phyatk,magatk,def,absorb);		
								if (Prefix > prefix) prefix = Prefix;
								char AuctionPacket[67] = {0}; ZeroMemory(AuctionPacket,67);
								Tools->Compile(AuctionPacket,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbd",Index,IID,prefix,Info,Amount,MaxEnd,CurEnd,SetGem,XAtk,XMag,XDef,XHit,XEva,0,Upgrl,Upgrr,x,y,z,remaintime,dsstype,phyatk,magatk,def,absorb,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,PerfShotCheck,QigongGrade,dg1stat,dg1type,a,dg2stat,dg2type,0);			
								Tools->Compile(RealAuctionPacket+nSize,"swdsdmd",Seller.c_str(),(RemainTime/3600),Buy,Bidder.c_str(),Bid,AuctionPacket,67,0);
								nSize += Amount; xItemCount++;
							} else {
								if (RemainTime < 0 && Bidder == "none")
								{
									int MakeItem = CItem::CreateItem(31,0,97000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,97000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,97000,0,"Auction closed without bidder");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),IID,0,0,Prefix,Amount,0,"Auction closed without bidder");
									MainSvrT::DeleteAuctionHouse(IID);
								}

								if (RemainTime < 0 && Bidder != "none")
								{
									int MakeItem = CItem::CreateItem(31,0,Bid+100000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,Bid+100000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,Bid+100000,0,"Auction ended");
									}
								
									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Bidder.c_str(),IID,0,0,Prefix,Amount,0,"You won the auction");
									MainSvrT::DeleteAuctionHouse(IID);
								}
							}
						}
					}

					sqlite3_finalize(stmt);
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwbm",0,ItemCount,Page,xItemCount,RealAuctionPacket,8000);
					delete[] RealAuctionPacket;
				}

				return;
			}
		}

		if (packet == 38)
		{
			int Type = 0, IID = 0, Bid = 0; char a = 0, b = 0; 
			CPacket::Read((char*)pPacket,(char*)pPos,"ddbbd",&Type,&IID,&a,&b,&Bid);

			if (IPlayer.IsOnline() && IID && Bid)
			{
				int PID = 0, Buy = 0, Bidx = 0; std::string Seller, Bidder;
				MainSvrT::CheckAuctionHouseBidAndBuy(IID,PID,Bidx,Buy,Seller,Bidder);
				if (IPlayer.GetPID() == PID) return;
				if (!PID) return; if (!Bidx) return;

				if (Bid <= Bidx)
				{
					CPlayer::Write(IPlayer.GetOffset(),254,"b",13);
					return;
				}

				if (Bid >= Buy)
				{
					IPlayer.SystemMessage("Bid price can not be equal or higher then buy now price!", TEXTCOLOR_RED);
					return;
				}

				if ((Buy - 1000) == Bidx)
				{
					CPlayer::Write(IPlayer.GetOffset(),254,"b",17);
					return;
				}

				if (Bidder == IPlayer.GetName())
				{
					IPlayer.SystemMessage("You already placed a bid!", TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.IsOnline() && (!*(DWORD*)(Player + 1096) || *(DWORD*)(*(DWORD*)(Player + 1096) + 52) < Bid))
				{
					IPlayer.SystemMessage("You do not have enough money!", TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.IsOnline() && !(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(**((DWORD**)Player + 274) + 120))(*((DWORD*)Player + 274),(void*)Player,9,-Bid)) return;

				if (Bidder != "none")
				{
					int MakeItem = CItem::CreateItem(31,0,Bidx,-1);

					if (MakeItem)
					{
						LONG NewIID = CItem::NewIID();
						CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,Bidx,0,0,0,1);
						CBase::Delete((void*)MakeItem);
						CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Bidder.c_str(),NewIID,0,0,0,Bidx,0,"Other player placed higher bid");
					}
				}

				MainSvrT::UpdateAuctionHouseBidAndBuy(IID,Bid,IPlayer.GetName());
				IPlayer.SystemMessage("You have successfully bid!", TEXTCOLOR_GREEN);
				return;
			}
		}

		if (packet == 66)
		{
			int Type = 0, IID = 0;
			CPacket::Read((char*)pPacket,(char*)pPos,"dd",&Type,&IID);
			
			if (IID)
			{
				int PID = 0, Buy = 0, Bidx = 0; std::string Seller, Bidder;
				MainSvrT::CheckAuctionHouseBidAndBuy(IID,PID,Bidx,Buy,Seller,Bidder);
				if (IPlayer.GetPID() == PID) return;
				if (!PID) return; if (!Bidx) return;

				if (IPlayer.IsOnline() && (!*(DWORD*)(Player + 1096) || *(DWORD*)(*(DWORD*)(Player + 1096) + 52) < Buy))
				{
					IPlayer.SystemMessage("You do not have enough money!", TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.IsOnline() && !(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(**((DWORD**)Player + 274) + 120))(*((DWORD*)Player + 274),(void*)Player,9,-Buy)) return;

				if (Bidder != "none")
				{
					int MakeItem = CItem::CreateItem(31,0,Bidx,-1);

					if (MakeItem)
					{
						LONG NewIID = CItem::NewIID();
						CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,Bidx,0,0,0,1);
						CBase::Delete((void*)MakeItem);
						CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Bidder.c_str(),NewIID,0,0,0,Bidx,0,"Other player bought the item");
					}
				}

				if (strlen(Seller.c_str()))
				{
					__int64 ReturnValue = Buy - (Buy / 20);
					int MakeItem = CItem::CreateItem(31,0,ReturnValue+100000,-1);

					if (MakeItem)
					{
						LONG NewIID = CItem::NewIID();
						CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,ReturnValue+100000,0,0,0,1);
						CBase::Delete((void*)MakeItem);
						CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,(int)ReturnValue+100000,0,"Item has been sold at auction");
					}
				}

				int Index = 0, Prefix = 0, Info = 0, Amount = 0, MaxEnd = 0, CurEnd = 0, SetGem = 0, XAtk = 0, XMag = 0, XDef = 0, XHit = 0, XEva = 0;
				int Upgrl = 0, Upgrr = 0, Time = 0, aBid = 0, aBuy = 0; std::string aSeller, aBidder;
				MainSvrT::ReadAuctionHouseRegister(IID,Index,Prefix,Info,Amount,MaxEnd,CurEnd,SetGem,XAtk,XMag,XDef,XHit,XEva,Upgrl,Upgrr,Time,aBid,aBuy,aSeller,aBidder);			
				if (IPlayer.IsOnline()) CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",IPlayer.GetName(),IID,0,0,Prefix,Amount,0,"Auction house buy now");
				MainSvrT::DeleteAuctionHouse(IID);
				IPlayer.SystemMessage("BUY NOW! has succeeded.", TEXTCOLOR_GREEN);
				return;
			}
		}

		if (packet == 88)
		{
			int IID = 0, Value = 0, Item = 0, Recheck = 0, Bid = 0, BuyNow = 0, Arg = 0;
			CPacket::Read((char*)pPacket,(char*)pPos,"dwdd",&IID,&Arg,&Bid,&BuyNow);
			Undefined::CreateMonsterValue((char*)Player + 1068,(int)&Value,(int)&IID);
			int Check = Undefined::Check((int)((char*)Player + 1068), (int)&Recheck);
			
			if (Undefined::CheckValues(&Value,Check))
			{	
				Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);
				IItem AUR((void*)Item);
				if (Bid >= BuyNow) return;
				if (AUR.GetInfo() & 128) return;
				if (AUR.GetInfo() & 4194304) return;			
				if (ItemLifeCheck.count(AUR.GetIID())) return;
				if (Bid > 2100000000 || BuyNow > 2100000000) return;
				if (IPlayer.IsOnline() && (!*(DWORD*)(Player + 1096) || *(DWORD*)(*(DWORD*)(Player + 1096) + 52) < 100000)) return;
				if (IPlayer.IsOnline() && !(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(**((DWORD**)Player + 274) + 120))(*((DWORD*)Player + 274),(void*)Player,9,-100000)) return;		
				Interface<ITools> Tools; char AuctionPacket[67] = {0}; ZeroMemory(AuctionPacket,67);
				unsigned long remaintime=0,QigongGrade=0; unsigned short phyatk=0,magatk=0,def=0,absorb=0;
				unsigned char prefix=0,x=0,y=0,z=0,dsstype=0,eva=0,otp=0,hpinc=0,mpinc=0,str=0,hp=0,intel=0,wis=0,agi=0,a=0,dg1stat=0,dg1type=0,dg2stat=0,dg2type=0,PerfShotCheck=0;
				ItemShow::GetItemStat(IPlayer.GetOffset(),AUR.GetIID(),AUR.CheckIndex(),prefix,x,y,z,dsstype,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,a,dg1stat,dg1type,dg2stat,dg2type,PerfShotCheck,remaintime,QigongGrade,phyatk,magatk,def,absorb);		
				if (AUR.PrefixID() > prefix) prefix = AUR.PrefixID();	
				Tools->Compile(AuctionPacket,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbd",AUR.CheckIndex(),AUR.GetIID(),prefix,AUR.GetInfo(),AUR.GetAmount(),*(DWORD*)(Item + 92),AUR.GetEndurance(),*(DWORD*)(Item + 84),*(DWORD*)(Item + 100),*(DWORD*)(Item + 104),*(DWORD*)(Item + 108),*(DWORD*)(Item + 112),*(DWORD*)(Item + 116),0,*(DWORD*)(Item + 124),*(DWORD*)(Item + 120),x,y,z,remaintime,dsstype,phyatk,magatk,def,absorb,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,PerfShotCheck,QigongGrade,dg1stat,dg1type,a,dg2stat,dg2type,0);
				MainSvrT::UpdateAuctionHouse(AUR.GetIID(),IPlayer.GetPID(),AUR.CheckIndex(),prefix,AUR.GetInfo(),AUR.GetAmount(),*(DWORD*)(Item + 92),AUR.GetEndurance(),*(DWORD*)(Item + 84),*(DWORD*)(Item + 100),*(DWORD*)(Item + 104),*(DWORD*)(Item + 108),*(DWORD*)(Item + 112),*(DWORD*)(Item + 116),*(DWORD*)(Item + 124),*(DWORD*)(Item + 120),(int)time(0),Bid,BuyNow,IPlayer.GetName(),"none");
				CPlayer::Write(IPlayer.GetOffset(),8,"db",IID,11);
				CDBSocket::Write(7,"dddwbd",IID,IPlayer.GetPID(),0,AUR.CheckIndex(),11,AUR.GetAmount());
				CPlayer::Write(IPlayer.GetOffset(),254,"b",1);
				int RegisterCount = MainSvrT::CheckAuctionHouse(IPlayer.GetPID());

				if (!RegisterCount)
				{
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwb",2,0,1,0);
				} else {
					int IID = 0, Index = 0, Prefix = 0, Info = 0, Amount = 0, MaxEnd = 0, CurEnd = 0, SetGem = 0, XAtk = 0, XMag = 0, XDef = 0;
					int Upgrl = 0, Upgrr = 0, Time = 0, Bid = 0, Buy = 0, XHit = 0, XEva = 0; std::string Seller, Bidder; sqlite3_stmt *stmt;
					char *RealAuctionPacket = new char[8000]; Interface<ITools> Tools; size_t nSize = 0; int xItemCount = 0;
					std::stringstream Query; Query << "SELECT * FROM Auction WHERE PID = '" << IPlayer.GetPID() << "' LIMIT 8 OFFSET '" << 0 << "'";

					if (sqlite3_prepare_v2(R3volutioN,Query.str().c_str(),-1,&stmt,0) == SQLITE_OK)
					{
						while (sqlite3_step(stmt) == SQLITE_ROW)
						{
							IID = sqlite3_column_int(stmt,0);
							Index = sqlite3_column_int(stmt,2);
							Prefix = sqlite3_column_int(stmt,3);
							Info = sqlite3_column_int(stmt,4);
							Amount = sqlite3_column_int(stmt,5);
							MaxEnd = sqlite3_column_int(stmt,6);
							CurEnd = sqlite3_column_int(stmt,7);
							SetGem = sqlite3_column_int(stmt,8);
							XAtk = sqlite3_column_int(stmt,9);
							XMag = sqlite3_column_int(stmt,10);
							XDef = sqlite3_column_int(stmt,11);
							XHit = sqlite3_column_int(stmt,12);
							XEva = sqlite3_column_int(stmt,13);
							Upgrl = sqlite3_column_int(stmt,14);
							Upgrr = sqlite3_column_int(stmt,15);
							Time = sqlite3_column_int(stmt,16);
							Bid = sqlite3_column_int(stmt,17);
							Buy = sqlite3_column_int(stmt,18);
							char *xxx = (char*)sqlite3_column_text(stmt,19);
							if (xxx != NULL) Seller = xxx; else Seller = "none";
							char *yyy = (char*)sqlite3_column_text(stmt,20);
							if (yyy != NULL) Bidder = yyy; else Bidder = "none";
							int RemainTime = (Time + (3600*6)) - (int)time(0);

							if (RemainTime > 0)
							{
								int Amount = 67 + strlen(Seller.c_str()) + 1 + strlen(Bidder.c_str()) + 1 + 2 + 4 + 4 + 4;
								unsigned long remaintime=0,QigongGrade=0; unsigned short phyatk=0,magatk=0,def=0,absorb=0;
								unsigned char prefix=0,x=0,y=0,z=0,dsstype=0,eva=0,otp=0,hpinc=0,mpinc=0,str=0,hp=0,intel=0,wis=0,agi=0,a=0,dg1stat=0,dg1type=0,dg2stat=0,dg2type=0,PerfShotCheck=0;
								ItemShow::GetItemStat(IPlayer.GetOffset(),IID,Index,prefix,x,y,z,dsstype,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,a,dg1stat,dg1type,dg2stat,dg2type,PerfShotCheck,remaintime,QigongGrade,phyatk,magatk,def,absorb);		
								if (Prefix > prefix) prefix = Prefix;
								char AuctionPacket[67] = {0}; ZeroMemory(AuctionPacket,67);
								Tools->Compile(AuctionPacket,"wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbd",Index,IID,prefix,Info,Amount,MaxEnd,CurEnd,SetGem,XAtk,XMag,XDef,XHit,XEva,0,Upgrl,Upgrr,x,y,z,remaintime,dsstype,phyatk,magatk,def,absorb,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,PerfShotCheck,QigongGrade,dg1stat,dg1type,a,dg2stat,dg2type,0);			
								Tools->Compile(RealAuctionPacket+nSize,"swdsdmd",Seller.c_str(),(RemainTime/3600),Buy,Bidder.c_str(),Bid,AuctionPacket,67,0);
								nSize += Amount; xItemCount++;
							} else {
								if (RemainTime < 0 && Bidder == "none")
								{
									int MakeItem = CItem::CreateItem(31,0,97000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,97000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,97000,0,"Auction closed without bidder");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),IID,0,0,Prefix,Amount,0,"Auction closed without bidder");
									MainSvrT::DeleteAuctionHouse(IID);
								}

								if (RemainTime < 0 && Bidder != "none")
								{
									int MakeItem = CItem::CreateItem(31,0,Bid+100000,-1);

									if (MakeItem)
									{
										LONG NewIID = CItem::NewIID();
										CDBSocket::Write(6,"dwbddbbdb",NewIID,31,0,0,Bid+100000,0,0,0,1);
										CBase::Delete((void*)MakeItem);
										CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Seller.c_str(),NewIID,0,0,0,Bid+100000,0,"Auction ended");
									}

									CDBSocket::Write(30,"dbdbbssdbwbdds",-1,0,-1,0,1,"Auction House",Bidder.c_str(),IID,0,0,Prefix,Amount,0,"You won the auction");
									MainSvrT::DeleteAuctionHouse(IID);
								}
							}
						}
					}

					sqlite3_finalize(stmt);
					CPlayer::Write(IPlayer.GetOffset(),253,"bdwbm",2,RegisterCount,1,xItemCount,RealAuctionPacket,8000);
					delete[] RealAuctionPacket;
				}

				return;
			}
		}

		if (packet == 75 && IPlayer.GetClass() == 3)
		{
			int SkillID = 0;
			CPacket::Read((char*)pPacket, (char*)pPos,"b", &SkillID);
			int SkillPointer = IPlayer.GetSkillPointer(SkillID);

			if (SkillPointer && SkillID < 90 && SkillID != 79 && SkillID != 37 && SkillID != 50 && SkillID != 70 && SkillID != 87 && SkillID != 88 && SkillID != 89 && (CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 362, 1) || CPlayer::RemoveItem(IPlayer.GetOffset(), 9, 502, 1)))
			{
				if (IPlayer.IsOnline() && IPlayer.GetClass() == 3 && SkillID == 4)
				{
					IPlayer.DecreaseCritRate(*(DWORD*)(SkillPointer + 8));
					IPlayer.DecreaseCritDamage(*(DWORD*)(SkillPointer + 8));
				}

				if (IPlayer.IsOnline() && IPlayer.GetClass() == 3 && SkillID == 5)
				{
					IPlayer.DecreaseCritRate(*(DWORD*)(SkillPointer + 8));
					IPlayer.DecreaseCritDamage(*(DWORD*)(SkillPointer + 8));
				}

				IPlayer.AddSkillPoint(*(DWORD*)(SkillPointer + 8));
				*(DWORD*)(SkillPointer + 8) = 0;
				CPlayer::Write(IPlayer.GetOffset(),11,"bb",*(DWORD*)(SkillPointer + 4),0);
				CDBSocket::Write(22,"dbbw",IPlayer.GetPID(),*(DWORD*)(SkillPointer + 4),*(DWORD*)(SkillPointer + 8),*(DWORD*)((int)IPlayer.GetOffset() + 548));
				*(DWORD*)((int)IPlayer.GetOffset() + 4 * *(DWORD*)(SkillPointer +4) + 632) = 0;
				return;
			} else {
				return;
			}
		}

		if (packet == 33)
		{
			int IID = 0, Recheck = 0, Check = 0, Value = 0, Item = 0;
			CPacket::Read((char*)pPacket, (char*)pPos,"d", &IID);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Item = *(DWORD*)(Undefined::GetValue(&Value) + 4);

			if (Item && IID)
			{
				IItem Itemx((void*)Item);

				if (Itemx.CheckIndex() == 1620)
				{
					if (IPlayer.GetLevel() < 70)
					{
						IPlayer.SystemMessage("Your character must be level 70 or above.",TEXTCOLOR_RED);
						return;
					}

					if (IPlayer.IsOnline() && LastManStand::Active == true && IPlayer.GetMap() == LMSMap && IPlayer.IsBuff(378)) return;
					if (IPlayer.IsOnline() && Protect32::Active == true && IPlayer.GetMap() == PLMap && (IPlayer.IsBuff(170) || IPlayer.IsBuff(171))) return;
					if (IPlayer.IsOnline() && Battlefield::Active == true && IPlayer.GetMap() == BFMap && (IPlayer.IsBuff(160) || IPlayer.IsBuff(161))) return;
					if (IPlayer.IsOnline() && Scenario::Active == true && IPlayer.GetMap() == ScenarioMap && IPlayer.GetRectX() >= 8915 && IPlayer.GetRectX() <= 8998 && !CChar::IsGState((int)IPlayer.GetOffset(),2)) return;
					if (IPlayer.IsValid()) IPlayer.Teleport(6, 360931, 187024);
					return;
				}
			}
		}

		if (packet == 63)
		{
			int IID = 0, Value = 0, Item = 0, Recheck = 0, Check;
			CPacket::Read((char*)pPacket, (char*)pPos, "d", &IID);
			Undefined::CreateMonsterValue((char *)Player + 1068, (int)&Value, (int)&IID);
			Check = Undefined::Check((int)((char *)Player + 1068), (int)&Recheck);
			if (!Undefined::CheckValues(&Value, Check)) return;
			Item = *(DWORD *)(Undefined::GetValue(&Value) + 4);

			if (Item && IID)
			{
				IItem Itemx((void*)Item);
				if (Itemx.GetInfo() & 4194304) return;
			}
		}

		if (packet == 61)
		{
			int ID = 0; char Type;
			CPacket::Read((char*)pPacket, (char*)pPos, "db", &ID, &Type);

			if (IPlayer.IsOnline() && IPlayer.GetID() != ID)
			{
				IPlayer.Kick();
				return;
			}
		}

		if (packet == 85)
		{
			IPlayer.SystemMessage("Student system disabled!", TEXTCOLOR_RED); return;
			int ID = 0; unsigned __int8 Type = 0; char Kind = 0;
			int Check = CPacket::Read((char*)pPacket, (char*)pPos, "b", &Type);

			if (Type == 1)
			{
				CPacket::Read((char*)Check, (char*)pPos, "db", &ID, &Kind);
				void *GetPlayer = CPlayer::FindPlayer(ID);

				if (GetPlayer)
				{
					IChar Player(GetPlayer);

					if (Player.IsOnline())
					{
						if (Player.GetLevel() > 15)
						{
							IPlayer.Kick();
							if (GetPlayer) CSkill::ObjectRelease(GetPlayer, (int)GetPlayer + 352);
							return;
						}
					}

					if (GetPlayer) CSkill::ObjectRelease(GetPlayer, (int)GetPlayer + 352);
				}
			}
		}

		if (packet == 179)
		{
			char Type = 0; int Key = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bw", &Type, &Key);
			int Rate = CTools::Rate(1,1000);

			if (CPlayer::GetInvenSize(Player) >= IPlayer.MaxInventorySize())
			{
				IPlayer.BoxMsg("Your inventory is full.");
				return;
			}

			if (MiningListExchange.count(Key)) CQuest::Start(MiningListExchange.find(Key)->second,(int)IPlayer.GetOffset());
			return;
		}

		if (packet == 101)
		{
			int QuestID = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "w", &QuestID);
			if (PlayerQuest.find((QuestID * 1000000000000) + IPlayer.GetPID())->second.Active == 1) PlayerQuest[(QuestID * 1000000000000) + IPlayer.GetPID()].Active = 0;
			if (PlayerQuest.find((QuestID * 1000000000000) + IPlayer.GetPID())->second.MobAmount > 0) PlayerQuest[(QuestID * 1000000000000) + IPlayer.GetPID()].MobAmount = 0;
			if (CheckDailyQuest.count(QuestID)) IPlayer.QuitQuest(QuestID);
			else IPlayer.SystemMessage("You can only abandon daily quests!",TEXTCOLOR_RED);
			return;
		}

		if (packet == 188)
		{
			char Type = 0; int Value = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &Type, &Value);

			if (Type == -13 || Type == -12 || Type == -11)
			{
				int JewelCheck = CPlayer::FindItem(IPlayer.GetOffset(),3360,39);

				if (JewelCheck)
				{
					if (IPlayer.IsBuff(172) && IPlayer.IsBuff(173) && IPlayer.IsBuff(174))
					{
						IPlayer.SystemMessage("You cannot add more the inventory slot(s).",TEXTCOLOR_RED);
						return;
					}

					CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,39);

					if (!IPlayer.IsBuff(172) && !IPlayer.IsBuff(173) && !IPlayer.IsBuff(174))
					{
						IPlayer.Buff(174,2592000,0);
						CPlayer::Write(IPlayer.GetOffset(),204,"d",36);
						CPlayer::Write(IPlayer.GetOffset(),181,"dwd",IPlayer.GetID(),499,2592000);
						return;
					}

					if (!IPlayer.IsBuff(172) && !IPlayer.IsBuff(173) && IPlayer.IsBuff(174))
					{
						IPlayer.Buff(173,2592000,0);
						CPlayer::Write(IPlayer.GetOffset(),204,"d",72);
						CPlayer::Write(IPlayer.GetOffset(),181,"dwd",IPlayer.GetID(),500,2592000);
						return;
					}

					if (!IPlayer.IsBuff(172) && IPlayer.IsBuff(173) && IPlayer.IsBuff(174))
					{
						IPlayer.Buff(172,2592000,0);
						CPlayer::Write(IPlayer.GetOffset(),204,"d",108);
						CPlayer::Write(IPlayer.GetOffset(),181,"dwd",IPlayer.GetID(),501,2592000);
						return;
					}
				} else {
					IPlayer.SystemMessage("You can not extend the period because there are not enough jewels.",TEXTCOLOR_RED);
					return;
				}
			}

			return;
		}

		if (packet == 92)
		{
			char Type = 0; int Amount = 0;
			int LeftData = CPacket::Read((char*)pPacket, (char*)pPos, "bd", &Type, &Amount);

			if (Type == 12 && Amount > 0)
			{
				if (Amount > 50)
				{
					IPlayer.SystemMessage("You can delete maximum 50 mails in a row.",TEXTCOLOR_RED);
					return;
				}

				for (int i = 0; i < Amount; i++)
				{
					int MAILID = 0;
					LeftData = CPacket::Read((char*)LeftData, (char*)pPos, "d", &MAILID);	
					if (MAILID > 0 && IPlayer.IsOnline()) CPlayer::Write(IPlayer.GetOffset(),255,"dd",223,MAILID);
				}

				return;
			}
		}

		if (packet == 192)
		{
			int Amount = 0, Type = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "dd", &Amount, &Type);

			if (Type == 0 && CPlayer::RemoveItem(IPlayer.GetOffset(),9,3360,2*Amount))
			{
				CheckTraining[IPlayer.GetPID()].Time = CheckTraining.find(IPlayer.GetPID())->second.Time + (1800*Amount);
				MainSvrT::UpdateTrainingCenter(IPlayer.GetPID(),CheckTraining.find(IPlayer.GetPID())->second.Day,CheckTraining.find(IPlayer.GetPID())->second.Time);
			}

			return;
		}

		if (packet == 255)
		{
			int TypeCheck = 0;
			CPacket::Read((char*)pPacket, (char*)pPos,"d", &TypeCheck);

			if (TypeCheck == 5)
			{
				if (CheckBlob.find(IPlayer.GetPID())->second)
				{
					CheckBlob[IPlayer.GetPID()] = 0;
					IPlayer.SystemMessage("Monster blob enabled.", TEXTCOLOR_INFOMSG);
				} else {
					CheckBlob[IPlayer.GetPID()] = 1;
					IPlayer.SystemMessage("Monster blob disabled.", TEXTCOLOR_INFOMSG);
				}
			}

			return;
		}

		if (packet == 172)
		{
			unsigned char Str = 0, Hp = 0, Int = 0, Wis = 0, Agi = 0; int NeedStatPoint = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bbbbb", &Str, &Hp, &Int, &Wis, &Agi);
			int GetStr = IPlayer.GetStr(), GetHth = IPlayer.GetHth(), GetInt = IPlayer.GetInt(), GetWis = IPlayer.GetWis(), GetAgi = IPlayer.GetAgi();

			if (Str)
			{
				if (IPlayer.GetStr() + Str >= 255)
				{
					IPlayer.SystemMessage("Base stats can not be higher then 255!",TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.GetClass() == 0 || IPlayer.GetClass() == 5)
				{
					for (int i = 0; i < Str; i++)
					{
						NeedStatPoint += _StatTable[GetStr];
						GetStr++;
					}
				} else {
					for (int i = 0; i < Str; i++)
					{
						NeedStatPoint += StatTable[GetStr];
						GetStr++;
					}
				}
			}

			if (Hp)
			{
				if (IPlayer.GetHth() + Hp >= 255)
				{
					IPlayer.SystemMessage("Base stats can not be higher then 255!",TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.GetClass() == 3)
				{
					int pSkill = *((DWORD*)((int)IPlayer.GetOffset() + 624) + 30 + 2);

					if (pSkill)
					{
						ISkill xSkill((void*)pSkill);
						IPlayer.IncreaseMaxHp((5 * xSkill.GetGrade()) * Hp);
					}
				}

				for (int i = 0; i < Hp; i++)
				{
					NeedStatPoint += StatTable[GetHth];
					GetHth++;
				}
			}

			if (Int)
			{
				if (IPlayer.GetInt() + Int >= 255)
				{
					IPlayer.SystemMessage("Base stats can not be higher then 255!",TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.GetClass() == 1 || IPlayer.GetClass() == 4)
				{
					for (int i = 0; i < Int; i++)
					{
						NeedStatPoint += _StatTable[GetInt];
						GetInt++;
					}
				} else {
					for (int i = 0; i < Int; i++)
					{
						NeedStatPoint += StatTable[GetInt];
						GetInt++;
					}
				}
			}

			if (Wis)
			{
				if (IPlayer.GetWis() + Wis >= 255)
				{
					IPlayer.SystemMessage("Base stats can not be higher then 255!",TEXTCOLOR_RED);
					return;
				}

				for (int i = 0; i < Wis; i++)
				{
					NeedStatPoint += StatTable[GetWis];
					GetWis++;
				}
			}

			if (Agi)
			{
				if (IPlayer.GetAgi() + Agi >= 255)
				{
					IPlayer.SystemMessage("Base stats can not be higher then 255!",TEXTCOLOR_RED);
					return;
				}

				if (IPlayer.GetClass() == 2 || IPlayer.GetClass() == 3)
				{
					for (int i = 0; i < Agi; i++)
					{
						NeedStatPoint += _StatTable[GetAgi];
						GetAgi++;
					}
				} else {
					for (int i = 0; i < Agi; i++)
					{
						NeedStatPoint += StatTable[GetAgi];
						GetAgi++;
					}
				}
			}

			if (IPlayer.GetStatPoint() < NeedStatPoint || IPlayer.GetStatPoint() <= 0 || NeedStatPoint <= 0)
			{
				CPlayer::Write(IPlayer.GetOffset(), 68, "bb", 26, NeedStatPoint);
				return;
			}

			if (Str)
			{
				IPlayer.IncreaseStat(Str,0);
				CDBSocket::Write(16,"dbwbb",IPlayer.GetPID(),23,IPlayer.GetStatPoint(),0,*(DWORD *)(Player + 4 * 0 + 64));
			}

			if (Hp)
			{
				IPlayer.IncreaseStat(Hp,1);
				CDBSocket::Write(16,"dbwbb",IPlayer.GetPID(),23,IPlayer.GetStatPoint(),1,*(DWORD *)(Player + 4 * 1 + 64));
			}

			if (Int)
			{
				IPlayer.IncreaseStat(Int,2);
				CDBSocket::Write(16,"dbwbb",IPlayer.GetPID(),23,IPlayer.GetStatPoint(),2,*(DWORD *)(Player + 4 * 2 + 64));
			}

			if (Wis)
			{
				IPlayer.IncreaseStat(Wis,3);
				CDBSocket::Write(16,"dbwbb",IPlayer.GetPID(),23,IPlayer.GetStatPoint(),3,*(DWORD *)(Player + 4 * 3 + 64));
			}

			if (Agi)
			{
				IPlayer.IncreaseStat(Agi,4);
				CDBSocket::Write(16,"dbwbb",IPlayer.GetPID(),23,IPlayer.GetStatPoint(),4,*(DWORD *)(Player + 4 * 4 + 64));
			}

			if (NeedStatPoint) IPlayer.RemoveStatPoint(NeedStatPoint);
			return;
		}

		if (packet == 183)
		{
			char Type = 0, Value = 0; int BillCode = 0; unsigned short Amount = 0; const char *GiftName = "none";
			Interface<ITools> Tools; Tools->ParseData((char*)pPacket, "bbdws", &Type, &Value, &BillCode, &Amount, &GiftName);		
			if (Type == 1 && Value == 0) CPlayer::Write(IPlayer.GetOffset(),186,"bbwwm", 3, 1, 1, ItemShopCheck.size(), ItemShopPacket, ItemShopCheck.size() * 12);

			if (QuestRentItem.count(BillCode))
			{
				CQuest::Start(QuestRentItem.find(BillCode)->second << 16 | 1,(int)IPlayer.GetOffset());
				return;
			}

			if (Type == 1 && Value == 1)
			{
				int Jewel = 0, GoldKC = 0, SilverKC = 0, JewelAmount = 0;
				int GoldKCAmount = 0, SilverKCAmount = 0, BSGrade = 0, BSPurchase = 0;
				Jewel = CPlayer::FindItem(IPlayer.GetOffset(),ShopJewelIndex,1);
				GoldKC = CPlayer::FindItem(IPlayer.GetOffset(),ShopGoldIndex,1);
				SilverKC = CPlayer::FindItem(IPlayer.GetOffset(),ShopSilverIndex,1);
				if (Jewel)
				{
					IItem xJewel((void*)Jewel);
					JewelAmount = xJewel.GetAmount();
				}
				if (GoldKC)
				{
					IItem xGold((void*)GoldKC);
					GoldKCAmount = xGold.GetAmount();
				}
				if (SilverKC)
				{
					IItem xSilver((void*)SilverKC);
					SilverKCAmount = xSilver.GetAmount();
				}
				if (BenefitSystem.count(IPlayer.GetPID()) && BenefitSystem.find(IPlayer.GetPID())->second.Purchase)
				{
					BSPurchase = BenefitSystem.find(IPlayer.GetPID())->second.Purchase;
					if (BSPurchase > 0 && BSPurchase < 2) BSGrade = 1;
					if (BSPurchase > 1 && BSPurchase < 5) BSGrade = 2;
					if (BSPurchase > 4 && BSPurchase < 10) BSGrade = 3;
					if (BSPurchase > 10) BSGrade = 4;
				}
				CPlayer::Write(IPlayer.GetOffset(),186,"b",0);
				CPlayer::Write(IPlayer.GetOffset(),186,"bddddbw",4,GoldKCAmount,SilverKCAmount,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount,BSGrade,BSPurchase);
			}

			if ((Type == 2 || Type == 4) && ItemShopCheck.count(BillCode) && ItemShopCheck.find(BillCode)->second.Priority == 4)
			{
				CPlayer::Write(IPlayer.GetOffset(),186,"b",11);
				return;
			}

			if (Type == 2)
			{
				if (ItemShopCheck.count(BillCode))
				{
					const char *PlayerName = GiftName;
					int GTPlayer = CPlayer::FindPlayerByName(PlayerName);
					if (GTPlayer)
					{
						IChar Target((void*)GTPlayer);
						if (!Target.IsOnline())
						{
							if (GTPlayer) CSkill::ObjectRelease((void*)GTPlayer, GTPlayer + 352);
							CPlayer::Write(IPlayer.GetOffset(),186,"b",18);
							return;
						}
						if (Amount <= 0)
						{
							if (GTPlayer) CSkill::ObjectRelease((void*)GTPlayer, GTPlayer + 352);
							CPlayer::Write(IPlayer.GetOffset(),186,"b",17);
							return;
						}
						int Price = 0; Price = ItemShopCheck.find(BillCode)->second.Price * Amount;
						if (Price <= 0)
						{
							if (GTPlayer) CSkill::ObjectRelease((void*)GTPlayer, GTPlayer + 352);
							CPlayer::Write(IPlayer.GetOffset(),186,"b",17);
							return;
						}
						int Check = 2147483647 / Price;
						if (Amount >= Check)
						{
							if (GTPlayer) CSkill::ObjectRelease((void*)GTPlayer, GTPlayer + 352);
							CPlayer::Write(IPlayer.GetOffset(),186,"b",17);
							return;
						}
						if (ItemShopCheck.find(BillCode)->second.Discount) Price -= ((Price * ItemShopCheck.find(BillCode)->second.Discount) / 100);
						int Jewel = 0, GoldKC = 0, SilverKC = 0, JewelAmount = 0, GoldKCAmount = 0, SilverKCAmount = 0;
						Jewel = CPlayer::FindItem(IPlayer.GetOffset(),ShopJewelIndex,1);
						GoldKC = CPlayer::FindItem(IPlayer.GetOffset(),ShopGoldIndex,1);
						SilverKC = CPlayer::FindItem(IPlayer.GetOffset(),ShopSilverIndex,1);
						IItem xJewel((void*)Jewel); IItem xGold((void*)GoldKC); IItem xSilver((void*)SilverKC);
						if (Jewel) JewelAmount = xJewel.GetAmount();
						if (GoldKC) GoldKCAmount = xGold.GetAmount();
						if (SilverKC) SilverKCAmount = xSilver.GetAmount();
						if (ItemShopCheck.find(BillCode)->second.Type == 1)
						{
							if (GoldKC && GoldKCAmount >= Price && Target.IsOnline() && IPlayer.IsOnline())
							{
								CPlayer::Write(IPlayer.GetOffset(),186,"bdddd",4,GoldKCAmount-Price,SilverKCAmount,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount);
								IPlayer.DecreaseItemAmount(ShopGoldIndex,Price);
								std::string name = IPlayer.GetName();
								std::string msg = "KalOnline Item Shop Gift From Player " + name;
								Target.GiveReward(ItemShopCheck.find(BillCode)->second.ItemIndex,0,ItemShopCheck.find(BillCode)->second.Amount * Amount,-128,0,0,0,0,0,0,0,msg.c_str());
								CPlayer::Write(IPlayer.GetOffset(),186,"b",16);
								std::string KalShop = "./Exception/KAL_SHOP_LOG_" + Time::GetDay() + "_" + Time::GetMonth() + "_" + Time::GetYear() + ".txt";
								std::fstream Kal_Shop;
								Kal_Shop.open(KalShop, std::fstream::in | std::fstream::out | std::fstream::app);
								Kal_Shop << "[" << Time::GetDay() << "/" << Time::GetMonth() << "/" << Time::GetYear() << "-" << Time::GetTime() << "] Type: GoldKC --- Name: " << IPlayer.GetName() << " --- Code: " << BillCode << " --- Amount: " << Amount << " --- Gift: " << Target.GetName() << std::endl;
								Kal_Shop.close();
							} else {
								CPlayer::Write(IPlayer.GetOffset(),186,"b",17);
							}
						}
						if (ItemShopCheck.find(BillCode)->second.Type == 4)
						{
							if (Jewel && JewelAmount >= Price)
							{
								CPlayer::Write(IPlayer.GetOffset(),186,"bdddd",4,GoldKCAmount,SilverKCAmount,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount-Price);
								IPlayer.DecreaseItemAmount(ShopJewelIndex,Price);
								std::string name = IPlayer.GetName();
								std::string msg = "KalOnline Item Shop Gift From Player " + name;
								Target.GiveReward(ItemShopCheck.find(BillCode)->second.ItemIndex,0,ItemShopCheck.find(BillCode)->second.Amount * Amount,-128,0,0,0,0,0,0,0,msg.c_str());
								CPlayer::Write(IPlayer.GetOffset(),186,"b",16);
								std::string KalShop = "./Exception/KAL_SHOP_LOG_" + Time::GetDay() + "_" + Time::GetMonth() + "_" + Time::GetYear() + ".txt";
								std::fstream Kal_Shop;
								Kal_Shop.open(KalShop, std::fstream::in | std::fstream::out | std::fstream::app);
								Kal_Shop << "[" << Time::GetDay() << "/" << Time::GetMonth() << "/" << Time::GetYear() << "-" << Time::GetTime() << "] Type: Jewel --- Name: " << IPlayer.GetName() << " --- Code: " << BillCode << " --- Amount: " << Amount << " --- Gift: " << Target.GetName() << std::endl;
								Kal_Shop.close();
							} else {
								CPlayer::Write(IPlayer.GetOffset(),186,"b",17);
							}
						}

						if (GTPlayer) CSkill::ObjectRelease((void*)GTPlayer, GTPlayer + 352);
					}
				}
			}

			if (Type == 4)
			{
				if (ItemShopCheck.count(BillCode))
				{
					if (Amount <= 0)
					{
						CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,5);
						return;
					}
					int Price = 0; Price = ItemShopCheck.find(BillCode)->second.Price * Amount;
					if (Price <= 0)
					{
						CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,5);
						return;
					}
					int Check = 2147483647 / Price;
					if (Amount >= Check)
					{
						CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,5);
						return;
					}
					if (ItemShopCheck.find(BillCode)->second.Discount) Price -= ((Price * ItemShopCheck.find(BillCode)->second.Discount) / 100);
					int Jewel = 0, GoldKC = 0, SilverKC = 0, JewelAmount = 0, GoldKCAmount = 0, SilverKCAmount = 0;
					Jewel = CPlayer::FindItem(IPlayer.GetOffset(),ShopJewelIndex,1);
					GoldKC = CPlayer::FindItem(IPlayer.GetOffset(),ShopGoldIndex,1);
					SilverKC = CPlayer::FindItem(IPlayer.GetOffset(),ShopSilverIndex,1);
					IItem xJewel((void*)Jewel); IItem xGold((void*)GoldKC); IItem xSilver((void*)SilverKC);
					if (Jewel) JewelAmount = xJewel.GetAmount();
					if (GoldKC) GoldKCAmount = xGold.GetAmount();
					if (SilverKC) SilverKCAmount = xSilver.GetAmount();
					if (ItemShopCheck.find(BillCode)->second.Type == 1)
					{
						if (GoldKC && GoldKCAmount >= Price)
						{
							CPlayer::Write(IPlayer.GetOffset(),186,"bdddd",4,GoldKCAmount-Price,SilverKCAmount,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount);
							IPlayer.DecreaseItemAmount(ShopGoldIndex,Price);
							CItem::InsertItem((int)IPlayer.GetOffset(),27,ItemShopCheck.find(BillCode)->second.ItemIndex,0,ItemShopCheck.find(BillCode)->second.Amount * Amount,-1);
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",5,5);
							std::string KalShop = "./Exception/KAL_SHOP_LOG_" + Time::GetDay() + "_" + Time::GetMonth() + "_" + Time::GetYear() + ".txt";
							std::fstream Kal_Shop;
							Kal_Shop.open(KalShop, std::fstream::in | std::fstream::out | std::fstream::app);
							Kal_Shop << "[" << Time::GetDay() << "/" << Time::GetMonth() << "/" << Time::GetYear() << "-" << Time::GetTime() << "] Type: GoldKC --- Name: " << IPlayer.GetName() << " --- Code: " << BillCode << " --- Amount: " << Amount << std::endl;
							Kal_Shop.close();
						} else {
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,5);
						}
					}
					if (ItemShopCheck.find(BillCode)->second.Type == 2)
					{
						if (SilverKC && SilverKCAmount >= Price)
						{
							CPlayer::Write(IPlayer.GetOffset(),186,"bdddd",4,GoldKCAmount,SilverKCAmount-Price,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount);
							IPlayer.DecreaseItemAmount(ShopSilverIndex,Price);
							CItem::InsertItem((int)IPlayer.GetOffset(),27,ItemShopCheck.find(BillCode)->second.ItemIndex,0,ItemShopCheck.find(BillCode)->second.Amount * Amount,-1);
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",5,5);
							std::string KalShop = "./Exception/KAL_SHOP_LOG_" + Time::GetDay() + "_" + Time::GetMonth() + "_" + Time::GetYear() + ".txt";
							std::fstream Kal_Shop;
							Kal_Shop.open(KalShop, std::fstream::in | std::fstream::out | std::fstream::app);
							Kal_Shop << "[" << Time::GetDay() << "/" << Time::GetMonth() << "/" << Time::GetYear() << "-" << Time::GetTime() << "] Type: SilverKC --- Name: " << IPlayer.GetName() << " --- Code: " << BillCode << " --- Amount: " << Amount << std::endl;
							Kal_Shop.close();
						} else {
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,5);
						}
					}
					if (ItemShopCheck.find(BillCode)->second.Type == 3)
					{
						if (CheckHonor.find(IPlayer.GetPID())->second.RPx && CheckHonor.find(IPlayer.GetPID())->second.RPx >= Price)
						{
							CheckHonor[IPlayer.GetPID()].RPx -= Price;
							CPlayer::Write(IPlayer.GetOffset(),186,"bdddd",4,GoldKCAmount,SilverKCAmount,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount);
							CItem::InsertItem((int)IPlayer.GetOffset(),27,ItemShopCheck.find(BillCode)->second.ItemIndex,0,ItemShopCheck.find(BillCode)->second.Amount * Amount,-1);
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",5,5);
							std::string KalShop = "./Exception/KAL_SHOP_LOG_" + Time::GetDay() + "_" + Time::GetMonth() + "_" + Time::GetYear() + ".txt";
							std::fstream Kal_Shop;
							Kal_Shop.open(KalShop, std::fstream::in | std::fstream::out | std::fstream::app);
							Kal_Shop << "[" << Time::GetDay() << "/" << Time::GetMonth() << "/" << Time::GetYear() << "-" << Time::GetTime() << "] Type: HonorPoint --- Name: " << IPlayer.GetName() << " --- Code: " << BillCode << " --- Amount: " << Amount << std::endl;
							Kal_Shop.close();
						} else {
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,5);
						}
					}
					if (ItemShopCheck.find(BillCode)->second.Type == 4)
					{
						if (Jewel && JewelAmount >= Price)
						{
							CPlayer::Write(IPlayer.GetOffset(),186,"bdddd",4,GoldKCAmount,SilverKCAmount,CheckHonor.find(IPlayer.GetPID())->second.RPx,JewelAmount-Price);
							IPlayer.DecreaseItemAmount(ShopJewelIndex,Price);
							CItem::InsertItem((int)IPlayer.GetOffset(),27,ItemShopCheck.find(BillCode)->second.ItemIndex,0,ItemShopCheck.find(BillCode)->second.Amount * Amount,-1);
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",5,4);
							std::string KalShop = "./Exception/KAL_SHOP_LOG_" + Time::GetDay() + "_" + Time::GetMonth() + "_" + Time::GetYear() + ".txt";
							std::fstream Kal_Shop;
							Kal_Shop.open(KalShop, std::fstream::in | std::fstream::out | std::fstream::app);
							Kal_Shop << "[" << Time::GetDay() << "/" << Time::GetMonth() << "/" << Time::GetYear() << "-" << Time::GetTime() << "] Type: Jewel --- Name: " << IPlayer.GetName() << " --- Code: " << BillCode << " --- Amount: " << Amount << std::endl;
							Kal_Shop.close();
						} else {
							CPlayer::Write(IPlayer.GetOffset(),186,"bb",6,4);
						}
					}
				}
			}

			return;
		}

		if (packet == 41)
		{
			int SkillID = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "b", &SkillID);
			if (SkillID == 87) SkillID = 89;
			if (SkillID == 86) SkillID = 88;
			if (SkillID == 85) SkillID = 87;
			if (SkillID >= 90) return; if (SkillID == 79) return;

			if (IPlayer.GetLevel() >= 81 && SkillID >= 87 && SkillID <= 89 && IPlayer.GetSpecialty() > 1)
			{
				int OTP = IPlayer.GetSkillPointer(87);
				int EVA = IPlayer.GetSkillPointer(88);
				int DEF = IPlayer.GetSkillPointer(89);

				if (OTP || EVA || DEF)
				{
					IPlayer.SystemMessage("You can only learn one mystery skill.",TEXTCOLOR_RED);
					return;
				}

				if (SkillID == 87)
				{
					int Value = 0, Argument = 0, Arg = 0, Check = 0, Recheck = 0;
					Value = SkillID + (*((DWORD*)((DWORD*)((int)IPlayer.GetOffset() + 624)) + 1) << 16);
					Undefined::CreateSkillValue((void*)0x004E218C, (int)&Argument, (int)&Value);
					Check = Undefined::Check((int)0x004E218C, (int)&Arg);

					if (*(DWORD*)&Argument != *(DWORD*)Check)
					{
						Recheck = *(DWORD*)(Undefined::GetValue(&Argument) + 4);

						if ((*(int (__thiscall**)(int, DWORD, DWORD))(*(DWORD*)Recheck + 60))(Recheck,*(DWORD *)((int)IPlayer.GetOffset() + 60),*(DWORD*)((int)IPlayer.GetOffset() + 464)))
						{
							if (Undefined::CheckSkillValue((DWORD*)((int)IPlayer.GetOffset() + 624),*(DWORD*)(Recheck + 20),*(DWORD *)(Recheck + 24)))
							{
								Recheck = (int)Undefined::CheckSkillX((void*)Recheck);
								Undefined::CheckSkillY(Recheck, SkillID, 1);
								(*(void (__thiscall **)(int, DWORD, DWORD, DWORD))(*(DWORD*)Recheck + 8))(Recheck, (int)IPlayer.GetOffset(), 0, 0);
								*((DWORD*)(DWORD*)((int)IPlayer.GetOffset() + 624) + SkillID + 2) = Recheck;
								IPlayer.AddOTP(8);
								CDBSocket::Write(9, "dbw", IPlayer.GetPID(), SkillID, 1);
								CPlayer::Write(IPlayer.GetOffset(), 81, "bb", 85, 1);
							}
						}
					}

					if (IPlayer.GetLevel() > 81) MainSvrT::AutoLearn((void*)(DWORD*)(Player + 624),0,0);
				}

				if (SkillID == 88)
				{
					int Value = 0, Argument = 0, Arg = 0, Check = 0, Recheck = 0;
					Value = SkillID + (*((DWORD*)((DWORD*)((int)IPlayer.GetOffset() + 624)) + 1) << 16);
					Undefined::CreateSkillValue((void*)0x004E218C, (int)&Argument, (int)&Value);
					Check = Undefined::Check((int)0x004E218C, (int)&Arg);

					if (*(DWORD*)&Argument != *(DWORD*)Check)
					{
						Recheck = *(DWORD*)(Undefined::GetValue(&Argument) + 4);

						if ((*(int (__thiscall**)(int, DWORD, DWORD))(*(DWORD*)Recheck + 60))(Recheck,*(DWORD *)((int)IPlayer.GetOffset() + 60),*(DWORD*)((int)IPlayer.GetOffset() + 464)))
						{
							if (Undefined::CheckSkillValue((DWORD*)((int)IPlayer.GetOffset() + 624),*(DWORD*)(Recheck + 20),*(DWORD *)(Recheck + 24)))
							{
								Recheck = (int)Undefined::CheckSkillX((void*)Recheck);
								Undefined::CheckSkillY(Recheck, SkillID, 1);
								(*(void (__thiscall **)(int, DWORD, DWORD, DWORD))(*(DWORD*)Recheck + 8))(Recheck, (int)IPlayer.GetOffset(), 0, 0);
								*((DWORD*)(DWORD*)((int)IPlayer.GetOffset() + 624) + SkillID + 2) = Recheck;
								IPlayer.AddEva(6);
								CDBSocket::Write(9, "dbw", IPlayer.GetPID(), SkillID, 1);
								CPlayer::Write(IPlayer.GetOffset(), 81, "bb", 86, 1);
							}
						}
					}

					if (IPlayer.GetLevel() > 81) MainSvrT::AutoLearn((void*)(DWORD*)(Player + 624),0,0);
				}

				if (SkillID == 89)
				{
					int Value = 0, Argument = 0, Arg = 0, Check = 0, Recheck = 0;
					Value = SkillID + (*((DWORD*)((DWORD*)((int)IPlayer.GetOffset() + 624)) + 1) << 16);
					Undefined::CreateSkillValue((void*)0x004E218C, (int)&Argument, (int)&Value);
					Check = Undefined::Check((int)0x004E218C, (int)&Arg);

					if (*(DWORD*)&Argument != *(DWORD*)Check)
					{
						Recheck = *(DWORD*)(Undefined::GetValue(&Argument) + 4);

						if ((*(int (__thiscall**)(int, DWORD, DWORD))(*(DWORD*)Recheck + 60))(Recheck,*(DWORD *)((int)IPlayer.GetOffset() + 60),*(DWORD*)((int)IPlayer.GetOffset() + 464)))
						{
							if (Undefined::CheckSkillValue((DWORD*)((int)IPlayer.GetOffset() + 624),*(DWORD*)(Recheck + 20),*(DWORD *)(Recheck + 24)))
							{
								Recheck = (int)Undefined::CheckSkillX((void*)Recheck);
								Undefined::CheckSkillY(Recheck, SkillID, 1);
								(*(void (__thiscall **)(int, DWORD, DWORD, DWORD))(*(DWORD*)Recheck + 8))(Recheck, (int)IPlayer.GetOffset(), 0, 0);
								*((DWORD*)(DWORD*)((int)IPlayer.GetOffset() + 624) + SkillID + 2) = Recheck;
								IPlayer.AddDef(31);
								CDBSocket::Write(9, "dbw", IPlayer.GetPID(), SkillID, 1);
								CPlayer::Write(IPlayer.GetOffset(), 81, "bb", 87, 1);
							}
						}
					}

					if (IPlayer.GetLevel() > 81) MainSvrT::AutoLearn((void*)(DWORD*)(Player + 624),0,0);
				}

				return;
			}

			if (IPlayer.GetClass() == 0 && ((SkillID >= 37 && SkillID <= 44) || (SkillID >= 87 && SkillID <= 89) || SkillID == 70 || SkillID == 71 || SkillID == 82))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (!SkillPointer)
				{
					if (IPlayer.GetSkillPoint() <= 0 || !SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 0)) return;
				}
			}

			if (IPlayer.GetClass() == 1 && ((SkillID >= 63 && SkillID <= 76) || (SkillID >= 87 && SkillID <= 89) || SkillID == 82))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (!SkillPointer)
				{
					if (IPlayer.GetSkillPoint() <= 0 || !SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 0)) return;
				}
			}

			if (IPlayer.GetClass() == 2 && ((SkillID >= 46 && SkillID <= 51) || (SkillID >= 87 && SkillID <= 89) || SkillID == 82))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (!SkillPointer)
				{
					if (IPlayer.GetSkillPoint() <= 0 || !SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 0)) return;
				}
			}

			if (IPlayer.GetClass() == 3 && ((SkillID >= 0 && SkillID <= 37) || (SkillID >= 87 && SkillID <= 89) || SkillID == 70 || SkillID == 63))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (!SkillPointer)
				{
					if (IPlayer.GetSkillPoint() <= 0 || !SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 0)) return;
				}
			}

			if (IPlayer.GetClass() == 4 && SkillID >= 0 && SkillID <= 89)
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (!SkillPointer)
				{
					if (IPlayer.GetSkillPoint() <= 0 || !SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 0)) return;
				}
			}
		}

		if (packet == 42)
		{
			int SkillID = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "b", &SkillID);
			if (SkillID == 87) SkillID = 89;
			if (SkillID == 86) SkillID = 88;
			if (SkillID == 85) SkillID = 87;
			if (SkillID >= 90) return; if (SkillID >= 79) return;
			if (IPlayer.GetLevel() >= 81 && SkillID >= 87 && SkillID <= 89) return;

			if (IPlayer.GetClass() == 0 && ((SkillID >= 37 && SkillID <= 44) || (SkillID >= 87 && SkillID <= 89) || SkillID == 70 || SkillID == 71 || SkillID == 82))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (IPlayer.GetSkillPoint() > 0 && SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 1))
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
						IPlayer.RemoveSkillPoint(1);
					}
				}

				return;
			}

			if (IPlayer.GetClass() == 1 && SkillID == 40)
			{
				int SkillPointer = IPlayer.GetSkillPointer(40);

				if (IPlayer.GetSkillPoint() > 0 && SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (xSkill.GetGrade() >= 1 && xSkill.GetGrade() < 5)
					{
						int Check = (IPlayer.GetLevel() - 47) / 5;

						if (Check > xSkill.GetGrade())
						{
							CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
							CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
							*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
							IPlayer.RemoveSkillPoint(1);
							return;
						}
					}
				}
			}

			if (IPlayer.GetClass() == 1 && SkillID == 26)
			{
				int SkillPointer = IPlayer.GetSkillPointer(26);

				if (IPlayer.GetSkillPoint() > 0 && SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (xSkill.GetGrade() >= 1 && xSkill.GetGrade() < 5)
					{
						if (IPlayer.GetLevel() >= 30)
						{
							CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
							CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
							*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
							IPlayer.RemoveSkillPoint(1);
							return;
						}
					}
				}
			}

			if (IPlayer.GetClass() == 1 && ((SkillID >= 63 && SkillID <= 76) || (SkillID >= 87 && SkillID <= 89) || SkillID == 82))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (IPlayer.GetSkillPoint() > 0 && SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 1))
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
						IPlayer.RemoveSkillPoint(1);
					}
				}

				return;
			}

			if (IPlayer.GetClass() == 2 && ((SkillID >= 46 && SkillID <= 51) || (SkillID >= 87 && SkillID <= 89) || SkillID == 70 || SkillID == 71 || SkillID == 82))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (IPlayer.GetSkillPoint() > 0 && SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 1))
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
						IPlayer.RemoveSkillPoint(1);
					}
				}

				return;
			}

			if (IPlayer.GetClass() == 3 &&  ((SkillID >= 0 && SkillID <= 37) || (SkillID >= 87 && SkillID <= 89) || SkillID == 70 || SkillID == 63))
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (IPlayer.GetSkillPoint() > 0 && SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 1))
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
						IPlayer.RemoveSkillPoint(1);
					}
				}

				return;
			}

			if (IPlayer.GetClass() == 4 && SkillID >= 0 && SkillID <= 89)
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (IPlayer.GetSkillPoint() > 0 && SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 1))
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
						IPlayer.RemoveSkillPoint(1);
					}
				}

				return;
			}

			if (IPlayer.GetClass() == 5 && SkillID >= 0 && SkillID <= 89)
			{
				int SkillPointer = IPlayer.GetSkillPointer(SkillID);

				if (SkillPointer)
				{
					ISkill xSkill((void*)SkillPointer);

					if (IPlayer.GetSkillPoint() > 0 && SkillUpgradeCheck(IPlayer.GetOffset(), SkillID, 1))
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
						IPlayer.RemoveSkillPoint(1);
					}
				}

				return;
			}
		}

		if (packet == 43)
		{
			if (IPlayer.IsValid() && IPlayer.IsBuff(349))
			{
				IPlayer.DisableRiding();
				return;
			}

			unsigned __int8 AnimationID = 0; int ID = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &AnimationID, &ID);

			if (IPlayer.IsOnline() && IPlayer.GetID() != ID && (int)AnimationID == 1)
			{
				IPlayer.Kick();
				return;
			}

			if (IPlayer.IsOnline() && AnimationID >= 100 && AnimationID <= 120 && CChar::IsGState((int)IPlayer.GetOffset(),512))
			{
				CChar::WriteInSight((void*)Player, 61, "dbbd", IPlayer.GetID(), 5, AnimationID, ID);
				return;
			}

			if (IPlayer.IsOnline() && AnimationID == 74 && IPlayer.GetClass() == 1 && ID == IPlayer.GetID())
			{
				int pSkill = IPlayer.GetSkillPointer(74);

				if (pSkill)
				{
					int GetCooldown = MainSvrT::GetCooldownTime(IPlayer.GetPID(),74);

					if (GetCooldown > (int)GetTickCount())
					{
						IPlayer.SystemMessage("Invalid skill time detected!", TEXTCOLOR_RED);
						return;
					}

					ISkill xSkill((void*)pSkill);
					int IceArrowGrade = xSkill.GetGrade();
					int Mana = (int)(((450 + (IceArrowGrade * ((IceArrowGrade - 1) + 100)))) * 0.42);
					__int64 GState = 536870912, State = 64, StateEx = 65;
					if (IPlayer.GetCurMp() <= Mana) return; IPlayer.DecreaseMana(Mana);
					CChar::WriteInSight((void*)Player, 61, "dbbd", IPlayer.GetID(), 5, AnimationID, ID);

					if (IceArrowGrade == 1)
					{
						IPlayer.Buff(290,18,0);
						IPlayer.Buff(295,18,0);
						IPlayer.SendGStateExIceArrow(GState);
						IPlayer.SendGStateExIceArrow(State << 32);
					} else if (IceArrowGrade == 2)
					{
						IPlayer.Buff(291,18,0);
						IPlayer.Buff(295,18,0);
						IPlayer.SendGStateExIceArrow(GState*2);
						IPlayer.SendGStateExIceArrow(State << 32);
					} else if (IceArrowGrade == 3)
					{
						IPlayer.Buff(292,18,0);
						IPlayer.Buff(295,18,0);
						IPlayer.SendGStateExIceArrow(GState*4);
						IPlayer.SendGStateExIceArrow(State << 32);
					} else if (IceArrowGrade == 4)
					{
						IPlayer.Buff(293,18,0);
						IPlayer.Buff(295,18,0);
						IPlayer.SendGStateExIceArrow(GState*8);
						IPlayer.SendGStateExIceArrow(State << 32);
					} else if (IceArrowGrade == 5)
					{
						IPlayer.Buff(294,18,0);
						IPlayer.Buff(295,18,0);
						IPlayer.SendGStateExIceArrow(GState*16);
						IPlayer.SendGStateExIceArrow(StateEx << 32);
					} else {
						return;
					}
				}

				return;
			}
		}

		CPlayer::Process((void*)Player, packet, (void*)pPacket, pPos);
		if (IPlayer.IsOnline() && packet == 18 && IPlayer.GetClass() == 4 && IPlayer.IsBuff(406)) CPlayer::Write(IPlayer.GetOffset(),148,"dI",IPlayer.GetID(),(__int64)0);
	}

	void MyPlayerProcess()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x004956B0,MainSvrT::PlayerProcess);
	}
}