namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		int SkillGrade;
		int Count;
		int x;
		int y;
	} ThiefSB;

	void __cdecl ContinueSpinBlade(void *Pack)
	{
		MainSvrT::ThiefSB *CTSB = (MainSvrT::ThiefSB*)Pack;
		IChar IPlayer(CTSB->Player);

		if (IPlayer.IsValid() && CTSB->SkillGrade && CTSB->Count)
		{
			for (int i = 0; i < CTSB->Count; i++)
			{
				if (!IPlayer.IsValid()) break;
				if (IPlayer.IsMoved(CTSB->x, CTSB->y)) break;
				if (!ContinueSkills.count(IPlayer.GetPID()) || ContinueSkills.find(IPlayer.GetPID())->second != 36) break;
				int Around = IPlayer.GetObjectListAround(3);

				while(Around)
				{
					IChar Object((void*)*(DWORD*)Around);

					if (Object.IsValid() && IPlayer.IsValid() && (*(int (__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0))
					{
						int nDmg = (IPlayer.GetAttack() * TSBMul) + (CTSB->SkillGrade * CTools::Rate(TSBMin,TSBMax));
						if (Object.GetType() == 0) nDmg = (nDmg * TSBReduce) / 100;
						if (IPlayer.CheckHit(Object, 10) || Object.GetType() == 1) IPlayer.OktayDamageArea(Object,nDmg,36);
					}

					Around = CBaseList::Pop((void*)Around);
				}

				IPlayer._ShowBattleAnimation(IPlayer, 36);
				Sleep(1000);
			}
		}

		free(CTSB);
	}

	void __fastcall SpinBlade(IChar IPlayer)
	{
		int pSkill = IPlayer.GetSkillPointer(36);

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nMana = (int)((((nSkillGrade+IPlayer.GetLevel()) * nSkillGrade)*1.25) + 180);
			if (IPlayer.GetCurMp() < nMana) return;

			if (nSkillGrade && IPlayer.IsValid())
			{
				IPlayer.DecreaseMana(nMana);	
				MainSvrT::ThiefSB *SBContinue;
				SBContinue = (MainSvrT::ThiefSB*)malloc(sizeof(MainSvrT::ThiefSB));
				SBContinue->Player = IPlayer.GetOffset();
				SBContinue->SkillGrade = nSkillGrade;
				SBContinue->x = IPlayer.GetX();
				SBContinue->y = IPlayer.GetY();
				SBContinue->Count = nSkillGrade + 1;
				_beginthread(MainSvrT::ContinueSpinBlade,0,(void*)SBContinue);
			}
		}
	}
}