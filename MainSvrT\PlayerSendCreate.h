namespace MainSvrT
{
	void __cdecl CPlayerSendCreate(void *player, unsigned char Type, char* format, long id, char* name, unsigned char _class, long x, long y, long z, unsigned short direction, unsigned long gState, unsigned char* Items, int ItemsLen, char face, char hair, unsigned __int64 mState64, char* GuildTitle, char* GuildName, long GuildID, char flag, long flagIndex)
	{
		Interface<ITools> Tools; int a=0,b=0,c=0,d=0,e=0,f=0,g=0; char Fix[78] = {0};
		Tools->ParseData((char*)Items,"wwwwwww",&a,&b,&c,&d,&e,&f,&g);
		Tools->Compile(Fix,"wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww",a,b,c,d,e,f,g,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);
		CPlayer::Write(player, 50, "dsbdddwImbbIssdbdddIIb", id, name, _class, x, y, z, direction, (unsigned __int64)gState, Fix, 78, face, hair, mState64, GuildTitle, GuildName, GuildID, flag, flagIndex, 0, 0, (__int64)0, (__int64)0,0);
	}

	long __cdecl CPlayerSendExclusiveCreate(unsigned char Type, void *player, long id, char* name, unsigned char _class, long x, long y, long z, unsigned short direction, unsigned long gState, unsigned char* Items, int ItemsLen, char face, char hair, unsigned __int64 mState64, char* GuildTitle, char* GuildName, long GuildID, char flag, long flagIndex)
	{
		Interface<ITools> Tools; int a=0,b=0,c=0,d=0,e=0,f=0,g=0; char Fix[78] = {0};
		Tools->ParseData((char*)Items,"wwwwwww",&a,&b,&c,&d,&e,&f,&g);
		Tools->Compile(Fix,"wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww",a,b,c,d,e,f,g,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);
		return CObject::WriteExclusive(50, "dsbdddwImbbIssdbdddIIb", id, name, _class, x, y, z, direction, (unsigned __int64)gState, Fix, 78, face, hair, mState64, GuildTitle, GuildName, GuildID, flag, flagIndex, 0, 0, (__int64)0, (__int64)0,0);
	}

	int __cdecl PlayerLogOutDuplicateFix(void *Player, unsigned char Type, char *Format, long id)
	{
		return CPlayer::WriteAll(Type,Format,id);
	}

	int __cdecl PlayerTeleportDuplicateFix(void *Player, unsigned char Type, char *Format, long id)
	{
		IChar IPlayer(Player); int Around = IPlayer.GetObjectListAround(25);
		
		while(Around)
		{
			IChar Object((void*)*(DWORD*)Around);
			
			if (Object.IsOnline() && IPlayer.IsOnline() && Object.GetType() == 0 && IPlayer.GetOffset() != Object.GetOffset())
			{
				CPlayer::Write(IPlayer.GetOffset(),55,"d",Object.GetID());
			}
			
			Around = CBaseList::Pop((void*)Around);
		}

		return CChar::WriteInSight(Player,Type,Format,id);
	}

	long __cdecl PlayerMoveFix(unsigned char Type, const char *Format, int nID, signed int x, signed int y, signed int z)
	{
		return CObject::WriteExclusive(Type, "dbbw", nID, x, y, (__int16)z);
	}

	void __cdecl FixGuildBasicInfo(void *player, unsigned char Type, char *format, unsigned char nType, BYTE byPenaltyLevel, int nPoint, int nExp, int nMoney, int nFlag)
	{
		IChar IPlayer(player);
		std::string Name, LName, BuffName; int Level = 0, Exp = 0, Point = 0, Attendance = 0, BuffType = 0, Time = 0, Size = 0;
		MainSvrT::ReadGuild(IPlayer.GetGID(),Name,LName,Level,Exp,Point,Attendance,BuffType,BuffName,Time,Size);
		if (IPlayer.GetGID() == IPlayer.GetPID()) LName = IPlayer.GetName(); Level = byPenaltyLevel; Exp = nExp;
		MainSvrT::UpdateGuild(IPlayer.GetGID(),Name.c_str(),LName.c_str(),Level,Exp,Point,Attendance,BuffType,BuffName.c_str(),Time,Size);
		if (Point >= 201600) BuffType = 1; if (Time > (int)time(0)) Time = Time - (int)time(0); else Time = 0; if (Time) BuffType = 2;
		CPlayer::Write(player, Type, "bbdddbwsd", 14, byPenaltyLevel, Point, nExp, nFlag, BuffType, Attendance, BuffName.c_str(), Time);
	}

	void __cdecl FixGuildMemberInfo(void *player, unsigned char Type, char *format, BYTE nType, int GID, char *GuildName, char *C1, char *C2, char *C3, char *C4, char *C5, char *C6, BYTE MemberSize, char *Members, int MembersLen)
	{
		size_t NewSize = 0; size_t Size = MembersLen + (MemberSize * 13);
		Interface<ITools> Tools; char *Packet = new char[Size]; int TotalPoint = 0;
		
		for (BYTE i = 0; i < MemberSize; i++)
		{
			char *szName = 0; BYTE byClass = 0, byLevel = 0; std::string NewName;
			Members = Tools->ParseData(Members,"sbb",&szName,&byClass,&byLevel);
			int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
			MainSvrT::ReadGuildMember(szName,Count,Class,Level,Point,Time);
			if (byLevel) Level = byLevel; if (byLevel) Time = (int)time(0);
			MainSvrT::SetGuildMember(szName,i,byClass,Level,Point,Time);
			Tools->Compile(Packet+NewSize,"dsbbdbd",i,szName,byClass,Level,Point,byLevel ? 1 : 0,Time);
			NewSize += strlen(szName) + 16; TotalPoint += Point;
		}
		
		std::string Name, LName, BuffName; int Level = 0, Exp = 0, Point = 0, Attendance = 0, BuffType = 0, Time = 0, xSize = 0;
		IChar IPlayer(player); MainSvrT::ReadGuild(GID,Name,LName,Level,Exp,Point,Attendance,BuffType,BuffName,Time,xSize);
		if (IPlayer.GetGID() == IPlayer.GetPID()) LName = IPlayer.GetName(); Name = GuildName; xSize = MemberSize;
		if (TotalPoint != Point) Point = TotalPoint;
		MainSvrT::UpdateGuild(GID,GuildName,LName.c_str(),Level,Exp,Point,Attendance,BuffType,BuffName.c_str(),Time,xSize);
		CPlayer::Write(player, Type, "bdssssssbm", 10, GID, GuildName, C1, C2, C3, C4, C5, MemberSize, Packet, Size);
		delete[] Packet;
	}

	void __cdecl FixGuildCreation(void *player, unsigned char Type, char *format, BYTE nType, BYTE mType)
	{
		if (mType == 4) mType = 5;
		else if (mType == 7) mType = 9;
		else if (mType == 8) mType = 10;
		else if (mType == 9) mType = 11;
		else if (mType == 11) mType = 13;
		else if (mType == 12) mType = 14;
		else if (mType == 13) mType = 15;
		else if (mType == 15) mType = 17;
		else if (mType == 16) mType = 18;
		else if (mType == 18) mType = 20;
		else if (mType == 19) mType = 21;
		else if (mType == 20) mType = 22;
		else if (mType == 22) mType = 24;
		else if (mType == 23) mType = 25;
		else if (mType == 24) mType = 26;
		else if (mType == 25) mType = 27;
		else if (mType == 29) mType = 31;
		else if (mType == 31) mType = 36;
		else if (mType == 33) mType = 38;
		else if (mType == 34) mType = 39;
		else if (mType == 36) mType = 41;
		else if (mType == 37) mType = 42;
		else if (mType == 38) mType = 43;
		else if (mType == 40) mType = 45;
		else if (mType == 41) mType = 46;
		else if (mType == 44) mType = 49;
		else if (mType == 45) mType = 50;
		else if (mType == 47) mType = 52;
		else if (mType == 49) mType = 54;
		else if (mType == 50) mType = 55;
		else if (mType == 51) mType = 56;
		else if (mType == 53) mType = 58;
		else if (mType == 55) mType = 60;
		else if (mType == 56) mType = 61;
		else if (mType == 57) mType = 67;
		else if (mType == 58) mType = 68;
		else if (mType == 59) mType = 69;
		else if (mType == 60) mType = 70;
		else if (mType == 61) mType = 71;
		else if (mType == 64) mType = 74;
		else if (mType == 68) mType = 78;
		else if (mType == 70) mType = 80;
		else if (mType == 71) mType = 81;
		else if (mType == 72) mType = 82;
		else if (mType == 73) mType = 83;
		else if (mType == 75) mType = 85;
		else if (mType == 77) mType = 87;
		else if (mType == 82) mType = 92;
		else if (mType == 83) mType = 93;
		else if (mType == 84) mType = 94;
		else if (mType == 86) mType = 96;
		else if (mType == 87) mType = 97;
		else if (mType == 90) mType = 100;
		else if (mType == 91) mType = 101;
		else if (mType == 92) mType = 102;
		else if (mType == 93) mType = 103;
		else if (mType == 94) mType = 104;
		else if (mType == 95) mType = 105;
		else if (mType == 96) mType = 106;
		else if (mType == 97) mType = 107;
		else if (mType == 103) mType = 113;
		else if (mType == 107) mType = 117;
		CPlayer::Write(player, Type, "bb", nType, mType);
	}

	long __cdecl FixGuildConfluxComplete(unsigned char Type, const char *Format, BYTE nType, BYTE mType, char *Name, BYTE Value)
	{
		int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
		MainSvrT::ReadGuildMember(Name,Count,Class,Level,Point,Time);
		return CObject::WriteExclusive(Type, "bbdsbdb", nType, 32, Count, Name, Value, (int)time(0), 1);
	}

	long __cdecl FixGuildConnect(unsigned char Type, const char *Format, BYTE nType, BYTE mType, char *Name, BYTE Value)
	{
		int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
		MainSvrT::ReadGuildMember(Name,Count,Class,Level,Point,Time);
		MainSvrT::SetGuildMember(Name,Count,Class,Level,Point,(int)time(0));
		return CObject::WriteExclusive(Type, "bbdb", nType, mType, Count, Level);
	}
	
	long __cdecl FixGuildDisconnect(unsigned char Type, const char *Format, BYTE nType, BYTE mType, char *Name)
	{
		int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
		MainSvrT::ReadGuildMember(Name,Count,Class,Level,Point,Time);
		return CObject::WriteExclusive(Type, "bbd", nType, mType, Count);
	}

	long __cdecl FixGuildSession(unsigned char Type, const char *Format, BYTE nType, BYTE mType, char *Name)
	{
		int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
		MainSvrT::ReadGuildMember(Name,Count,Class,Level,Point,Time);
		return CObject::WriteExclusive(Type, "bbd", nType, 37, Count);
	}

	long __cdecl FixGuildAppointmentComplete(unsigned char Type, const char *Format, BYTE nType, BYTE mType, char *Name, BYTE Value)
	{
		int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
		MainSvrT::ReadGuildMember(Name,Count,Class,Level,Point,Time);
		return CObject::WriteExclusive(Type, "bbdb", nType, 47, Count, Value);
	}

	long __cdecl FixGuildAllianceInfo(unsigned char Type, const char *Format, BYTE nType, int Time, BYTE ASize, int GID, char *AGuild, char *ALeader)
	{
		return CObject::WriteExclusive(Type, "bddbdss", 26, 0, Time, 1, GID, AGuild, ALeader);
	}

	void __cdecl FixGuildAllianceInfoShow(void *Player, unsigned char Type, const char *Format, BYTE nType, int Time, BYTE ASize, char *Ally, int AllyLen)
	{
		CPlayer::Write(Player,Type, "bddbm", 26, 0, Time, ASize, Ally, AllyLen);
	}

	long __cdecl FixGuildBanish(unsigned char Type, const char *Format, BYTE nType, BYTE mType, char *Name)
	{
		int Count = 0, Class = 0, Level = 0, Point = 0, Time = 0;
		MainSvrT::ReadGuildMember(Name,Count,Class,Level,Point,Time);
		return CObject::WriteExclusive(Type, "bbd", nType, 40, Count);
	}

	int __fastcall MySetGID(void *Player, void *edx, int Value)
	{
		IChar IPlayer(Player);
		if (IPlayer.IsOnline() && IPlayer.GetGID()) MainSvrT::DeleteGuild(IPlayer.GetGID());
		return CPlayer::SetGID(Player,Value);
	}

	int __fastcall MyGetGID(void *Player, void *edx)
	{
		IChar IPlayer(Player);
		MainSvrT::DeleteJoinRequest(IPlayer.GetName());
		return CPlayer::GetGID(Player);
	}

	void MyCPlayerSendCreate()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x00458358,MainSvrT::CPlayerSendCreate);
		Memory->Hook(0x004514A1,MainSvrT::CPlayerSendCreate);
		Memory->Hook(0x00458412,MainSvrT::CPlayerSendExclusiveCreate);
		Memory->Hook(0x004918EB,MainSvrT::CPlayerSendExclusiveCreate);
		Memory->Hook(0x0045D088,MainSvrT::CPlayerSendExclusiveCreate);
		Memory->Hook(0x004512E0,MainSvrT::PlayerLogOutDuplicateFix);
		Memory->Hook(0x0045CF12,MainSvrT::PlayerTeleportDuplicateFix);
		Memory->Hook(0x004917ED,MainSvrT::PlayerMoveFix);
		Memory->Hook(0x0049182D,MainSvrT::PlayerMoveFix);
		Memory->Hook(0x0041514B,MainSvrT::FixGuildBasicInfo);
		Memory->Hook(0x00415241,MainSvrT::FixGuildBasicInfo);
		Memory->Hook(0x00415104,MainSvrT::FixGuildMemberInfo);
		BYTE FixGuilView = 15;
		Memory->Copy((void*)0x00412862, &FixGuilView, 1);
		Memory->Copy((void*)0x00413F92, &FixGuilView, 1);
		Memory->Copy((void*)0x0041420F, &FixGuilView, 1);
		Memory->Copy((void*)0x0041479A, &FixGuilView, 1);
		Memory->Copy((void*)0x00414A4D, &FixGuilView, 1);
		Memory->Copy((void*)0x00414D31, &FixGuilView, 1);
		Memory->Copy((void*)0x004168C7, &FixGuilView, 1);
		BYTE FixGuildCreateConsentAnswer = 3;
		Memory->Copy((void*)0x00412FD1, &FixGuildCreateConsentAnswer, 1);
		BYTE FixGuilCreateConsentComplete = 5;
		Memory->Copy((void*)0x00413276, &FixGuilCreateConsentComplete, 1);
		BYTE FixGuilAdminInfo = 13;
		Memory->Copy((void*)0x004151D8, &FixGuilAdminInfo, 1);
		BYTE FixGuilTodayMessage = 6;
		Memory->Copy((void*)0x004124C1, &FixGuilTodayMessage, 1);
		Memory->Copy((void*)0x00414F0B, &FixGuilTodayMessage, 1);
		Memory->Copy((void*)0x00414E84, &FixGuilTodayMessage, 1);
		Memory->Hook(0x00455574,MainSvrT::FixGuildCreation);
		Memory->Hook(0x0040D2AE,MainSvrT::FixGuildCreation);
		Memory->Hook(0x0046118F,MainSvrT::FixGuildCreation);
		Memory->Hook(0x00461211,MainSvrT::FixGuildCreation);
		Memory->Hook(0x004612DD,MainSvrT::FixGuildCreation);
		Memory->Hook(0x00461358,MainSvrT::FixGuildCreation);
		Memory->Hook(0x00461461,MainSvrT::FixGuildCreation);
		Memory->Hook(0x004614DE,MainSvrT::FixGuildCreation);
		Memory->Hook(0x0046155B,MainSvrT::FixGuildCreation);
		Memory->Hook(0x00461685,MainSvrT::FixGuildCreation);
		BYTE FixGuildCreateConsentWait = 16;
		Memory->Copy((void*)0x00413007, &FixGuildCreateConsentWait, 1);
		BYTE FixGuildCreateConsent = 19;
		Memory->Copy((void*)0x004130EF, &FixGuildCreateConsent, 1);
		BYTE FixGuildCreateComplete = 23;
		Memory->Copy((void*)0x004136B6, &FixGuildCreateComplete, 1);
		BYTE FixGuildExpAdd = 57;
		Memory->Copy((void*)0x00415889, &FixGuildExpAdd, 1);
		BYTE FixGuildTitleInvalid = 50;
		Memory->Copy((void*)0x004613EB, &FixGuildTitleInvalid, 1);
		BYTE FixGuildWarChangeLord = 116;
		Memory->Copy((void*)0x0041C2B7, &FixGuildWarChangeLord, 1);
		BYTE FixGuildWarExtendedTime = 115;
		Memory->Copy((void*)0x0041C1B4, &FixGuildWarExtendedTime, 1);
		BYTE FixGuildWarEndWin = 89;
		Memory->Copy((void*)0x0041BF1B, &FixGuildWarEndWin, 1);
		BYTE FixGuildWarLordDeclareTime = 114;
		Memory->Copy((void*)0x0041BE6E, &FixGuildWarLordDeclareTime, 1);
		BYTE FixGuildWarTime = 110;
		Memory->Copy((void*)0x0041BDB1, &FixGuildWarTime, 1);
		BYTE FixGuildWarBrokenStandard = 108;
		Memory->Copy((void*)0x0041B2A8, &FixGuildWarBrokenStandard, 1);
		Memory->Copy((void*)0x0041B303, &FixGuildWarBrokenStandard, 1);
		BYTE FixGuildWarDeclareList = 29;
		Memory->Copy((void*)0x0041B025, &FixGuildWarDeclareList, 1);
		BYTE FixGuildWarDeclare = 86;
		Memory->Copy((void*)0x0041AB48, &FixGuildWarDeclare, 1);
		Memory->Copy((void*)0x0041AD50, &FixGuildWarDeclare, 1);
		BYTE FixGuildWarBegin = 88;
		Memory->Copy((void*)0x00419FB3, &FixGuildWarBegin, 1);
		Memory->Copy((void*)0x0041A89E, &FixGuildWarBegin, 1);
		Memory->Copy((void*)0x0041A94C, &FixGuildWarBegin, 1);
		Memory->Copy((void*)0x0041A9EC, &FixGuildWarBegin, 1);
		BYTE FixGuildAllianceExpired = 77;
		Memory->Copy((void*)0x004194EA, &FixGuildAllianceExpired, 1);
		BYTE FixGuildAlliancePostpone = 79;
		Memory->Copy((void*)0x0041943B, &FixGuildAlliancePostpone, 1);
		BYTE FixGuildSetTaxRateComplete = 98;
		Memory->Copy((void*)0x00417EE3, &FixGuildSetTaxRateComplete, 1);
		BYTE FixGuildCastleInfo = 32;
		Memory->Copy((void*)0x00417E03, &FixGuildCastleInfo, 1);
		BYTE FixGuildWarBrokenGate = 109;
		Memory->Copy((void*)0x00417C28, &FixGuildWarBrokenGate, 1);
		BYTE FixGuildWarUnfuelComplete = 95;
		Memory->Copy((void*)0x00417B62, &FixGuildWarUnfuelComplete, 1);
		BYTE FixGuildAllianceCancel = 76;
		Memory->Copy((void*)0x00417316, &FixGuildAllianceCancel, 1);
		Memory->Copy((void*)0x00417334, &FixGuildAllianceCancel, 1);
		BYTE FixGuildAllianceComplete = 75;
		Memory->Copy((void*)0x004172DD, &FixGuildAllianceComplete, 1);
		BYTE FixGuildAllianceOfferSend = 73;
		Memory->Copy((void*)0x00416FB7, &FixGuildAllianceOfferSend, 1);
		BYTE FixGuildAllianceOffer = 72;
		Memory->Copy((void*)0x00416F99, &FixGuildAllianceOffer, 1);
		BYTE FixGuildMemberLevelUp = 59;
		Memory->Copy((void*)0x00415B7D, &FixGuildMemberLevelUp, 1);
		BYTE FixGuildSetStandardComplete = 53;
		Memory->Copy((void*)0x00415512, &FixGuildSetStandardComplete, 1);
		BYTE FixGuildTitleComplete = 51;
		Memory->Copy((void*)0x00414C40, &FixGuildTitleComplete, 1);
		BYTE FixGuildSubleaderDateLimit = 44;
		Memory->Copy((void*)0x0041442C, &FixGuildSubleaderDateLimit, 1);
		Memory->Copy((void*)0x0041453A, &FixGuildSubleaderDateLimit, 1);
		BYTE FixGuildConfluxCancel = 30;
		Memory->Copy((void*)0x00413B97, &FixGuildConfluxCancel, 1);
		BYTE FixGuildConflux = 23;
		Memory->Copy((void*)0x00413A1D, &FixGuildConflux, 1);
		BYTE FixGuildSendOk = 29;
		Memory->Copy((void*)0x004139EC, &FixGuildSendOk, 1);
		BYTE FixGuildDateLimit = 8;
		Memory->Copy((void*)0x00412F8B, &FixGuildDateLimit, 1);
		Memory->Copy((void*)0x00413C38, &FixGuildDateLimit, 1);
		BYTE FixGuildCheckStandard = 37;
		Memory->Copy((void*)0x0040D374, &FixGuildCheckStandard, 1);
		BYTE FixGuildDissolution = 48;
		Memory->Copy((void*)0x00412834, &FixGuildDissolution, 1);
		Memory->Copy((void*)0x00414A1F, &FixGuildDissolution, 1);
		Memory->Hook(0x00413D6D,MainSvrT::FixGuildConfluxComplete);
		Memory->Hook(0x0041246E,MainSvrT::FixGuildConnect);
		Memory->Hook(0x00415AFB,MainSvrT::FixGuildConnect);
		Memory->Hook(0x00415B1A,MainSvrT::FixGuildDisconnect);
		Memory->Hook(0x004126E5,MainSvrT::FixGuildDisconnect);
		Memory->Hook(0x00412580,MainSvrT::FixGuildDisconnect);
		Memory->Hook(0x004129FA,MainSvrT::FixGuildSession);
		Memory->Hook(0x00413FF7,MainSvrT::FixGuildSession);
		Memory->Hook(0x00414859,MainSvrT::FixGuildAppointmentComplete);
		BYTE FixGuildTaxLevy = 111;
		Memory->Copy((void*)0x00417FDE, &FixGuildTaxLevy, 1);
		BYTE FixGuildSetStandardFailedByLevel = 112;
		Memory->Copy((void*)0x004155C9, &FixGuildSetStandardFailedByLevel, 1);
		Memory->Hook(0x0041925A,MainSvrT::FixGuildAllianceInfo);
		Memory->Hook(0x00419664,MainSvrT::FixGuildAllianceInfoShow);
		Memory->Hook(0x0041415F,MainSvrT::FixGuildBanish);
		Memory->Hook(0x004149D7,MainSvrT::MySetGID);
		Memory->Hook(0x00413A63,MainSvrT::MyGetGID);
	}
}