void __fastcall WeaponApplySpec(int Item, void *edx, int Player)
{
	IItem IItem((void*)Item); IChar IPlayer((void*)Player);

	if (IPlayer.IsOnline())
	{
		int atk = IPlayer.GetMaxPhyAttack();
		CItemWeapon::ApplySpec(Item, Player);
		if (ConfigWeaponSkins.count(IItem.CheckIndex())) WSuit[IPlayer.GetPID()].IID = IItem.GetIID();
		if (ConfigWeaponSkins.count(IItem.CheckIndex())) WSuit[IPlayer.GetPID()].Index = IItem.CheckIndex();
		if (ConfigWeaponSkins.count(IItem.CheckIndex())) WSuit[IPlayer.GetPID()].Active = 1;
		if (ConfigWeaponSkins.count(IItem.CheckIndex())) CPlayer::Write(IPlayer.GetOffset(),234,"ddwb",IPlayer.GetID(),27,IItem.CheckIndex(),0);
		int check = IPlayer.GetMaxPhyAttack() - (atk + (*(DWORD*)(Item + 100) * (2 * *(DWORD*)(Item + 100) + 7) / 9));
		int FirstDemonGongType = 0, SecondDemonGongType = 0, FirstDemonGongStat = 0, SecondDemonGongStat = 0;
		int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock; int DSSType = 0;
		MainSvrT::ReadItemTable(IItem.GetIID(),GetDSS,GetStat,GetTime,Lock);
		if (GetDSS) DSSType = GetDSS & 0xFF;

		if (GetStat)
		{
			FirstDemonGongType = (GetStat % 100000000) / 10000000;
			SecondDemonGongType = (GetStat % 1000000) / 100000;
			FirstDemonGongStat = (GetStat % 10000000) / 1000000;
			SecondDemonGongStat = (GetStat % 100000) / 10000;
		}

		if (FirstDemonGongType)
		{
			if (FirstDemonGongType == 1)
			{
				IPlayer.AddPhyAttack(DemonGongStatWeaponEarth[0][FirstDemonGongStat]);
				IPlayer.AddMagAttack(DemonGongStatWeaponEarth[0][FirstDemonGongStat]);
				IPlayer.AddOTP(DemonGongStatWeaponEarth[1][FirstDemonGongStat]);
			}

			if (FirstDemonGongType == 2)
			{
				IPlayer.AddPhyAttack(DemonGongStatWeaponWind[0][FirstDemonGongStat]);
				IPlayer.AddMagAttack(DemonGongStatWeaponWind[0][FirstDemonGongStat]);
				IPlayer.AddEva(DemonGongStatWeaponWind[1][FirstDemonGongStat]);
			}

			if (FirstDemonGongType == 3)
			{
				IPlayer.AddPhyAttack(DemonGongStatWeaponWater[0][FirstDemonGongStat]);
				IPlayer.AddMagAttack(DemonGongStatWeaponWater[0][FirstDemonGongStat]);
				IPlayer.IncreaseMaxHp(DemonGongStatWeaponWater[1][FirstDemonGongStat]);
			}
		}

		if (SecondDemonGongType)
		{
			if (SecondDemonGongType == 1)
			{
				IPlayer.AddPhyAttack(DemonGongStatWeaponEarth[0][SecondDemonGongStat]);
				IPlayer.AddMagAttack(DemonGongStatWeaponEarth[0][SecondDemonGongStat]);
				IPlayer.AddOTP(DemonGongStatWeaponEarth[1][SecondDemonGongStat]);
			}

			if (SecondDemonGongType == 2)
			{
				IPlayer.AddPhyAttack(DemonGongStatWeaponWind[0][SecondDemonGongStat]);
				IPlayer.AddMagAttack(DemonGongStatWeaponWind[0][SecondDemonGongStat]);
				IPlayer.AddEva(DemonGongStatWeaponWind[1][SecondDemonGongStat]);
			}

			if (SecondDemonGongType == 3)
			{
				IPlayer.AddPhyAttack(DemonGongStatWeaponWater[0][SecondDemonGongStat]);
				IPlayer.AddMagAttack(DemonGongStatWeaponWater[0][SecondDemonGongStat]);
				IPlayer.IncreaseMaxHp(DemonGongStatWeaponWater[1][SecondDemonGongStat]);
			}
		}

		if (GetStat > 0)
		{
			int QigongType = GetStat % 100;

			if (QigongType)
			{
				IPlayer.AddMagAttack(check*QigongWeapon[QigongType-1][0]/100);
				IPlayer.AddPhyAttack(check*QigongWeapon[QigongType-1][1]/100);
				IPlayer.IncreaseMaxHp(QigongWeapon[QigongType-1][2]);
				IPlayer.IncreaseMaxMp(QigongWeapon[QigongType-1][3]);

				if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
					IPlayer.AddEva(QigongWeapon[QigongType-1][4]);
				else
					IPlayer.AddOTP(QigongWeapon[QigongType-1][4]);

				int Type = QigongWeapon[QigongType-1][5];
				int Stat = QigongWeapon[QigongType-1][6];

				if (Type == 0)
					IPlayer.AddStr(Stat);
				else if (Type == 1)
					IPlayer.AddHp(Stat);
				else if (Type == 2)
					IPlayer.AddInt(Stat);
				else if (Type == 3)
					IPlayer.AddWis(Stat);
				else if (Type == 4)
					IPlayer.AddAgi(Stat);
			}
		}

		if (DSSType == 240 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*17/100);
			IPlayer.AddPhyAttack(check*17/100);
			IPlayer.AddAgi(8);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(18);
			else
				IPlayer.AddOTP(18);

			IPlayer.IncreaseMaxHp(250);
			IPlayer.IncreaseMaxMp(200);
		} else if (DSSType == 239 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*17/100);
			IPlayer.AddPhyAttack(check*17/100);
			IPlayer.AddWis(8);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(18);
			else
				IPlayer.AddOTP(18);

			IPlayer.IncreaseMaxHp(250);
			IPlayer.IncreaseMaxMp(200);
		} else if (DSSType == 238 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*17/100);
			IPlayer.AddPhyAttack(check*17/100);
			IPlayer.AddInt(8);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(18);
			else
				IPlayer.AddOTP(18);

			IPlayer.IncreaseMaxHp(250);
			IPlayer.IncreaseMaxMp(200);
		} else if (DSSType == 237 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*17/100);
			IPlayer.AddPhyAttack(check*17/100);
			IPlayer.AddHp(8);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(18);
			else
				IPlayer.AddOTP(18);

			IPlayer.IncreaseMaxHp(250);
			IPlayer.IncreaseMaxMp(200);
		} else if (DSSType == 236 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*17/100);
			IPlayer.AddPhyAttack(check*17/100);
			IPlayer.AddStr(8);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(18);
			else
				IPlayer.AddOTP(18);

			IPlayer.IncreaseMaxHp(250);
			IPlayer.IncreaseMaxMp(200);
		} else if (DSSType == 235 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*14/100);
			IPlayer.AddPhyAttack(check*14/100);
			IPlayer.AddAgi(5);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(15);
			else
				IPlayer.AddOTP(15);

			IPlayer.IncreaseMaxHp(200);
			IPlayer.IncreaseMaxMp(150);
		} else if (DSSType == 234 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*14/100);
			IPlayer.AddPhyAttack(check*14/100);
			IPlayer.AddWis(5);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(15);
			else
				IPlayer.AddOTP(15);

			IPlayer.IncreaseMaxHp(200);
			IPlayer.IncreaseMaxMp(150);
		} else if (DSSType == 233 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*14/100);
			IPlayer.AddPhyAttack(check*14/100);
			IPlayer.AddInt(5);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(15);
			else
				IPlayer.AddOTP(15);

			IPlayer.IncreaseMaxHp(200);
			IPlayer.IncreaseMaxMp(150);
		} else if (DSSType == 232 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*14/100);
			IPlayer.AddPhyAttack(check*14/100);
			IPlayer.AddHp(5);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(15);
			else
				IPlayer.AddOTP(15);

			IPlayer.IncreaseMaxHp(200);
			IPlayer.IncreaseMaxMp(150);
		} else if (DSSType == 231 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*14/100);
			IPlayer.AddPhyAttack(check*14/100);
			IPlayer.AddStr(5);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(15);
			else
				IPlayer.AddOTP(15);

			IPlayer.IncreaseMaxHp(200);
			IPlayer.IncreaseMaxMp(150);
		} else if (DSSType == 230 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*12/100);
			IPlayer.AddPhyAttack(check*12/100);
			IPlayer.AddAgi(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(12);
			else
				IPlayer.AddOTP(12);

			IPlayer.IncreaseMaxHp(130);
			IPlayer.IncreaseMaxMp(110);
		} else if (DSSType == 229 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*12/100);
			IPlayer.AddPhyAttack(check*12/100);
			IPlayer.AddWis(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(12);
			else
				IPlayer.AddOTP(12);

			IPlayer.IncreaseMaxHp(130);
			IPlayer.IncreaseMaxMp(110);
		} else if (DSSType == 228 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*12/100);
			IPlayer.AddPhyAttack(check*12/100);
			IPlayer.AddInt(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(12);
			else
				IPlayer.AddOTP(12);

			IPlayer.IncreaseMaxHp(130);
			IPlayer.IncreaseMaxMp(110);
		} else if (DSSType == 227 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*12/100);
			IPlayer.AddPhyAttack(check*12/100);
			IPlayer.AddHp(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(12);
			else
				IPlayer.AddOTP(12);

			IPlayer.IncreaseMaxHp(130);
			IPlayer.IncreaseMaxMp(110);
		} else if (DSSType == 226 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*12/100);
			IPlayer.AddPhyAttack(check*12/100);
			IPlayer.AddStr(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(12);
			else
				IPlayer.AddOTP(12);

			IPlayer.IncreaseMaxHp(130);
			IPlayer.IncreaseMaxMp(110);
		} else if (DSSType == 225 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*10/100);
			IPlayer.AddPhyAttack(check*10/100);
			IPlayer.AddAgi(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(10);
			else
				IPlayer.AddOTP(10);

			IPlayer.IncreaseMaxHp(110);
			IPlayer.IncreaseMaxMp(90);
		} else if (DSSType == 224 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*10/100);
			IPlayer.AddPhyAttack(check*10/100);
			IPlayer.AddWis(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(10);
			else
				IPlayer.AddOTP(10);

			IPlayer.IncreaseMaxHp(110);
			IPlayer.IncreaseMaxMp(90);
		} else if (DSSType == 223 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*10/100);
			IPlayer.AddPhyAttack(check*10/100);
			IPlayer.AddInt(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(10);
			else
				IPlayer.AddOTP(10);

			IPlayer.IncreaseMaxHp(110);
			IPlayer.IncreaseMaxMp(90);
		} else if (DSSType == 222 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*10/100);
			IPlayer.AddPhyAttack(check*10/100);
			IPlayer.AddHp(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(10);
			else
				IPlayer.AddOTP(10);

			IPlayer.IncreaseMaxHp(110);
			IPlayer.IncreaseMaxMp(90);
		} else if (DSSType == 221 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*10/100);
			IPlayer.AddPhyAttack(check*10/100);
			IPlayer.AddStr(3);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(10);
			else
				IPlayer.AddOTP(10);

			IPlayer.IncreaseMaxHp(110);
			IPlayer.IncreaseMaxMp(90);
		} else if (DSSType == 220 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*8/100);
			IPlayer.AddPhyAttack(check*8/100);
			IPlayer.AddAgi(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(8);
			else
				IPlayer.AddOTP(8);

			IPlayer.IncreaseMaxHp(90);
			IPlayer.IncreaseMaxMp(70);
		} else if (DSSType == 219 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*8/100);
			IPlayer.AddPhyAttack(check*8/100);
			IPlayer.AddWis(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(8);
			else
				IPlayer.AddOTP(8);

			IPlayer.IncreaseMaxHp(90);
			IPlayer.IncreaseMaxMp(70);
		} else if (DSSType == 218 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*8/100);
			IPlayer.AddPhyAttack(check*8/100);
			IPlayer.AddInt(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(8);
			else
				IPlayer.AddOTP(8);

			IPlayer.IncreaseMaxHp(90);
			IPlayer.IncreaseMaxMp(70);
		} else if (DSSType == 217 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*8/100);
			IPlayer.AddPhyAttack(check*8/100);
			IPlayer.AddHp(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(8);
			else
				IPlayer.AddOTP(8);

			IPlayer.IncreaseMaxHp(90);
			IPlayer.IncreaseMaxMp(70);
		} else if (DSSType == 216 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*8/100);
			IPlayer.AddPhyAttack(check*8/100);
			IPlayer.AddStr(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(8);
			else
				IPlayer.AddOTP(8);

			IPlayer.IncreaseMaxHp(90);
			IPlayer.IncreaseMaxMp(70);
		} else if (DSSType == 215 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*6/100);
			IPlayer.AddPhyAttack(check*6/100);
			IPlayer.AddAgi(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(6);
			else
				IPlayer.AddOTP(6);

			IPlayer.IncreaseMaxHp(70);
			IPlayer.IncreaseMaxMp(50);
		} else if (DSSType == 214 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*6/100);
			IPlayer.AddPhyAttack(check*6/100);
			IPlayer.AddWis(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(6);
			else
				IPlayer.AddOTP(6);

			IPlayer.IncreaseMaxHp(70);
			IPlayer.IncreaseMaxMp(50);
		} else if (DSSType == 213 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*6/100);
			IPlayer.AddPhyAttack(check*6/100);
			IPlayer.AddInt(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(6);
			else
				IPlayer.AddOTP(6);

			IPlayer.IncreaseMaxHp(70);
			IPlayer.IncreaseMaxMp(50);
		} else if (DSSType == 212 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*6/100);
			IPlayer.AddPhyAttack(check*6/100);
			IPlayer.AddHp(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(6);
			else
				IPlayer.AddOTP(6);

			IPlayer.IncreaseMaxHp(70);
			IPlayer.IncreaseMaxMp(50);
		} else if (DSSType == 211 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*6/100);
			IPlayer.AddPhyAttack(check*6/100);
			IPlayer.AddStr(2);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(6);
			else
				IPlayer.AddOTP(6);

			IPlayer.IncreaseMaxHp(70);
			IPlayer.IncreaseMaxMp(50);
		} else if (DSSType == 210 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*4/100);
			IPlayer.AddPhyAttack(check*4/100);
			IPlayer.AddAgi(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(4);
			else
				IPlayer.AddOTP(4);

			IPlayer.IncreaseMaxHp(50);
			IPlayer.IncreaseMaxMp(30);
		} else if (DSSType == 209 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*4/100);
			IPlayer.AddPhyAttack(check*4/100);
			IPlayer.AddWis(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(4);
			else
				IPlayer.AddOTP(4);

			IPlayer.IncreaseMaxHp(50);
			IPlayer.IncreaseMaxMp(30);
		} else if (DSSType == 208 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*4/100);
			IPlayer.AddPhyAttack(check*4/100);
			IPlayer.AddInt(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(4);
			else
				IPlayer.AddOTP(4);

			IPlayer.IncreaseMaxHp(50);
			IPlayer.IncreaseMaxMp(30);
		} else if (DSSType == 207 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*4/100);
			IPlayer.AddPhyAttack(check*4/100);
			IPlayer.AddHp(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(4);
			else
				IPlayer.AddOTP(4);

			IPlayer.IncreaseMaxHp(50);
			IPlayer.IncreaseMaxMp(30);
		} else if (DSSType == 206 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*4/100);
			IPlayer.AddPhyAttack(check*4/100);
			IPlayer.AddStr(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(4);
			else
				IPlayer.AddOTP(4);

			IPlayer.IncreaseMaxHp(50);
			IPlayer.IncreaseMaxMp(30);
		} else if (DSSType == 205 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*2/100);
			IPlayer.AddPhyAttack(check*2/100);
			IPlayer.AddAgi(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(2);
			else
				IPlayer.AddOTP(2);

			IPlayer.IncreaseMaxHp(30);
			IPlayer.IncreaseMaxMp(10);
		} else if (DSSType == 204 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*2/100);
			IPlayer.AddPhyAttack(check*2/100);
			IPlayer.AddWis(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(2);
			else
				IPlayer.AddOTP(2);

			IPlayer.IncreaseMaxHp(30);
			IPlayer.IncreaseMaxMp(10);
		} else if (DSSType == 203 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*2/100);
			IPlayer.AddPhyAttack(check*2/100);
			IPlayer.AddInt(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(2);
			else
				IPlayer.AddOTP(2);

			IPlayer.IncreaseMaxHp(30);
			IPlayer.IncreaseMaxMp(10);
		} else if (DSSType == 202 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*2/100);
			IPlayer.AddPhyAttack(check*2/100);
			IPlayer.AddHp(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(2);
			else
				IPlayer.AddOTP(2);

			IPlayer.IncreaseMaxHp(30);
			IPlayer.IncreaseMaxMp(10);
		} else if (DSSType == 201 && IItem.GetInfo() & 1)
		{
			IPlayer.AddMagAttack(check*2/100);
			IPlayer.AddPhyAttack(check*2/100);
			IPlayer.AddStr(1);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.AddEva(2);
			else
				IPlayer.AddOTP(2);

			IPlayer.IncreaseMaxHp(30);
			IPlayer.IncreaseMaxMp(10);
		}
	}
}

void __fastcall WeaponPutOff(void *Item,void *edx,int Player)
{
	IChar IPlayer((void*)Player);
	IItem IItem((void*)Item);

	if (IPlayer.IsBuff(256))
	{
		IPlayer.SystemMessage("You need to wait 3 seconds to put off your weapon.", TEXTCOLOR_INFOMSG);
	} else {
		IPlayer.Buff(256,2,0);

		if (!ConfigWeaponSkins.count(IItem.CheckIndex()))
		{
			if (WSuit.count(IPlayer.GetPID()) && WSuit.find(IPlayer.GetPID())->second.Index)
			{
				IPlayer.BoxMsg("You must to put off your weapon skin first.");
				return;
			}
		}

		if (ConfigWeaponSkins.count(IItem.CheckIndex()))
		{
			if (WSuit.count(IPlayer.GetPID()) && WSuit.find(IPlayer.GetPID())->second.Index)
			{
				if (CItem::IsState((int)Item,1) && MainSvrT::CPlayerIsWState(Player,0,-116))
				{
					MainSvrT::CPlayerSubWState(Player,0,-116);
					CItem::SubState((int)Item,1);
					WSuit[IPlayer.GetPID()].IID = 0;
					WSuit[IPlayer.GetPID()].Index = 0;
					WSuit[IPlayer.GetPID()].Active = 0;
					(*(void (__thiscall **)(int, int))(*(DWORD*)Item + 108))((int)Item,Player);
					CChar::WriteInSight(IPlayer.GetOffset(),6,"ddw",IPlayer.GetID(),IItem.GetIID(),IItem.CheckIndex());
					CDBSocket::Write(5,"d",IItem.GetIID());
					return;
				}
			}

			return;
		}

		CItemWeapon::PutOff(Item, Player);
	}
}

void __fastcall CItemWeaponFreeSpec(void *Item,void *edx,int Player)
{
	IChar IPlayer((void*)Player); IItem IItem((void*)Item);
	int atk = IPlayer.GetMaxPhyAttack();
	CItemWeapon::FreeSpec(Item, Player);
	int check = atk - (IPlayer.GetMaxPhyAttack() + (*(DWORD*)((int)Item + 100) * (2 * *(DWORD*)((int)Item + 100) + 7) / 9));
	int FirstDemonGongType = 0, SecondDemonGongType = 0, FirstDemonGongStat = 0, SecondDemonGongStat = 0;
	int GetDSS = 0, GetStat = 0, GetTime = 0; std::string Lock; int DSSType = 0;
	MainSvrT::ReadItemTable(IItem.GetIID(),GetDSS,GetStat,GetTime,Lock);
	if (GetDSS) DSSType = GetDSS & 0xFF;

	if (GetStat)
	{
		FirstDemonGongType = (GetStat % 100000000) / 10000000;
		SecondDemonGongType = (GetStat % 1000000) / 100000;
		FirstDemonGongStat = (GetStat % 10000000) / 1000000;
		SecondDemonGongStat = (GetStat % 100000) / 10000;
	}

	if (FirstDemonGongType)
	{
		if (FirstDemonGongType == 1)
		{
			IPlayer.RemovePhyAttack(DemonGongStatWeaponEarth[0][FirstDemonGongStat]);
			IPlayer.RemoveMagAttack(DemonGongStatWeaponEarth[0][FirstDemonGongStat]);
			IPlayer.RemoveOTP(DemonGongStatWeaponEarth[1][FirstDemonGongStat]);
		}

		if (FirstDemonGongType == 2)
		{
			IPlayer.RemovePhyAttack(DemonGongStatWeaponWind[0][FirstDemonGongStat]);
			IPlayer.RemoveMagAttack(DemonGongStatWeaponWind[0][FirstDemonGongStat]);
			IPlayer.RemoveEva(DemonGongStatWeaponWind[1][FirstDemonGongStat]);
		}

		if (FirstDemonGongType == 3)
		{
			IPlayer.RemovePhyAttack(DemonGongStatWeaponWater[0][FirstDemonGongStat]);
			IPlayer.RemoveMagAttack(DemonGongStatWeaponWater[0][FirstDemonGongStat]);
			IPlayer.DecreaseMaxHp(DemonGongStatWeaponWater[1][FirstDemonGongStat]);
		}
	}

	if (SecondDemonGongType)
	{
		if (SecondDemonGongType == 1)
		{
			IPlayer.RemovePhyAttack(DemonGongStatWeaponEarth[0][SecondDemonGongStat]);
			IPlayer.RemoveMagAttack(DemonGongStatWeaponEarth[0][SecondDemonGongStat]);
			IPlayer.RemoveOTP(DemonGongStatWeaponEarth[1][SecondDemonGongStat]);
		}

		if (SecondDemonGongType == 2)
		{
			IPlayer.RemovePhyAttack(DemonGongStatWeaponWind[0][SecondDemonGongStat]);
			IPlayer.RemoveMagAttack(DemonGongStatWeaponWind[0][SecondDemonGongStat]);
			IPlayer.RemoveEva(DemonGongStatWeaponWind[1][SecondDemonGongStat]);
		}

		if (SecondDemonGongType == 3)
		{
			IPlayer.RemovePhyAttack(DemonGongStatWeaponWater[0][SecondDemonGongStat]);
			IPlayer.RemoveMagAttack(DemonGongStatWeaponWater[0][SecondDemonGongStat]);
			IPlayer.DecreaseMaxHp(DemonGongStatWeaponWater[1][SecondDemonGongStat]);
		}
	}

	if (GetStat > 0)
	{
		int QigongType = GetStat % 100;

		if (QigongType)
		{
			IPlayer.RemoveMagAttack(check*QigongWeapon[QigongType-1][0]/100);
			IPlayer.RemovePhyAttack(check*QigongWeapon[QigongType-1][1]/100);
			IPlayer.DecreaseMaxHp(QigongWeapon[QigongType-1][2]);
			IPlayer.DecreaseMaxMp(QigongWeapon[QigongType-1][3]);

			if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
				IPlayer.RemoveEva(QigongWeapon[QigongType-1][4]);
			else
				IPlayer.RemoveOTP(QigongWeapon[QigongType-1][4]);

			int Type = QigongWeapon[QigongType-1][5];
			int Stat = QigongWeapon[QigongType-1][6];

			if (Type == 0)
				IPlayer.RemoveStr(Stat);
			else if (Type == 1)
				IPlayer.RemoveHp(Stat);
			else if (Type == 2)
				IPlayer.RemoveInt(Stat);
			else if (Type == 3)
				IPlayer.RemoveWis(Stat);
			else if (Type == 4)
				IPlayer.RemoveAgi(Stat);
		}
	}

	if (DSSType == 240)
	{
		IPlayer.RemoveMagAttack(check*17/100);
		IPlayer.RemovePhyAttack(check*17/100);
		IPlayer.RemoveAgi(8);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(18);
		else
			IPlayer.RemoveOTP(18);

		IPlayer.DecreaseMaxHp(250);
		IPlayer.DecreaseMaxMp(200);
	} else if (DSSType == 239)
	{
		IPlayer.RemoveMagAttack(check*17/100);
		IPlayer.RemovePhyAttack(check*17/100);
		IPlayer.RemoveWis(8);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(18);
		else
			IPlayer.RemoveOTP(18);

		IPlayer.DecreaseMaxHp(250);
		IPlayer.DecreaseMaxMp(200);
	} else if (DSSType == 238)
	{
		IPlayer.RemoveMagAttack(check*17/100);
		IPlayer.RemovePhyAttack(check*17/100);
		IPlayer.RemoveInt(8);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(18);
		else
			IPlayer.RemoveOTP(18);

		IPlayer.DecreaseMaxHp(250);
		IPlayer.DecreaseMaxMp(200);
	} else if (DSSType == 237)
	{
		IPlayer.RemoveMagAttack(check*17/100);
		IPlayer.RemovePhyAttack(check*17/100);
		IPlayer.RemoveHp(8);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(18);
		else
			IPlayer.RemoveOTP(18);

		IPlayer.DecreaseMaxHp(250);
		IPlayer.DecreaseMaxMp(200);
	} else if (DSSType == 236)
	{
		IPlayer.RemoveMagAttack(check*17/100);
		IPlayer.RemovePhyAttack(check*17/100);
		IPlayer.RemoveStr(8);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(18);
		else
			IPlayer.RemoveOTP(18);

		IPlayer.DecreaseMaxHp(250);
		IPlayer.DecreaseMaxMp(200);
	} else if (DSSType == 235)
	{
		IPlayer.RemoveMagAttack(check*14/100);
		IPlayer.RemovePhyAttack(check*14/100);
		IPlayer.RemoveAgi(5);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(15);
		else
			IPlayer.RemoveOTP(15);

		IPlayer.DecreaseMaxHp(200);
		IPlayer.DecreaseMaxMp(150);
	} else if (DSSType == 234)
	{
		IPlayer.RemoveMagAttack(check*14/100);
		IPlayer.RemovePhyAttack(check*14/100);
		IPlayer.RemoveWis(5);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(15);
		else
			IPlayer.RemoveOTP(15);

		IPlayer.DecreaseMaxHp(200);
		IPlayer.DecreaseMaxMp(150);
	} else if (DSSType == 233)
	{
		IPlayer.RemoveMagAttack(check*14/100);
		IPlayer.RemovePhyAttack(check*14/100);
		IPlayer.RemoveInt(5);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(15);
		else
			IPlayer.RemoveOTP(15);

		IPlayer.DecreaseMaxHp(200);
		IPlayer.DecreaseMaxMp(150);
	} else if (DSSType == 232)
	{
		IPlayer.RemoveMagAttack(check*14/100);
		IPlayer.RemovePhyAttack(check*14/100);
		IPlayer.RemoveHp(5);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(15);
		else
			IPlayer.RemoveOTP(15);

		IPlayer.DecreaseMaxHp(200);
		IPlayer.DecreaseMaxMp(150);
	} else if (DSSType == 231)
	{
		IPlayer.RemoveMagAttack(check*14/100);
		IPlayer.RemovePhyAttack(check*14/100);
		IPlayer.RemoveStr(5);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(15);
		else
			IPlayer.RemoveOTP(15);

		IPlayer.DecreaseMaxHp(200);
		IPlayer.DecreaseMaxMp(150);
	} else if (DSSType == 230)
	{
		IPlayer.RemoveMagAttack(check*12/100);
		IPlayer.RemovePhyAttack(check*12/100);
		IPlayer.RemoveAgi(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(12);
		else
			IPlayer.RemoveOTP(12);

		IPlayer.DecreaseMaxHp(130);
		IPlayer.DecreaseMaxMp(110);
	} else if (DSSType == 229)
	{
		IPlayer.RemoveMagAttack(check*12/100);
		IPlayer.RemovePhyAttack(check*12/100);
		IPlayer.RemoveWis(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(12);
		else
			IPlayer.RemoveOTP(12);

		IPlayer.DecreaseMaxHp(130);
		IPlayer.DecreaseMaxMp(110);
	} else if (DSSType == 228)
	{
		IPlayer.RemoveMagAttack(check*12/100);
		IPlayer.RemovePhyAttack(check*12/100);
		IPlayer.RemoveInt(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(12);
		else
			IPlayer.RemoveOTP(12);

		IPlayer.DecreaseMaxHp(130);
		IPlayer.DecreaseMaxMp(110);
	} else if (DSSType == 227)
	{
		IPlayer.RemoveMagAttack(check*12/100);
		IPlayer.RemovePhyAttack(check*12/100);
		IPlayer.RemoveHp(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(12);
		else
			IPlayer.RemoveOTP(12);

		IPlayer.DecreaseMaxHp(130);
		IPlayer.DecreaseMaxMp(110);
	} else if (DSSType == 226)
	{
		IPlayer.RemoveMagAttack(check*12/100);
		IPlayer.RemovePhyAttack(check*12/100);
		IPlayer.RemoveStr(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(12);
		else
			IPlayer.RemoveOTP(12);

		IPlayer.DecreaseMaxHp(130);
		IPlayer.DecreaseMaxMp(110);
	} else if (DSSType == 225)
	{
		IPlayer.RemoveMagAttack(check*10/100);
		IPlayer.RemovePhyAttack(check*10/100);
		IPlayer.RemoveAgi(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(10);
		else
			IPlayer.RemoveOTP(10);

		IPlayer.DecreaseMaxHp(110);
		IPlayer.DecreaseMaxMp(90);
	} else if (DSSType == 224)
	{
		IPlayer.RemoveMagAttack(check*10/100);
		IPlayer.RemovePhyAttack(check*10/100);
		IPlayer.RemoveWis(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(10);
		else
			IPlayer.RemoveOTP(10);

		IPlayer.DecreaseMaxHp(110);
		IPlayer.DecreaseMaxMp(90);
	} else if (DSSType == 223)
	{
		IPlayer.RemoveMagAttack(check*10/100);
		IPlayer.RemovePhyAttack(check*10/100);
		IPlayer.RemoveInt(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(10);
		else
			IPlayer.RemoveOTP(10);

		IPlayer.DecreaseMaxHp(110);
		IPlayer.DecreaseMaxMp(90);
	} else if (DSSType == 222)
	{
		IPlayer.RemoveMagAttack(check*10/100);
		IPlayer.RemovePhyAttack(check*10/100);
		IPlayer.RemoveHp(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(10);
		else
			IPlayer.RemoveOTP(10);

		IPlayer.DecreaseMaxHp(110);
		IPlayer.DecreaseMaxMp(90);
	} else if (DSSType == 221)
	{
		IPlayer.RemoveMagAttack(check*10/100);
		IPlayer.RemovePhyAttack(check*10/100);
		IPlayer.RemoveStr(3);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(10);
		else
			IPlayer.RemoveOTP(10);

		IPlayer.DecreaseMaxHp(110);
		IPlayer.DecreaseMaxMp(90);
	} else if (DSSType == 220)
	{
		IPlayer.RemoveMagAttack(check*8/100);
		IPlayer.RemovePhyAttack(check*8/100);
		IPlayer.RemoveAgi(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(8);
		else
			IPlayer.RemoveOTP(8);

		IPlayer.DecreaseMaxHp(90);
		IPlayer.DecreaseMaxMp(70);
	} else if (DSSType == 219)
	{
		IPlayer.RemoveMagAttack(check*8/100);
		IPlayer.RemovePhyAttack(check*8/100);
		IPlayer.RemoveWis(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(8);
		else
			IPlayer.RemoveOTP(8);

		IPlayer.DecreaseMaxHp(90);
		IPlayer.DecreaseMaxMp(70);
	} else if (DSSType == 218)
	{
		IPlayer.RemoveMagAttack(check*8/100);
		IPlayer.RemovePhyAttack(check*8/100);
		IPlayer.RemoveInt(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(8);
		else
			IPlayer.RemoveOTP(8);

		IPlayer.DecreaseMaxHp(90);
		IPlayer.DecreaseMaxMp(70);
	} else if (DSSType == 217)
	{
		IPlayer.RemoveMagAttack(check*8/100);
		IPlayer.RemovePhyAttack(check*8/100);
		IPlayer.RemoveHp(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(8);
		else
			IPlayer.RemoveOTP(8);

		IPlayer.DecreaseMaxHp(90);
		IPlayer.DecreaseMaxMp(70);
	} else if (DSSType == 216)
	{
		IPlayer.RemoveMagAttack(check*8/100);
		IPlayer.RemovePhyAttack(check*8/100);
		IPlayer.RemoveStr(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(8);
		else
			IPlayer.RemoveOTP(8);

		IPlayer.DecreaseMaxHp(90);
		IPlayer.DecreaseMaxMp(70);
	} else if (DSSType == 215)
	{
		IPlayer.RemoveMagAttack(check*6/100);
		IPlayer.RemovePhyAttack(check*6/100);
		IPlayer.RemoveAgi(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(6);
		else
			IPlayer.RemoveOTP(6);

		IPlayer.DecreaseMaxHp(70);
		IPlayer.DecreaseMaxMp(50);
	} else if (DSSType == 214)
	{
		IPlayer.RemoveMagAttack(check*6/100);
		IPlayer.RemovePhyAttack(check*6/100);
		IPlayer.RemoveWis(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(6);
		else
			IPlayer.RemoveOTP(6);

		IPlayer.DecreaseMaxHp(70);
		IPlayer.DecreaseMaxMp(50);
	} else if (DSSType == 213)
	{
		IPlayer.RemoveMagAttack(check*6/100);
		IPlayer.RemovePhyAttack(check*6/100);
		IPlayer.RemoveInt(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(6);
		else
			IPlayer.RemoveOTP(6);

		IPlayer.DecreaseMaxHp(70);
		IPlayer.DecreaseMaxMp(50);
	} else if (DSSType == 212)
	{
		IPlayer.RemoveMagAttack(check*6/100);
		IPlayer.RemovePhyAttack(check*6/100);
		IPlayer.RemoveHp(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(6);
		else
			IPlayer.RemoveOTP(6);

		IPlayer.DecreaseMaxHp(70);
		IPlayer.DecreaseMaxMp(50);
	} else if (DSSType == 211)
	{
		IPlayer.RemoveMagAttack(check*6/100);
		IPlayer.RemovePhyAttack(check*6/100);
		IPlayer.RemoveStr(2);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(6);
		else
			IPlayer.RemoveOTP(6);

		IPlayer.DecreaseMaxHp(70);
		IPlayer.DecreaseMaxMp(50);
	} else if (DSSType == 210)
	{
		IPlayer.RemoveMagAttack(check*4/100);
		IPlayer.RemovePhyAttack(check*4/100);
		IPlayer.RemoveAgi(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(4);
		else
			IPlayer.RemoveOTP(4);

		IPlayer.DecreaseMaxHp(50);
		IPlayer.DecreaseMaxMp(30);
	} else if (DSSType == 209)
	{
		IPlayer.RemoveMagAttack(check*4/100);
		IPlayer.RemovePhyAttack(check*4/100);
		IPlayer.RemoveWis(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(4);
		else
			IPlayer.RemoveOTP(4);

		IPlayer.DecreaseMaxHp(50);
		IPlayer.DecreaseMaxMp(30);
	} else if (DSSType == 208)
	{
		IPlayer.RemoveMagAttack(check*4/100);
		IPlayer.RemovePhyAttack(check*4/100);
		IPlayer.RemoveInt(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(4);
		else
			IPlayer.RemoveOTP(4);

		IPlayer.DecreaseMaxHp(50);
		IPlayer.DecreaseMaxMp(30);
	} else if (DSSType == 207)
	{
		IPlayer.RemoveMagAttack(check*4/100);
		IPlayer.RemovePhyAttack(check*4/100);
		IPlayer.RemoveHp(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(4);
		else
			IPlayer.RemoveOTP(4);

		IPlayer.DecreaseMaxHp(50);
		IPlayer.DecreaseMaxMp(30);
	} else if (DSSType == 206)
	{
		IPlayer.RemoveMagAttack(check*4/100);
		IPlayer.RemovePhyAttack(check*4/100);
		IPlayer.RemoveStr(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(4);
		else
			IPlayer.RemoveOTP(4);

		IPlayer.DecreaseMaxHp(50);
		IPlayer.DecreaseMaxMp(30);
	} else if (DSSType == 205)
	{
		IPlayer.RemoveMagAttack(check*2/100);
		IPlayer.RemovePhyAttack(check*2/100);
		IPlayer.RemoveAgi(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(2);
		else
			IPlayer.RemoveOTP(2);

		IPlayer.DecreaseMaxHp(30);
		IPlayer.DecreaseMaxMp(10);
	} else if (DSSType == 204)
	{
		IPlayer.RemoveMagAttack(check*2/100);
		IPlayer.RemovePhyAttack(check*2/100);
		IPlayer.RemoveWis(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(2);
		else
			IPlayer.RemoveOTP(2);

		IPlayer.DecreaseMaxHp(30);
		IPlayer.DecreaseMaxMp(10);
	} else if (DSSType == 203)
	{
		IPlayer.RemoveMagAttack(check*2/100);
		IPlayer.RemovePhyAttack(check*2/100);
		IPlayer.RemoveInt(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(2);
		else
			IPlayer.RemoveOTP(2);

		IPlayer.DecreaseMaxHp(30);
		IPlayer.DecreaseMaxMp(10);
	} else if (DSSType == 202)
	{
		IPlayer.RemoveMagAttack(check*2/100);
		IPlayer.RemovePhyAttack(check*2/100);
		IPlayer.RemoveHp(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(2);
		else
			IPlayer.RemoveOTP(2);

		IPlayer.DecreaseMaxHp(30);
		IPlayer.DecreaseMaxMp(10);
	} else if (DSSType == 201)
	{
		IPlayer.RemoveMagAttack(check*2/100);
		IPlayer.RemovePhyAttack(check*2/100);
		IPlayer.RemoveStr(1);

		if (IItem.GetType() == -1 || (IItem.GetType() == -3 && IPlayer.GetClass() == 4))
			IPlayer.RemoveEva(2);
		else
			IPlayer.RemoveOTP(2);

		IPlayer.DecreaseMaxHp(30);
		IPlayer.DecreaseMaxMp(10);
	}
}

void __fastcall WeaponPutOn(int Item,void *edx,int Player)
{
	IChar IPlayer((void*)Player);
	IItem IItem((void*)Item);

	if (IPlayer.IsBuff(256))
	{
		IPlayer.SystemMessage("You need to wait 3 seconds to put on your weapon.", TEXTCOLOR_INFOMSG);
	} else {
		IPlayer.Buff(256,2,0);

		if (IPlayer.IsValid() && IPlayer.IsBuff(120))
		{
			if (IItem.LevelLimit() > 5)
				IItem.SetLevelLimit(IItem.LevelLimit()-5);
		}

		if (ConfigWeaponSkins.count(IItem.CheckIndex()))
		{
			if (WSuit.count(IPlayer.GetPID()) && WSuit.find(IPlayer.GetPID())->second.Index)
			{
				IPlayer.BoxMsg("You are already wearing weapon skin.");
				return;
			}

			if (MainSvrT::CPlayerIsWState(Player,0,0))
			{
				if (WSuit.count(IPlayer.GetPID()) && WSuit.find(IPlayer.GetPID())->second.Index) return;
				if (CItem::IsState(Item,1)) return;
				if (MainSvrT::CPlayerIsWState(Player,0,-116)) return;
				WSuit[IPlayer.GetPID()].IID = IItem.GetIID();
				WSuit[IPlayer.GetPID()].Index = IItem.CheckIndex();
				WSuit[IPlayer.GetPID()].Active = 1;
				MainSvrT::CPlayerAddWState(Player,0,-116);
				CItem::AddState(Item,1);
				(*(void (__thiscall **)(int, DWORD))(*(DWORD*)Item + 104))(Item,Player);
				CChar::WriteInSight(IPlayer.GetOffset(),5,"ddw",IPlayer.GetID(),IItem.GetIID(),IItem.CheckIndex());
				CDBSocket::Write(4,"d",IItem.GetIID());
				return;
			} else {
				IPlayer.BoxMsg("You can not put on weapon skin without a weapon.");
				return;
			}

			return;
		}

		CItemWeapon::PutOn(Item, Player);

		if (IPlayer.IsValid() && IPlayer.IsBuff(120))
		{
			if (IItem.LevelLimit() > 5)
				IItem.SetLevelLimit(IItem.LevelLimit()+5);
		}
	}
}