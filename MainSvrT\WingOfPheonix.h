namespace MainSvrT
{
	void __fastcall WingOfPheonix(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(92);
		if (ControlBlade.find(IPlayer.GetPID())->second < 5) return;
		if (!IPlayer.IsBuff(432)) return;

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (nSkillGrade && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < 100)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if(IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						int nDmg = (IPlayer.GetAttack() * STWOPMul) + (nSkillGrade * CTools::Rate(STWOPMin,STWOPMax));
						if (Target.GetType() == 0) nDmg = nDmg * STWOPReduce / 100;
						IPlayer.SetDirection(Target);
						IPlayer.DecreaseMana(100);
						ControlBlade.find(IPlayer.GetPID())->second -= 5;
						CChar::WriteInSight(IPlayer.GetOffset(),235,"dbw",IPlayer.GetID(),51,ControlBlade.find(IPlayer.GetPID())->second);
						IPlayer.OktayDamageSingle(Target,nDmg,92);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}