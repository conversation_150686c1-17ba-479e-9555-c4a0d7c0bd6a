namespace MainSvrT
{
	void R3volutioNWebServer(webserver::http_request *r)
	{
		Socket s = *(r->s_); std::string show;

		if (r->path_.substr(0,1) == "/")
		{
			if (r->path_.substr(1,6) == "online") show = Int2String(*(DWORD*)0x004E213C) + " player/s online.";
			if (r->path_.substr(1,3) == "F10") show = Int2String(F10List.size()) + " player/s registered for F10.";

			if (r->path_.substr(1,8) == "monster=")
			{
				std::string Index = r->path_.substr(9,r->path_.size());
				if (strlen(Index.c_str()))
				{
					int FindMonster = CMonster::ScanMonster(String2Int(Index));
					if (FindMonster)
					{
						show = "Alive";
						CIOObject::Release((void*)FindMonster);
					} else {
						show = "Dead";
					}
				}
			}

			if (r->path_.substr(1,7) == "ListF10")
			{
				for (int i = 0; i < F10List.size(); i++)
				{
					if (F10List.size() > 0) show += (std::string)F10List[i] + "<br>";
				}
			}
		}

		r->answer_ += show;
	}

	void WebServer(void *Pack)
	{

		time_t now = time(0);
		struct tm timeinfo;
		localtime_s(&timeinfo, &now); // For Windows

		char timestamp[80];
		strftime(timestamp, sizeof(timestamp), "%Y/%m/%d %H:%M:%S", &timeinfo);

		// Time Zone
		char tzname[64];
		_tzname[0] ? strcpy_s(tzname, _tzname[0]) : strcpy_s(tzname, "UTC");

		const char* configFile = "configs/Protection.txt";
		int port = 65000; // Default port
    
		if (PathFileExistsA(configFile)) 
		{
			port = GetPrivateProfileIntA("WebPort", "Port", 65000, configFile);
        
			// Validate port range
			if (port > 0 && port <= 65535) {
				CConsole::Black("Web Server is ready on port %d (time stamp: %s [%s])\n", port, timestamp, tzname);
				// Web Server is ready on port %d (time stamp: )
				// Web Server is ready on port %d (time stamp: %s [%s])\n
				// CConsole::Black("Reading [MainSvrT WebServer Port: %d]...\n", port);
			}
			else {
				CConsole::Red("Invalid port %d in config, using default 65000\n", port);
				port = 65000;
			}
		}
		else 
		{
			CConsole::Red("Protection.txt not found, using default port 65000\n");
		}
    
		webserver(port, MainSvrT::R3volutioNWebServer);
	}

	void __fastcall Start(int Start, void *edx, u_short hostshort)
	{
		//if (XObjectDB::Load("configs/voyagershop")) CConsole::Blue("Reading [VoyagerShop] . . .");
		CConsole::Blue("Reading [MainSvrT Plugin] . . .");
		//XObjectDB::Load("configs/ItemUse"); 
		CConsole::Red("IMPORTANT: Do not forget to delete Database.R3volutioN on server reset!");
		CIOServer::Start(Start,hostshort);
		_beginthread(MainSvrT::WebServer,0,0);
		MainSvrT::MyWarEnd();
		MainSvrT::MyLevelUp();
		MainSvrT::MyExpTable();
		MainSvrT::MyItemDrop();
		MainSvrT::MyMailSend();
		MainSvrT::MyTransform();
		MainSvrT::MyNormalHit();
		MainSvrT::MyGameStart();
		MainSvrT::MyStatFixes();
		MainSvrT::ReadConfigs();
		MainSvrT::MyItemFixes();
		MainSvrT::MyBaseStats();
		MainSvrT::MyCDBProcess();
		MainSvrT::MyPlayerTick();
		MainSvrT::MyFriendList();
		MainSvrT::MyCustomFixes();
		MainSvrT::MyMonsterBlob();
		MainSvrT::MyWriteSkills();
		MainSvrT::MySocketLogin();
		MainSvrT::MyChatCommand();
		MainSvrT::MyOnLoadPlayer();
		MainSvrT::MyExecuteSkill();
		MainSvrT::MyHideIsNormal();
		MainSvrT::MyPlayerProcess();
		MainSvrT::MyCNPCSendCreate();
		MainSvrT::MySetSkillPointer();
		MainSvrT::MyDamageShowingFix();
		MainSvrT::MyCPlayerSendState();
		MainSvrT::MyCDBSocketProcess();
		MainSvrT::MyCPlayerSendCreate();
		MainSvrT::MyCMonsterSendCreate();
		MainSvrT::MyExecuteTransformSkill();
		Summon(0,10,272273,348220,1152,1,0,0,0,0);
		Summon(0,10,272273,348278,1152,1,0,0,0,0);
		Summon(0,10,272273,348336,1152,1,0,0,0,0);
		Summon(0,10,272273,348394,1152,1,0,0,0,0);
		Summon(0,10,272273,348452,1152,1,0,0,0,0);
		Summon(0,10,272215,348220,1152,1,0,0,0,0);
		Summon(0,10,272157,348220,1152,1,0,0,0,0);
		Summon(0,10,272099,348220,1152,1,0,0,0,0);
		Summon(0,10,272041,348220,1152,1,0,0,0,0);
		Summon(0,10,272041,348278,1152,1,0,0,0,0);
		Summon(0,10,272041,348336,1152,1,0,0,0,0);
		Summon(0,10,272041,348394,1152,1,0,0,0,0);
		Summon(0,10,272041,348452,1152,1,0,0,0,0);
		Summon(0,10,272099,348452,1152,1,0,0,0,0);
		Summon(0,10,272157,348452,1152,1,0,0,0,0);
		Summon(0,10,272215,348452,1152,1,0,0,0,0);
		MainSvrT::CreateEmok();
		MainSvrT::CreateHonor();
		MainSvrT::CreateGuild();
		MainSvrT::Create2ndPWD();
		MainSvrT::CreateMSSLimit();
		MainSvrT::CreateCooldown();
		MainSvrT::DeleteCooldown();
		MainSvrT::CreateItemTable();
		MainSvrT::CreateWearState();
		MainSvrT::DeleteWearState();
		MainSvrT::CreateIgnoreList();
		MainSvrT::CreateHardwareID();
		MainSvrT::CreateDailyQuest();
		MainSvrT::CreateBuffRemain();
		MainSvrT::CreateMonsterPet();
		MainSvrT::CreateJoinRequest();
		MainSvrT::CreateBattleHorse();
		MainSvrT::DeletePProtection();
		MainSvrT::CreatePProtection();
		MainSvrT::CreatePlayerColor();
		MainSvrT::CreateGuildMember();
		MainSvrT::CreateGuildRewards();
		MainSvrT::CreateAuctionHouse();
		MainSvrT::CreateBenefitSystem();
		MainSvrT::CreateSwordTrickster();
		MainSvrT::CreateTrainingCenter();
		MainSvrT::CreateAcceptJoinRequest();

		// Initialize GUI Menu System
		DetourTransactionBegin();
		DetourAttach(&(PVOID&)_Window::Proc, MainSvrT::MainSvrTMenuProc);
		DetourTransactionCommit();
		MainSvrT::InitializeMainSvrTMenu();
	}
}