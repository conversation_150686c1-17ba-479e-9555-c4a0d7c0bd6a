namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		void *Target;
		int Count;
	} ThiefTBS;

	void __cdecl ContinueTwinBladeStrike(void *Pack)
	{
		Sleep(2000);
		MainSvrT::ThiefTBS *CTTBS = (MainSvrT::ThiefTBS*)Pack;
		IChar IPlayer(CTTBS->Player);

		if (IPlayer.IsValid())
		{
			void *pTarget = CTTBS->Target;
			IChar Target(pTarget);

			if (pTarget && CTTBS->Count && Target.IsValid() && IPlayer.IsValid())
			{
				for (int i = 0; i < CTTBS->Count; i++)
				{
					if (!IPlayer.IsValid()) break; if (!Target.IsValid()) break;
					if (CChar::IsGState((int)Target.GetOffset(),1)) break;
					if (CChar::GetRange((int)IPlayer.GetOffset() + 332, (int)pTarget + 332) > 300) break;

					if (IPlayer.CheckHit(Target, 10))
					{
						int nDmg = (IPlayer.GetAttack() * TTBSMul) + (CTools::Rate(TTBSMin,TTBSMax));
						if(Target.GetType() == 0) nDmg = nDmg * TTBSReduce / 100;
						IPlayer.OktayDamageArea(Target,nDmg,23);
					}

					Sleep(2000);
				}
			}
		}

		free(CTTBS);
	}

	void __fastcall TwinBladeStrike(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(23);

		if (IPlayer.IsValid() && pSkill)
		{
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < 65)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						if (IPlayer.CheckHit(Target, 10))
						{
							MainSvrT::ThiefTBS *TBSContinue;
							TBSContinue = (MainSvrT::ThiefTBS*)malloc(sizeof(MainSvrT::ThiefTBS));
							TBSContinue->Player = IPlayer.GetOffset();
							TBSContinue->Target = Target.GetOffset();
							TBSContinue->Count = 4;
							_beginthread(MainSvrT::ContinueTwinBladeStrike,0,(void*)TBSContinue);
							if (!Target.IsBuff(339)) Target.SendGStateEx(Target.GetGStateEx() + 262144);
							Target.Buff(339,8,0);
							Target.Buff(340,12,0);
							IPlayer.Buff(341,20,0);

							if (Target.GetType() == 0)
							{
								IPlayer.RemoveBuffIcon(0,0,0,219);
								Target.SetBuffIcon(10000,0,2021,219);
							}

							int nDmg = (IPlayer.GetAttack() * TTBSMul) + (CTools::Rate(TTBSMin,TTBSMax));
							if (Target.GetType() == 0) nDmg = nDmg * TTBSReduce / 100;
							IPlayer.OktayDamageSingle(Target,nDmg,23);
						} else {
							IPlayer._ShowBattleMiss(Target, 23);
						}

						IPlayer.SetDirection(Target);
						IPlayer.DecreaseMana(65);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}