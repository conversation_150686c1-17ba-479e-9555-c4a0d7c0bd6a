namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		void *Target;
		int Count;
	} EggWWF;

	void __cdecl ContinueWhirlwindFeather(void *Pack)
	{
		MainSvrT::EggWWF *CEWWF = (MainSvrT::EggWWF*)Pack;
		IChar IPlayer(CEWWF->Player);

		if (IPlayer.IsValid() && CChar::IsGState((int)IPlayer.GetOffset(),512))
		{
			void *pTarget = CEWWF->Target; IChar Target(pTarget);

			if (pTarget && CEWWF->Count)
			{
				for (int i = 0; i < CEWWF->Count; i++)
				{
					if (!IPlayer.IsValid() || !Target.IsValid()) break;
					if (!ContinueSkills.count(IPlayer.GetPID()) || ContinueSkills.find(IPlayer.GetPID())->second != 114) break;
					int Around = Target.GetObjectListAround(2);

					while(Around)
					{
						IChar Object((void*)*(DWORD*)Around);

						if (Object.IsValid() && Target.IsValid() && IPlayer.IsValid() && (*(int (__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0))
						{
							int nDmg = (IPlayer.GetAttack() * TEWhirMul);
							if (Object.GetType() == 0) nDmg = nDmg * TEWhirReduce / 100;
							IPlayer.OktayDamageArea(Object,nDmg,114);
						}

						Around = CBaseList::Pop((void*)Around);
					}

					Sleep(1500);
				}
			}
		}

		free(CEWWF);
	}

	void __fastcall WhirlwindFeather(IChar IPlayer)
	{
		if (IPlayer.IsValid() && IPlayer.GetRage() >= 15000)
		{
			IPlayer.DecreaseRage(15000);
			int GetSetXY[1]; GetSetXY[0] = IPlayer.GetX(); GetSetXY[1] = IPlayer.GetY();
			int check = CMonsterMagic::Create(568,IPlayer.GetMap(),(int)GetSetXY,1,(int)IPlayer.GetOffset(),0,8000);
			MainSvrT::EggWWF *EWWFContinue;
			EWWFContinue = (MainSvrT::EggWWF*)malloc(sizeof(MainSvrT::EggWWF));
			EWWFContinue->Player = IPlayer.GetOffset();
			EWWFContinue->Target = (void*)check;
			EWWFContinue->Count = 6;
			IPlayer._ShowBattleAnimation(IPlayer, 114);
			_beginthread(MainSvrT::ContinueWhirlwindFeather,0,(void*)EWWFContinue);
		}
	}
}