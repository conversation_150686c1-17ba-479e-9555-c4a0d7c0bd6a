namespace MainSvrT
{
	void __cdecl MyStatStr(int Player, unsigned char Packet, char *Format, unsigned char Type, unsigned short Str, unsigned short Otp, unsigned short MinAtk, unsigned short MaxAtk)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwww", 0, IPlayer.GetStr(), CChar::GetHit(Player), CChar::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Player), CChar::GetMaxAttack(Player));
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwww", 43, (CChar::GetStr(Player) - IPlayer.GetStr()), CChar::GetHit(Player), CChar::Get<PERSON><PERSON><PERSON><PERSON><PERSON>(Player), CChar::GetMax<PERSON>ttack(Player));		
	}

	void __cdecl MyStatHth(int Player, unsigned char Packet, char *Format, unsigned char Type, unsigned short Str, unsigned short Otp, unsigned short MinAtk, unsigned short MaxAtk)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwddw", 1, IPlayer.GetHth(), IPlayer.GetCurHp(), CChar::GetMaxHP(Player), CChar::GetResist(IPlayer.GetOffset(), 4));
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwddw", 44, (CChar::GetHth(Player) - IPlayer.GetHth()), IPlayer.GetCurHp(), CChar::GetMaxHP(Player), CChar::GetResist(IPlayer.GetOffset(), 4));
	}

	void __cdecl MyStatInt(int Player, unsigned char Packet, char* Format, unsigned char statType, unsigned short points, unsigned short curWisdom, unsigned short maxWisdom, unsigned short curse)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwwwww", 2, IPlayer.GetInt(), CChar::GetMinMagic(Player), CChar::GetMaxMagic(Player), CChar::GetResist(IPlayer.GetOffset(), 0), CChar::GetResist(IPlayer.GetOffset(), 1), CChar::GetResist(IPlayer.GetOffset(), 2));
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwwwww", 45, (CChar::GetInt(Player) - IPlayer.GetInt()), CChar::GetMinMagic(Player), CChar::GetMaxMagic(Player), CChar::GetResist(IPlayer.GetOffset(), 0), CChar::GetResist(IPlayer.GetOffset(), 1), CChar::GetResist(IPlayer.GetOffset(), 2));
	}

	void __cdecl MyStatWis(int Player, unsigned char Packet, char* Format, unsigned char statType, unsigned short points, unsigned short curWisdom, unsigned short maxWisdom, unsigned short curse)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwwwww", 3, IPlayer.GetWis(), IPlayer.GetCurMp(), CChar::GetMaxMp(Player), CChar::GetMinMagic(Player), CChar::GetMaxMagic(Player), CChar::GetResist(IPlayer.GetOffset(), 3));
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwwwww", 46, (CChar::GetWis(Player) - IPlayer.GetWis()), IPlayer.GetCurMp(), CChar::GetMaxMp(Player), CChar::GetMinMagic(Player), CChar::GetMaxMagic(Player), CChar::GetResist(IPlayer.GetOffset(), 3));
	}

	void __cdecl MyStatAgi(int Player, unsigned char Type, char* format, unsigned char statType, unsigned short points, unsigned short otp, unsigned short evasion, unsigned short minAttack, unsigned short maxAttack)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwwwww", 4, IPlayer.GetAgi(), CChar::GetHit(Player), CChar::GetDodge(Player), CChar::GetDodge(Player), CChar::GetMinAttack(Player), CChar::GetMaxAttack(Player));
		CPlayer::Write(IPlayer.GetOffset(), 69, "bwwwwww", 47, (CChar::GetDex(Player) - IPlayer.GetAgi()), CChar::GetHit(Player), CChar::GetDodge(Player), CChar::GetDodge(Player), CChar::GetMinAttack(Player), CChar::GetMaxAttack(Player));		
	}

	void __cdecl MyStatHpCur(int Player, unsigned char Type, char* format, unsigned char statType, unsigned short hp)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bd", 7, IPlayer.GetCurHp());
	}

	void __cdecl MyStatCurHp(int Player, unsigned char Type, char* format, unsigned char statType, unsigned short hp, unsigned short maxhp)
	{
		IChar IPlayer((void*)Player);
		CPlayer::Write(IPlayer.GetOffset(), 69, "bdd", 5, IPlayer.GetCurHp(), CChar::GetMaxHP(Player));
	}

	void MyStatFixes()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x00458965,MainSvrT::MyStatStr);
		Memory->Hook(0x00459A8E,MainSvrT::MyStatStr);
		Memory->Hook(0x0045B361,MainSvrT::MyStatStr);
		Memory->Hook(0x00458AA7,MainSvrT::MyStatHth);
		Memory->Hook(0x00459CA8,MainSvrT::MyStatHth);
		Memory->Hook(0x0045B587,MainSvrT::MyStatHth);
		Memory->Hook(0x00458C81,MainSvrT::MyStatInt);
		Memory->Hook(0x00459FDF,MainSvrT::MyStatInt);
		Memory->Hook(0x0045B8CA,MainSvrT::MyStatInt);
		Memory->Hook(0x00458E05,MainSvrT::MyStatWis);
		Memory->Hook(0x0045A301,MainSvrT::MyStatWis);
		Memory->Hook(0x0045BBf8,MainSvrT::MyStatWis);
		Memory->Hook(0x00458F6E,MainSvrT::MyStatAgi);
		Memory->Hook(0x0045A56F,MainSvrT::MyStatAgi);
		Memory->Hook(0x0045BE72,MainSvrT::MyStatAgi);
		Memory->Hook(0x0045903B,MainSvrT::MyStatHpCur);
		Memory->Hook(0x00459556,MainSvrT::MyStatCurHp);
		Memory->Hook(0x0045A61C,MainSvrT::MyStatCurHp);
		Memory->Hook(0x0045BF2B,MainSvrT::MyStatCurHp);
	}
}