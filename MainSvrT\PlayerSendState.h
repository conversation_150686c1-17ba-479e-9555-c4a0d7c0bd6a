namespace MainSvrT
{
	void __cdecl CPlayerSendMState(void *player, unsigned char Type, char *format, unsigned long mState)
	{
		CPlayer::Write(player,Type,"I",(unsigned __int64)mState);
	}

	void __cdecl CPlayerSendGStateInSight(void *player, unsigned char Type, char *format, long id, unsigned long gState)
	{
		CChar::WriteInSight(player,Type,"dI",id,(unsigned __int64)gState);
	}

	void __cdecl CPlayerSendGState(void *player, unsigned char Type, char *format, long id, unsigned long gState)
	{
		CPlayer::Write(player,Type,"dI",id,(unsigned __int64)gState);
	}

	void MyCPlayerSendState()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x00451DC8,MainSvrT::CPlayerSendMState);
		Memory->Hook(0x00451E98,MainSvrT::CPlayerSendMState);
		Memory->Hook(0x0045Cf4E,MainSvrT::CPlayerSendGState);
		Memory->Hook(0x0046064D,MainSvrT::CPlayerSendGState);
		Memory->Hook(0x00460694,MainSvrT::CPlayerSendGState);
		Memory->Hook(0x00460800,MainSvrT::CPlayerSendGState);
		Memory->Hook(0x00460A36,MainSvrT::CPlayerSendGState);
		Memory->Hook(0x0044190F,MainSvrT::CPlayerSendGStateInSight);
		Memory->Hook(0x0044924F,MainSvrT::CPlayerSendGStateInSight);
	}
}