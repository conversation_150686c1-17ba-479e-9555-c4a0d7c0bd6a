#include <Windows.h>
#include "XFile.h"
#include "Utility.h"
#pragma warning (disable : 4996)

XMapView::XMapView()
{
	m_hFile = INVALID_HANDLE_VALUE;
	m_hMapping = 0;
	m_pMapView = 0;
}

XMapView::~XMapView()
{
	Close();
}

BOOL XMapView::Create(const char *szPathName, DWORD dwDesiredAccess)
{
	m_hFile = CreateFile(szPathName, dwDesiredAccess == FILE_MAP_READ ? GENERIC_READ : GENERIC_READ | GENERIC_WRITE, 0, NULL, OPEN_EXISTING, 0, 0);
	if (m_hFile == INVALID_HANDLE_VALUE)
	{
		return FALSE;
	}
	m_hMapping = CreateFileMapping(m_hFile, NULL, dwDesiredAccess == FILE_MAP_READ ? PAGE_READONLY : dwDesiredAccess == FILE_MAP_WRITE ? PAGE_READWRITE : PAGE_WRITECOPY, 0, 0, 0);
	if (m_hMapping == 0)
	{
		CloseHandle(m_hFile);
		m_hFile = INVALID_HANDLE_VALUE;
		return FALSE;
	}
	m_pMapView = (char *) MapViewOfFile(m_hMapping, dwDesiredAccess, 0, 0, 0);
	if (m_pMapView == 0)
	{
		CloseHandle(m_hMapping);
		CloseHandle(m_hFile);
		m_hMapping = 0;
		m_hFile = INVALID_HANDLE_VALUE;
		return FALSE;
	}
	return TRUE;
}

void XMapView::Close()
{
	if (m_pMapView)	UnmapViewOfFile(m_pMapView);
	if (m_hMapping) CloseHandle(m_hMapping);
	if (m_hFile != INVALID_HANDLE_VALUE) CloseHandle(m_hFile);
	m_pMapView = 0;
	m_hMapping = 0;
	m_hFile = INVALID_HANDLE_VALUE;
}

XMapViewEx::XMapViewEx()
{
	m_pView = 0;
	m_pViewEnd = 0;
}

XMapViewEx::XMapViewEx(LPCTSTR lpszFilename)
{
	m_pView = 0;
	m_pViewEnd = 0;
	Open( lpszFilename);
}

XMapViewEx::~XMapViewEx()
{
	Close();
}

BOOL XMapViewEx::Open(LPCTSTR lpszFilename)
{
	m_hFile = ::CreateFile(lpszFilename, GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, 0);
	if (m_hFile == INVALID_HANDLE_VALUE) return FALSE;
	m_hMapping = CreateFileMapping(m_hFile, NULL, PAGE_READONLY, 0, 0, 0);
	if (m_hMapping == 0)
	{
		CloseHandle(m_hFile);
		m_hFile = INVALID_HANDLE_VALUE;
		return FALSE;
	}
	m_pMapView = (char *) MapViewOfFile(m_hMapping, FILE_MAP_READ, 0, 0, 0);
	if (m_pMapView == 0)
	{
		CloseHandle(m_hMapping);
		CloseHandle(m_hFile);
		m_hMapping = 0;
		m_hFile = INVALID_HANDLE_VALUE;
		return FALSE;
	}
	m_pView = m_pMapView;
	m_pViewEnd = m_pMapView + GetFileSize(m_hFile, NULL);
	return TRUE;
}

UINT XMapViewEx::Read(void *lpBuf, UINT uiCount)
{
	if (uiCount > (UINT)(m_pViewEnd - m_pView)) uiCount = (UINT)(m_pViewEnd - m_pView);
	memcpy(lpBuf, m_pView, uiCount);
	m_pView += uiCount;
	return uiCount;
}

void XMapViewEx::Close()
{
	XMapView::Close();
	m_pView = 0;
	m_pViewEnd = 0;
}

long XMapViewEx::Seek(long lOffset, UINT nFrom)
{
	if (nFrom == begin) 
		m_pView = m_pMapView + lOffset;
	else if (nFrom == current)
		m_pView += lOffset;
	else if (nFrom == end)
		m_pView = m_pViewEnd + lOffset;
	return (long) (m_pView - m_pMapView);
}

XFile::XFile()
{
	m_pViewBegin = 0;
	m_pView = 0;
	m_pViewEnd = 0;
}

XFile::XFile(void *pFile, DWORD size)
{
	m_pView = 0;
	m_pViewEnd = 0;
	Open(pFile, size);
}

XFile::~XFile()
{
}

void XFile::Open(void *pVoid, DWORD size)
{
	m_pViewBegin = (char *) pVoid;
	m_pView = m_pViewBegin;
	m_pViewEnd = m_pViewBegin + size;
}

UINT XFile::Read(void *lpBuf, UINT uiCount)
{
	if (uiCount > (UINT)(m_pViewEnd - m_pView)) uiCount = (UINT)(m_pViewEnd - m_pView);
	memcpy(lpBuf, m_pView, uiCount);
	m_pView += uiCount;
	return uiCount;
}

void XFile::Close()
{
	m_pView = 0;
	m_pViewEnd = 0;
}

long XFile::Seek(long lOffset, UINT nFrom)
{
	if (nFrom == begin) 
		m_pView = m_pViewBegin + lOffset;
	else if (nFrom == current)
		m_pView += lOffset;
	else if (nFrom == end)
		m_pView = m_pViewEnd + lOffset;
	return (long)(m_pView - m_pViewBegin);
}

XFileEx::XFileEx()
{
	m_hFile = INVALID_HANDLE_VALUE;
	m_hMapping = 0;
	m_nSize = 0;
	m_pMemory = 0;
}

XFileEx::~XFileEx()
{
	Close();
}

BOOL XFileEx::Open(HANDLE hFile)
{
	if (PreOpen(hFile, MEMORY_SIZE))
	{
		m_pMemory = CVirtualMemoryPool<MEMORY_SIZE>::Alloc();
		if (!m_pMemory)
		{
			Close();
			return FALSE;
		}
		if (!PostOpen(m_pMemory))
		{
			Close();
			return FALSE;
		}
		return TRUE;
	}
	return (m_hFile != INVALID_HANDLE_VALUE);
}

BOOL XFileEx::Open(LPCSTR szPath)
{
	if (PreOpen(szPath, MEMORY_SIZE))
	{
		m_pMemory = CVirtualMemoryPool<MEMORY_SIZE>::Alloc();
		if (!m_pMemory)
		{
			Close();
			return FALSE;
		}
		if (!PostOpen(m_pMemory))
		{
			Close();
			return FALSE;
		}
		return TRUE;
	}
	return (m_hFile != INVALID_HANDLE_VALUE);
}

int	XFileEx::PreOpen(HANDLE hFile, DWORD nSize)
{
	m_hMapping = 0;
	m_hFile = hFile;
	m_nSize = GetFileSize(m_hFile, 0);
	if (m_nSize <= nSize) return m_nSize;
	m_hMapping = CreateFileMapping(m_hFile, NULL, PAGE_READONLY, 0, 0, 0);
	if (m_hMapping == 0)
	{
		CloseHandle(m_hFile);
		m_hFile = INVALID_HANDLE_VALUE;
		return 0;
	}
	LPVOID pMapView = MapViewOfFile(m_hMapping, FILE_MAP_READ, 0, 0, 0);
	if (pMapView == 0)
	{
		CloseHandle(m_hMapping);
		CloseHandle(m_hFile);
		m_hMapping = 0;
		m_hFile = INVALID_HANDLE_VALUE;
		return 0;
	}
	XFile::Open(pMapView, m_nSize);
	return 0;
}

int	XFileEx::PreOpen(LPCSTR szPath, DWORD nSize)
{
	HANDLE hFile = ::CreateFile(szPath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, 0);
	if (hFile == INVALID_HANDLE_VALUE)
	{
		return 0;
	}
	return PreOpen(hFile, nSize);
}

BOOL XFileEx::PostOpen(LPVOID pVoid)
{
	if (m_hFile == INVALID_HANDLE_VALUE)
	{
		return FALSE;
	}
	if (m_hMapping) return TRUE;
	DWORD dwFileSize;
	ReadFile(m_hFile, pVoid, m_nSize, &dwFileSize, NULL);
    XFile::Open(pVoid, m_nSize);
	return TRUE;
}

void XFileEx::Close()
{
	if (m_hMapping)
	{
		UnmapViewOfFile(m_pViewBegin);
		CloseHandle(m_hMapping);
		CloseHandle(m_hFile);
		m_hMapping = 0;
		m_hFile = INVALID_HANDLE_VALUE;
	} else if (m_hFile != INVALID_HANDLE_VALUE) {
		CloseHandle(m_hFile);
		m_hFile = INVALID_HANDLE_VALUE;
	}
	if (m_pMemory)
	{
		CVirtualMemoryPool<MEMORY_SIZE>::Free(m_pMemory);
		m_pMemory = 0;
	}
}

DWORD XFileEx::Touch()
{
	DWORD checksum = 0;
	if (m_hMapping)
	{
		SYSTEM_INFO sys_info;
		GetSystemInfo(&sys_info);
		for (char *ptr = m_pViewBegin; ptr < m_pViewEnd; ptr += sys_info.dwPageSize)
		{
			checksum += *(DWORD *) ptr;
		}
	}
	return checksum;
}