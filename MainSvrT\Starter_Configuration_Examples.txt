# ========================================
# STARTER.TXT CONFIGURATION EXAMPLES
# ========================================
# This file contains comprehensive examples for configuring the Starter system
# Copy and modify these examples for your server's needs

# ========================================
# BASIC SYNTAX REFERENCE
# ========================================

# Starter Class Configuration:
# (starter (Class X)(Map X)(X X)(Y X)(EXP X)(HTML X)(WelcomeMsg 'message'))

# Starter Item Configuration:
# (starterItem (class X)(index X)(amount X)(info X)(prefix X)(defense X)(evasion X)(attack X)(magic X)(toa X)(upgrade X)(mix X)(bof X)(dss X))

# ========================================
# CHARACTER CLASSES
# ========================================
# Class 0 = Knight
# Class 1 = Mage  
# Class 2 = Archer
# Class 3 = Thief
# Class 4 = Nirvana
# Class 5 = SwordTrickster
# Class 6 = Shaman

# ========================================
# EXAMPLE 1: BASIC KNIGHT STARTER
# ========================================

# Knight starter configuration - basic setup
(starter (Class 0)(Map 1)(X 257514)(Y 259273)(EXP 1000000)(HTML 0)(WelcomeMsg 'Welcome brave Knight $name! Your adventure begins now!'))

# Basic knight equipment
(starterItem (class 0)(index 1)(amount 1)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))     # Basic Sword
(starterItem (class 0)(index 2)(amount 1)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))     # Basic Shield
(starterItem (class 0)(index 3)(amount 1)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))     # Basic Armor
(starterItem (class 0)(index 4)(amount 1)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))     # Basic Helmet
(starterItem (class 0)(index 5)(amount 1)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))     # Basic Gloves
(starterItem (class 0)(index 48)(amount 100)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))   # Health Potions

# ========================================
# EXAMPLE 2: ENHANCED MAGE STARTER
# ========================================

# Mage starter with enhanced items and HTML window
(starter (Class 1)(Map 1)(X 257514)(Y 259273)(EXP 5000000)(HTML 100)(WelcomeMsg 'Welcome wise Mage $name! The arcane arts await your mastery!'))

# Enhanced mage equipment with upgrades
(starterItem (class 1)(index 100)(amount 1)(info 2097152)(prefix 1)(defense 0)(evasion 0)(attack 0)(magic 50)(toa 0)(upgrade 3)(mix 0)(bof 1)(dss 0))    # +3 BOF Magic Staff
(starterItem (class 1)(index 101)(amount 1)(info 0)(prefix 1)(defense 30)(evasion 20)(attack 0)(magic 0)(toa 0)(upgrade 2)(mix 0)(bof 0)(dss 0))         # +2 Magic Robe
(starterItem (class 1)(index 102)(amount 1)(info 0)(prefix 0)(defense 15)(evasion 10)(attack 0)(magic 0)(toa 0)(upgrade 1)(mix 0)(bof 0)(dss 0))         # +1 Magic Hat
(starterItem (class 1)(index 47)(amount 200)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Mana Potions
(starterItem (class 1)(index 48)(amount 100)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Health Potions

# ========================================
# EXAMPLE 3: PREMIUM ARCHER STARTER
# ========================================

# Archer with premium equipment and special location
(starter (Class 2)(Map 2)(X 300000)(Y 300000)(EXP 10000000)(HTML 200)(WelcomeMsg 'Welcome skilled Archer $name! Your arrows will never miss their mark!'))

# Premium archer equipment with multiple enhancements
(starterItem (class 2)(index 200)(amount 1)(info 2097152)(prefix 2)(defense 0)(evasion 0)(attack 100)(magic 0)(toa 25)(upgrade 5)(mix 1)(bof 1)(dss 1))  # Ultimate Bow
(starterItem (class 2)(index 201)(amount 1000)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))        # Arrows
(starterItem (class 2)(index 202)(amount 1)(info 0)(prefix 2)(defense 50)(evasion 40)(attack 0)(magic 0)(toa 0)(upgrade 4)(mix 1)(bof 0)(dss 1))        # Leather Armor
(starterItem (class 2)(index 203)(amount 1)(info 0)(prefix 1)(defense 20)(evasion 30)(attack 0)(magic 0)(toa 0)(upgrade 3)(mix 0)(bof 0)(dss 0))        # Archer Boots
(starterItem (class 2)(index 500)(amount 10)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))         # Special Scrolls

# ========================================
# EXAMPLE 4: THIEF STEALTH STARTER
# ========================================

# Thief with stealth-focused equipment
(starter (Class 3)(Map 1)(X 250000)(Y 250000)(EXP 2000000)(HTML 0)(WelcomeMsg 'Welcome shadow Thief $name! Strike from the darkness and vanish like smoke!'))

# Thief equipment focused on evasion and critical hits
(starterItem (class 3)(index 300)(amount 1)(info 0)(prefix 1)(defense 0)(evasion 0)(attack 80)(magic 0)(toa 30)(upgrade 2)(mix 0)(bof 0)(dss 0))         # Quick Dagger
(starterItem (class 3)(index 301)(amount 1)(info 0)(prefix 1)(defense 0)(evasion 0)(attack 75)(magic 0)(toa 28)(upgrade 2)(mix 0)(bof 0)(dss 0))         # Off-hand Dagger
(starterItem (class 3)(index 302)(amount 1)(info 0)(prefix 0)(defense 25)(evasion 50)(attack 0)(magic 0)(toa 0)(upgrade 3)(mix 0)(bof 0)(dss 0))         # Shadow Cloak
(starterItem (class 3)(index 303)(amount 1)(info 0)(prefix 0)(defense 15)(evasion 35)(attack 0)(magic 0)(toa 0)(upgrade 2)(mix 0)(bof 0)(dss 0))         # Stealth Boots
(starterItem (class 3)(index 600)(amount 50)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Poison Vials

# ========================================
# EXAMPLE 5: NIRVANA BALANCED STARTER
# ========================================

# Nirvana with balanced hybrid equipment
(starter (Class 4)(Map 1)(X 257514)(Y 259273)(EXP 3000000)(HTML 150)(WelcomeMsg 'Welcome enlightened Nirvana $name! Balance is the key to true power!'))

# Balanced equipment for hybrid playstyle
(starterItem (class 4)(index 400)(amount 1)(info 0)(prefix 1)(defense 0)(evasion 0)(attack 60)(magic 60)(toa 20)(upgrade 3)(mix 0)(bof 0)(dss 0))        # Balanced Weapon
(starterItem (class 4)(index 401)(amount 1)(info 0)(prefix 1)(defense 40)(evasion 25)(attack 0)(magic 0)(toa 0)(upgrade 2)(mix 0)(bof 0)(dss 0))         # Harmony Armor
(starterItem (class 4)(index 402)(amount 1)(info 0)(prefix 0)(defense 20)(evasion 15)(attack 0)(magic 0)(toa 0)(upgrade 1)(mix 0)(bof 0)(dss 0))         # Balance Helmet
(starterItem (class 4)(index 48)(amount 150)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Health Potions
(starterItem (class 4)(index 47)(amount 150)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Mana Potions

# ========================================
# EXAMPLE 6: SWORDTRICKSTER MASTER STARTER
# ========================================

# SwordTrickster with master-level equipment
(starter (Class 5)(Map 3)(X 400000)(Y 400000)(EXP 15000000)(HTML 300)(WelcomeMsg 'Welcome master SwordTrickster $name! Your blade dances with the power of legends!'))

# Master-level equipment with all enhancements
(starterItem (class 5)(index 500)(amount 1)(info 2097152)(prefix 3)(defense 0)(evasion 0)(attack 150)(magic 50)(toa 35)(upgrade 7)(mix 1)(bof 1)(dss 1)) # Legendary Sword
(starterItem (class 5)(index 501)(amount 1)(info 0)(prefix 2)(defense 60)(evasion 30)(attack 0)(magic 0)(toa 0)(upgrade 5)(mix 1)(bof 0)(dss 1))         # Master Armor
(starterItem (class 5)(index 502)(amount 1)(info 0)(prefix 2)(defense 30)(evasion 20)(attack 0)(magic 0)(toa 0)(upgrade 4)(mix 0)(bof 1)(dss 0))         # Master Helmet
(starterItem (class 5)(index 503)(amount 1)(info 0)(prefix 1)(defense 25)(evasion 25)(attack 0)(magic 0)(toa 0)(upgrade 3)(mix 0)(bof 0)(dss 0))         # Master Gloves
(starterItem (class 5)(index 700)(amount 20)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Skill Books

# ========================================
# EXAMPLE 7: SHAMAN SPIRIT STARTER
# ========================================

# Shaman with spirit-based equipment
(starter (Class 6)(Map 1)(X 257514)(Y 259273)(EXP 4000000)(HTML 250)(WelcomeMsg 'Welcome mystical Shaman $name! The spirits guide your path to greatness!'))

# Spirit-focused equipment
(starterItem (class 6)(index 600)(amount 1)(info 0)(prefix 1)(defense 0)(evasion 0)(attack 40)(magic 80)(toa 15)(upgrade 3)(mix 0)(bof 0)(dss 0))        # Spirit Staff
(starterItem (class 6)(index 601)(amount 1)(info 0)(prefix 1)(defense 35)(evasion 20)(attack 0)(magic 0)(toa 0)(upgrade 2)(mix 0)(bof 0)(dss 0))         # Ritual Robe
(starterItem (class 6)(index 602)(amount 1)(info 0)(prefix 0)(defense 18)(evasion 12)(attack 0)(magic 0)(toa 0)(upgrade 1)(mix 0)(bof 0)(dss 0))         # Shaman Mask
(starterItem (class 6)(index 800)(amount 30)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Spirit Totems
(starterItem (class 6)(index 47)(amount 200)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))          # Mana Potions

# ========================================
# EXAMPLE 8: MINIMAL TESTING CONFIGURATION
# ========================================

# Simple configuration for testing
(starter (Class 0)(Map 1)(X 257514)(Y 259273)(EXP 1000000)(HTML 0)(WelcomeMsg 'Test message for $name'))
(starterItem (class 0)(index 48)(amount 1)(info 0)(prefix 0)(defense 0)(evasion 0)(attack 0)(magic 0)(toa 0)(upgrade 0)(mix 0)(bof 0)(dss 0))

# ========================================
# EXAMPLE 9: ADVANCED CUSTOM ITEMS
# ========================================

# Examples of advanced item configurations

# Super Enhanced Weapon (all enhancements)
(starterItem (class 0)(index 1000)(amount 1)(info 2097152)(prefix 3)(defense 0)(evasion 0)(attack 200)(magic 100)(toa 40)(upgrade 10)(mix 1)(bof 1)(dss 1))

# Perfect Defense Armor
(starterItem (class 0)(index 1001)(amount 1)(info 0)(prefix 2)(defense 100)(evasion 80)(attack 0)(magic 0)(toa 0)(upgrade 8)(mix 1)(bof 0)(dss 1))

# Balanced Hybrid Item
(starterItem (class 4)(index 1002)(amount 1)(info 0)(prefix 1)(defense 50)(evasion 40)(attack 75)(magic 75)(toa 25)(upgrade 5)(mix 0)(bof 1)(dss 0))

# ========================================
# PARAMETER EXPLANATIONS
# ========================================

# STARTER PARAMETERS:
# Class: Character class (0-6)
# Map: Map index to teleport to
# X, Y: Coordinates for teleportation
# EXP: Experience points to grant
# HTML: HTML window ID (0 = none)
# WelcomeMsg: Message with $name placeholder

# ITEM PARAMETERS:
# class: Character class (0-6)
# index: Item index from database
# amount: Quantity of items
# info: Item info flags (2097152 for special items)
# prefix: Item prefix level (0-3)
# defense: Custom defense value
# evasion: Custom evasion value
# attack: Custom attack value
# magic: Custom magic value
# toa: Time of Attack value
# upgrade: Upgrade level (0-15)
# mix: Mix enhancement (0-1)
# bof: Bead of Fire enhancement (0-1)
# dss: DSS enhancement (0-1)

# ========================================
# TIPS FOR CONFIGURATION
# ========================================

# 1. Start with basic items and test
# 2. Verify item indices exist in your database
# 3. Test coordinates are valid and accessible
# 4. Use appropriate experience amounts for your server
# 5. Keep welcome messages under 100 characters
# 6. Don't give too many items (inventory space limit)
# 7. Test each class configuration separately
# 8. Use HTML windows for important information
# 9. Balance starter equipment with server progression
# 10. Monitor console output for errors
