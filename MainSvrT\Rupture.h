namespace MainSvrT
{
	typedef struct 
	{
		void *Player;
		void *Target;
		int SkillGrade;
		int Count;
	} ThiefRupture;

	void __cdecl ContinueRupture(void *Pack)
	{
		Sleep(1000);
		MainSvrT::ThiefRupture *CTRupture = (MainSvrT::ThiefRupture*)Pack;
		IChar IPlayer(CTRupture->Player);

		if (IPlayer.IsValid())
		{
			int nSkillGrade = CTRupture->SkillGrade;
			void *pTarget = CTRupture->Target;
			IChar Target(pTarget);

			if (nSkillGrade && pTarget && CTRupture->Count)
			{
				for (int i = 0; i < CTRupture->Count; i++)
				{
					if (!Target.IsValid() || !IPlayer.IsValid()) break;
					if (CChar::IsGState((int)Target.GetOffset(),1)) break;
					if (CChar::GetRange((int)IPlayer.GetOffset() + 332, (int)pTarget + 332) > 300) break;

					if (IPlayer.IsValid() && Target.IsValid())
					{
						int nDmg = (IPlayer.GetAttack() * TRupMul) + (nSkillGrade * CTools::Rate(TRupMin,TRupMax));
						if (Target.GetType() == 0) nDmg = nDmg * TRupReduce / 100;
						IPlayer.OktayDamageArea(Target,nDmg,17);
					}

					Sleep(2000);
				}
			}
		}

		free(CTRupture);
	}

	void __fastcall Rupture(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(17);

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			int nMana = (int)(30 * nSkillGrade + 35);
			if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (IPlayer.IsValid() && nSkillGrade)
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < nMana)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						if (IPlayer.CheckHit(Target, 10))
						{
							IPlayer.Buff(353,20,0);
							if (!Target.IsBuff(350)) Target.SendGStateEx(Target.GetGStateEx() + 65536);
							Target.Buff(350, 18, 0);
							MainSvrT::ThiefRupture *RuptureContinue;
							RuptureContinue = (MainSvrT::ThiefRupture*)malloc(sizeof(MainSvrT::ThiefRupture));
							RuptureContinue->Player = IPlayer.GetOffset();
							RuptureContinue->Target = Target.GetOffset();
							RuptureContinue->SkillGrade = nSkillGrade;
							RuptureContinue->Count = 9;
							_beginthread(MainSvrT::ContinueRupture,0,(void*)RuptureContinue);
							IPlayer._ShowBattleAnimation(IPlayer, 17, nSkillGrade);
						} else {
							IPlayer._ShowBattleMiss(Target, 17);
						}

						IPlayer.SetDirection(Target);
						IPlayer.DecreaseMana(nMana);
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}