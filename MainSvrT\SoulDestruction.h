namespace MainSvrT
{
	void __fastcall SoulDestruction(void *pSkill, void *pPlayer, int pPacket, int pPos)
	{
		ISkill ISkill(pSkill);
		int nSkillGrade = ISkill.GetGrade();
		if (!nSkillGrade) return;
		IChar IPlayer(pPlayer);
		int nTargetID = 0; char bType = 0; void *pTarget = 0;
		CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
		int nMana = ISkill.DecreaseMana();
		if (bType == 0 && nTargetID) pTarget = CPlayer::FindPlayer(nTargetID);
		if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);
		IChar ITarget(pTarget);

		if (pTarget && ITarget.IsValid() && IPlayer.IsValid() && nTargetID != IPlayer.GetID())
		{
			if (IPlayer.GetCurMp() < nMana) return;
			if (!IPlayer.IsInRange(ITarget,300)) return;

			if (SoulDestructionAOE == 1 && ITarget.GetType() == 1)
			{
				int Around = ITarget.GetObjectListAround(SoulDestructionRange);
				
				while(Around)
				{
					IChar Object((void*)*(DWORD*)Around);

					if (Object.IsValid() && IPlayer.IsValid() && Object.GetType() == 1 && (*(int (__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0))
					{
						int GetWisdom = CChar::GetWis((int)IPlayer.GetOffset());
						int nDmg = ((GetWisdom * ISkill.GetGrade() + CTools::Rate(MSDMin,MSDMax)) * IPlayer.GetMagic() / 500) * MSDMul;
						if (ITarget.GetType() == 0) nDmg = (nDmg * MSDReduce) / 100;

						if (Object.GetType() == 1)
						{
							IPlayer.OktayDamageArea(Object,nDmg,40);
							IPlayer._ShowBattleAnimation(Object, 40);
						}
					}

					Around = CBaseList::Pop((void*)Around);
				}

				IPlayer.SetDirection(ITarget);
				IPlayer.DecreaseMana(nMana);
			} else {
				IPlayer.SetDirection(ITarget);
				(*(void (__thiscall **)(int, int))(*(DWORD*)ITarget.GetOffset() + 80))((int)ITarget.GetOffset(), (int)IPlayer.GetOffset());
				int GetWisdom = CChar::GetWis((int)IPlayer.GetOffset());
				int nDmg = ((GetWisdom * ISkill.GetGrade() + CTools::Rate(MSDMin,MSDMax)) * IPlayer.GetMagic() / 500) * MSDMul;
				if (ITarget.GetType() == 0) nDmg = (nDmg * MSDReduce) / 100;
				IPlayer.OktayDamageSingle(ITarget,nDmg,40);
				IPlayer.DecreaseMana(nMana);
			}

			if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
		}
	}
}