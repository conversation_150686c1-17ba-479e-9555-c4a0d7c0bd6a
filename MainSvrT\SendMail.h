namespace MainSvrT
{
	void __cdecl FixMailItem(void *Player, unsigned char Type, const char *Format, unsigned char mType, int SMID, unsigned char byType, int IID, unsigned __int16 wLen, char *Msg, unsigned __int16 xwLen, char *Packet, int size)
	{
		IChar IPlayer(Player);

		if (IPlayer.IsOnline())
		{
			Interface<ITools> Tools;
			unsigned short index=0; char xPacket[67] = {0};
			unsigned char prefix=0;
			unsigned long info=0,amount=0;
			unsigned char maxend=0,curend=0,setgem=0,xatk=0,xmagic=0,xdefense=0,xhit=0,xevasion=0;
			unsigned short xprotect=0;
			unsigned char upgrlvl=0,upgrrate=0;
			unsigned char x=0,y=0,z=0,dsstype=0,eva=0,otp=0,hpinc=0,mpinc=0,str=0,hp=0,intel=0,wis=0,agi=0,a=0,dg1stat=0,dg1type=0,dg2stat=0,dg2type=0,PerfShotCheck=0;
			unsigned long remaintime=0,QigongGrade=0;
			unsigned short phyatk=0,magatk=0,def=0,absorb=0;
			Packet = Tools->ParseData(Packet, "wbddbbbbbbbbbbb", &index, &prefix, &info, &amount, &maxend, &curend, &setgem, &xatk, &xmagic, &xdefense, &xhit, &xevasion, &xprotect, &upgrlvl, &upgrrate);
			ItemShow::GetItemStat(Player,IID,index,prefix,x,y,z,dsstype,eva,otp,hpinc,mpinc,str,hp,intel,wis,agi,a,dg1stat,dg1type,dg2stat,dg2type,PerfShotCheck,remaintime,QigongGrade,phyatk,magatk,def,absorb);	
			Tools->Compile(xPacket, "wdbddbbbbbbbbwbbbbbdbwwwwbbbbbbbbbbdbbwbbd",index, IID, prefix, info, amount, maxend, curend, setgem, xatk, xmagic, xdefense, xhit, xevasion, xprotect, upgrlvl, upgrrate, x, y, z, remaintime, dsstype, phyatk, magatk, def, absorb, eva, otp, hpinc, mpinc, str, hp, intel, wis, agi, PerfShotCheck, QigongGrade, dg1stat, dg1type, a, dg2stat, dg2type, 0);
			CPlayer::Write(Player,Type,Format,mType,SMID,byType,IID,wLen,Msg,xwLen,xPacket,63);
		}
	}

	int __fastcall SendMail(void* Player, void *edx, int a1, int a2, char* ReceiverName, const char *Text, int a5, int a6, int Index, int a8, int a9, int Price)
	{
		IChar IPlayer(Player);

		if (IPlayer.IsOnline())
		{
			if (MainSvrT::CountIgnoreList(ReceiverName,IPlayer.GetName()))
			{
				IPlayer.SystemMessage("Player ignored you!",TEXTCOLOR_INFOMSG);
				return 0;
			}

			if (Index == 31 && Price > 0)
			{
				IPlayer.SystemMessage("You can not send geons with price!", TEXTCOLOR_RED);
				return 0;
			}
		}

		return CPlayer::SendMail(Player, a1, a2, ReceiverName, Text, a5, a6, Index, a8, a9, Price);
	}

	void MyMailSend()
	{
		Interface<IMemory> Memory;
		Memory->Hook(0x00468988,MainSvrT::SendMail);
		Memory->Hook(0x00468A82,MainSvrT::SendMail);
		Memory->Hook(0x00469B03,MainSvrT::SendMail);
		Memory->Hook(0x0046933B,MainSvrT::FixMailItem);
	}
}