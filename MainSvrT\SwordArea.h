namespace MainSvrT
{
	void __fastcall SwordArea(IChar IPlayer)
	{
		int pSkill = IPlayer.GetSkillPointer(47);
		if (ControlBlade.find(IPlayer.GetPID())->second < 6) return;
		if (!IPlayer.IsBuff(433)) return;

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill); int nSkillGrade = xSkill.GetGrade();

			if (nSkillGrade && IPlayer.IsValid())
			{
				if (IPlayer.GetCurMp() < 100) return;

				if (IPlayer.IsValid())
				{
					IPlayer.DecreaseMana(100);
					ControlBlade.find(IPlayer.GetPID())->second -= 6;
					int Around = IPlayer.GetObjectListAround(3);

					while(Around)
					{
						IChar Object((void*)*(DWORD*)Around);

						if (Object.IsValid() && IPlayer.IsValid() && Object.GetType() == 1 && Object.IsMobAggressive())
						{
							CChar::WriteInSight(Object.GetOffset(),56,"db",Object.GetID(),7);
							int x = IPlayer.GetX() + CTools::Rate(-20,20);
							int y = IPlayer.GetY() + CTools::Rate(-20,20);
							if (Object.IsValid()) *(DWORD*)((int)Object.GetOffset() + 332) = x;
							if (Object.IsValid()) *(DWORD*)((int)Object.GetOffset() + 336) = y;
							CChar::WriteInSight(Object.GetOffset(),51,"wdddwddIIsbdsIIb",Object.GetMobIndex(),Object.GetID(),x,y,*(DWORD*)((int)Object.GetOffset() + 348),Object.GetCurHp(),CChar::GetMaxHp((int)Object.GetOffset()),*(DWORD*)((int)Object.GetOffset() + 280),(unsigned __int64)*(DWORD*)((int)Object.GetOffset() + 288),*(DWORD*)((int)Object.GetOffset() + 292),(*(int (__thiscall **)(int))(*(DWORD*)(int)Object.GetOffset() + 224))((int)Object.GetOffset()),*(DWORD*)(*(DWORD*)((int)Object.GetOffset() + 440) + 64) | 0x80,*(DWORD*)((int)Object.GetOffset() + 524),(*(int (__thiscall **)(int))(*(DWORD*)(int)Object.GetOffset() + 240))((int)Object.GetOffset()),(__int64)0, (__int64)0, 0);
							Object.AddFxToTarget("TI_SK_15",1,0,0);
						}

						Around = CBaseList::Pop((void*)Around);
					}
				}
			}
		}
	}
}