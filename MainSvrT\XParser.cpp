#include <Windows.h>
#include "Functions.h"
#include "XParser.h"
#include "XFile.h"
#pragma warning (disable : 4996)

XParser::XParser(void)
{
}

XParser::~XParser(void)
{
}

void XParser::Open(XFile *pFile)
{
	m_pFile = pFile;
	m_nLine = 1;
	m_nDepth = 0;
}

lisp::var XParser::Load(LPCSTR szPath)
{
	XFileEx	file;
	if (!file.Open(szPath)) return lisp::error;
	return Load(&file);
}

lisp::var XParser::Load(XFile *pFile)
{
	m_pFile = pFile;
	m_nLine = 1;
	return OnLoad();
}

lisp::var XParser::OnLoad()
{
	TOKEN token;
	lisp::var varList;
	lisp::var *pList = &varList;
	for ( ; ; )
	{
		switch (token = GetToken())
		{
		case T_END:
		case T_CLOSE:
			return varList;
		case T_OPEN:
			{
				lisp::var child = OnLoad();
				if (!child.listp())
				{
					varList.destroy();
					return child;
				}
				*pList = new lisp::_cons(child, lisp::nil);
				pList = &pList->cdr();
			}
			break;
		case T_STRING:
			{
				char *p = (char *) malloc(m_strSymbol.size()+1);
				strcpy(p, GetString());
				*pList = new lisp::_cons(new lisp::_string(p), lisp::nil);
				pList = &pList->cdr();
			}
			break;
		case T_FLOAT:
			{
				*pList = new lisp::_cons(new lisp::_float(GetFloat()), lisp::nil);
				pList = &pList->cdr();
			}
			break;
		case T_INTEGER:
			{
				*pList = new lisp::_cons(new lisp::_integer(GetInteger()), lisp::nil);
				pList = &pList->cdr();
			}
			break;
		default:
			varList.destroy();
			CConsole::Red("XParser::OnLoad(): Invalid format at line %d\n", GetLine());
			return lisp::error;
		}
	}
}

XParser::TOKEN XParser::GetToken()
{
	int ch;
	{
		st_start:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd) return T_END;
		switch (ch = *m_pFile->m_pView++)
		{
		case 0 :
			m_pFile->m_pView--;
			return T_END;
		case ';':
			for ( ; ; )
			{
				if (m_pFile->m_pView == m_pFile->m_pViewEnd)
					return T_END;
				switch(*m_pFile->m_pView++)
				{
				case 0:
				case '\n':
					m_pFile->m_pView--;
					goto st_start;
				}
			}
		case '\n':
			m_nLine++;
			goto st_start;
		case '(':
		#ifdef	GAMECONVERT
		case '{':
		#endif
			m_nDepth++;
			return T_OPEN;
		case ')':
		#ifdef	GAMECONVERT
		case '}':
		#endif
			m_nDepth--;
			return T_CLOSE;
		case '"':
			m_strSymbol.clear();
			for ( ; ; )
			{
				if (m_pFile->m_pView == m_pFile->m_pViewEnd)
					return T_ERROR;
				switch(ch = *m_pFile->m_pView++)
				{
				case 0:
					m_pFile->m_pView--;
					return T_ERROR;
				case '"':
					if (m_pFile->m_pView == m_pFile->m_pViewEnd)
						return T_STRING;
					if ((ch = *m_pFile->m_pView) != '"') 
						return T_STRING;
					else
						m_strSymbol += '"';
					break;
				case '\r':
				case '\n':
					m_pFile->m_pView--;
					return T_ERROR;
				default:
					m_strSymbol += ch;
					break;
				}
			}
		case '\'':
			m_strSymbol.clear();
			for ( ; ; )
			{
				if (m_pFile->m_pView == m_pFile->m_pViewEnd)
					return T_ERROR;
				switch(ch = *m_pFile->m_pView++)
				{
				case 0:
					m_pFile->m_pView--;
					return T_ERROR;
				case '\'':
					if (m_pFile->m_pView == m_pFile->m_pViewEnd)
						return T_STRING;
					if ((ch = *m_pFile->m_pView) != '\'') 
						return T_STRING;
					else
						m_strSymbol += '\'';
					break;
				case '\r':
				case '\n':
					m_pFile->m_pView--;
					return T_ERROR;
				default:
					m_strSymbol += ch;
					break;
				}
			}
		case ' ':
		case '\t':
		case '\r':
			goto st_start;
		case '+':
		case '-':
			m_strSymbol.clear();
			m_strSymbol += ch;
			goto st_sign;
		case '0':
			m_strSymbol.clear();
			m_strSymbol += ch;
			goto st_zero;
		case '.':
			m_strSymbol.clear();
			m_strSymbol += ch;
			goto st_dot;
		case '*':
		case '/':
		case '_':
			m_strSymbol.clear();
			m_strSymbol += ch;
			goto st_string;
		default:
			if (isdigit(ch))
			{
				m_strSymbol.clear();
				m_strSymbol += ch;
				goto st_digit;
			} else if (isalnum(ch)) {
				m_strSymbol.clear();
				m_strSymbol += ch;
				goto st_string;
			}
			return T_ERROR;
		}
		st_string:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_STRING;
		switch(ch = *m_pFile->m_pView++)
		{
		case '+':
		case '-':
		case '*':
		case '/':
		case '.':
		case '_':
			m_strSymbol += ch;
			goto st_string;
		default:
			if (isalnum(ch)) {
				m_strSymbol += ch;
				goto st_string;
			}
			m_pFile->m_pView--;
			return T_STRING;
		}
		st_sign:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_STRING;
		switch (ch = *m_pFile->m_pView++) {
		case '0':
			m_strSymbol += ch;
			goto st_zero;
		case '.':
			m_strSymbol += ch;
			goto st_dot;
		default:
			if (isdigit(ch)) {
				m_strSymbol += ch;
				goto st_digit;
			}
			if (isalnum(ch))
				return T_ERROR;
			m_pFile->m_pView--;
			return T_STRING;
		}
		st_zero:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_INTEGER;
		switch (ch = *m_pFile->m_pView++) {
		case 'x':
		case 'X':
			m_strSymbol += ch;
			goto st_hexa;
		case '8':
		case '9':
			return T_ERROR;
		case '.':
			m_strSymbol += ch;
			goto st_dot;
		default:
			if (isdigit(ch)) {
				m_strSymbol += ch;
				goto st_octal;
			}
			if (isalnum(ch))
				return T_ERROR;
			m_pFile->m_pView--;
			return T_INTEGER;
		}
		st_octal:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_INTEGER;
		switch (ch = *m_pFile->m_pView++) {
		case '8':
		case '9':
			return T_ERROR;
		default:
			if (isdigit(ch)) {
				m_strSymbol += ch;
				goto st_octal;
			}
			if (isalnum(ch))
				return T_ERROR;
			m_pFile->m_pView--;
			return T_INTEGER;
		}
		st_hexa:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_INTEGER;
		if (isxdigit(ch = *m_pFile->m_pView++)) {
			m_strSymbol += ch;
			goto st_hexa;
		}
		if (isalnum(ch))
			return T_ERROR;
		m_pFile->m_pView--;
		return T_INTEGER;
		st_digit:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_INTEGER;
		switch (ch = *m_pFile->m_pView++) {
		case '.':
			m_strSymbol += ch;
			goto st_dot;
		case 'd':
		case 'D':
		case 'e':
		case 'E':
			m_strSymbol += ch;
			goto st_exp;
		default:
			if (isdigit(ch)) {
				m_strSymbol += ch;
				goto st_digit;
			}
			if (isalnum(ch))
				return T_ERROR;
			m_pFile->m_pView--;
			return T_INTEGER;
		}
		st_dot:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_ERROR;
		if (isdigit(ch = *m_pFile->m_pView++)) {
			m_strSymbol += ch;
			goto st_dot_digit;
		}
		m_pFile->m_pView--;
		return T_ERROR;		
		st_dot_digit:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_FLOAT;
		switch (ch = *m_pFile->m_pView++) {
		case 'd':
		case 'D':
		case 'e':
		case 'E':
			m_strSymbol += ch;
			goto st_exp;
		default:
			if (isdigit(ch)) {
				m_strSymbol += ch;
				goto st_dot_digit;
			}
			if (isalnum(ch))
				return T_ERROR;
			m_pFile->m_pView--;
			return T_FLOAT;
		}
		st_exp:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_ERROR;
		switch (ch = *m_pFile->m_pView++) {
		case '+':
		case '-':
			m_strSymbol += ch;
			goto st_exp_sign;
		default:
			if (isdigit(ch)) {
				m_strSymbol += ch;
				goto st_exp_digit;
			}
			m_pFile->m_pView--;
			return T_ERROR;
		}
		st_exp_sign:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_ERROR;
		if (isdigit(ch = *m_pFile->m_pView++)) {
			m_strSymbol += ch;
			goto st_exp_digit;
		}
		m_pFile->m_pView--;
		return T_ERROR;
		st_exp_digit:
		if (m_pFile->m_pView == m_pFile->m_pViewEnd)
			return T_FLOAT;
		if (isdigit(ch = *m_pFile->m_pView++)) {
			m_strSymbol += ch;
			goto st_exp_digit;
		}
		if (isalnum(ch))
			return T_ERROR;
		m_pFile->m_pView--;
		return T_FLOAT;
	}
}

DWORD XParser::ParseList(PARSE_CALLBACK Callback, DWORD dwParam)
{
	#undef new
	TOKEN token;
	lisp::var top;
	lisp::var stack;

	for ( ; ; ) {
		token = GetToken();
		switch (token) {
			case T_END:
				if (m_nDepth != 0) {
					CConsole::Red("Unmatched open parenthesis\n");
					top = lisp::error;
					goto quit;
				}
				top = lisp::nreverse(top);
				goto quit;
			case T_CLOSE:
				top = lisp::nreverse(top);
				if (stack.consp()) {
					lisp::_cons *pObject = (lisp::_cons *) stack.m_pObject;
					stack = pObject->m_car;
					pObject->m_car = top;
					top = pObject;
				}
				else if (m_nDepth < 0) {
					CConsole::Red("Unmatched close parenthesis %d\n", GetLine());
					top = lisp::error;
					m_nDepth = 0;
					goto quit;
				}
				else
					goto quit;
				break;
			case T_OPEN:
				stack = new(_alloca(sizeof(lisp::_cons))) lisp::_cons(stack, top);
				top = lisp::nil;
				break;
			case T_STRING:
				{
					size_t len = m_strSymbol.size();
					char *szSymbol = (char *) _alloca(len+1); 
					memcpy(szSymbol, m_strSymbol.c_str(), len+1);
					lisp::var object(new(_alloca(sizeof(lisp::_string))) lisp::_string(szSymbol));
					top = new(_alloca(sizeof(lisp::_cons))) lisp::_cons(object, top);
				}
				break;
			case T_INTEGER:
				{
					lisp::var object(new(_alloca(sizeof(lisp::_integer))) lisp::_integer(GetInteger()));
					top = new(_alloca(sizeof(lisp::_cons))) lisp::_cons(object, top);
				}
				break;
			case T_FLOAT:
				{
					lisp::var object(new(_alloca(sizeof(lisp::_float))) lisp::_float(GetFloat()));
					top = new(_alloca(sizeof(lisp::_cons))) lisp::_cons(object, top);
				}
				break;
			default:
				top = lisp::error;
				goto quit;
		}
	}
	#define new NEW
	quit:
	return (*Callback)(dwParam, top);
}