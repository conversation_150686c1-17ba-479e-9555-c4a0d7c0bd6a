#pragma once
#include <strstream>
#include "Lisp.h"
#pragma warning (disable : 4996)

namespace MacroGenVoyager
{
	template <typename t> class CBaseDB
	{
		public:
		DWORD m_dwKey;

		static BOOL LoadConfig( lisp::var param)
		{
			t data;
			BOOL bOK = data.Load(param);
			if (bOK) 
			{
				t_map::iterator it = s_map.find(data.m_dwKey);
				if (it != s_map.end())
				{
					CConsole::Red("Error CBaseDB LoadConfig -> Key Duplication\n");
					s_map.erase(it);
				}
				s_map.insert(t_map::value_type(data.m_dwKey, data));
			}
			return bOK;
		}

		static t* Find(DWORD index)
		{
			t_map::iterator it = s_map.find(index);
			if (it != s_map.end()) return &((*it).second);
			return NULL;
		}

		virtual BOOL Set(int attribute, lisp::var param){ return TRUE;}
		virtual BOOL Load(lisp::var param)
		{
			for ( ; param.consp() ; )
			{
				lisp::var list = param.pop();
				LPCSTR attribute = list.pop();
				if (attribute == 0) return FALSE;
				if (!Set( FindAttribute(attribute), list)) return FALSE;
			}
			return TRUE;
		}

		typedef std::map<DWORD,t> t_map;
		static t_map s_map;
	};

	class CGenVoyagerDB : public CBaseDB<CGenVoyagerDB>
	{
		public:		
		int	ID;
		std::vector<int> SELL;
		BOOL Set(int attribute, lisp::var param);
	};

	enum ATTRIBUTE
	{
		A_NULL,
		A_ID,
		A_SELL
	};

	struct AttributeTable
	{
		char *pString;
		ATTRIBUTE nAttribute;
		int operator - (const char *str) const { return stricmp(pString, str); }
	};

	ATTRIBUTE FindAttribute(const char *pString);
	BOOL Load(const char *szPath);	
}

template <typename t> typename MacroGenVoyager::CBaseDB<t>::t_map MacroGenVoyager::CBaseDB<t>::s_map;

namespace MacroGenMonster
{
	template <typename t> class CBaseDB
	{
		public:
		DWORD m_dwKey;

		static BOOL LoadConfig( lisp::var param)
		{
			t data;
			BOOL bOK = data.Load(param);
			if (bOK) 
			{
				t_map::iterator it = s_map.find(data.m_dwKey);
				if (it != s_map.end())
				{
					CConsole::Red("Error GenMonster LoadConfig -> Key Duplication\n");
					s_map.erase(it);
				}
				s_map.insert(t_map::value_type(data.m_dwKey, data));
			}
			return bOK;
		}

		static t* Find(DWORD index)
		{
			t_map::iterator it = s_map.find(index);
			if (it != s_map.end()) return &((*it).second);
			return NULL;
		}

		virtual BOOL Set(int attribute, lisp::var param){ return TRUE;}
		virtual BOOL Load(lisp::var param)
		{
			for ( ; param.consp() ; )
			{
				lisp::var list = param.pop();
				LPCSTR attribute = list.pop();
				if (attribute == 0) return FALSE;
				if (!Set( FindAttribute(attribute), list)) return FALSE;
			}
			return TRUE;
		}

		typedef std::map<DWORD,t> t_map;
		static t_map s_map;
	};

	class GenMonsterDB : public CBaseDB<GenMonsterDB>
	{
		public:		
		int	Index;
		int Map;
		int Area;
		int Max;
		int Cycle;
		int X1;
		int Y1;
		int X2;
		int Y2;
		BOOL Set(int attribute, lisp::var param);
	};

	enum ATTRIBUTE
	{
		A_NULL,
		A_INDEX,
		A_MAP,
		A_AREA,
		A_MAX,
		A_CYCLE,
		A_RECT
	};

	struct AttributeTable
	{
		char *pString;
		ATTRIBUTE nAttribute;
		int operator - (const char *str) const { return stricmp(pString, str); }
	};

	ATTRIBUTE FindAttribute(const char *pString);
	BOOL Load(const char *szPath);	
}

template <typename t> typename MacroGenMonster::CBaseDB<t>::t_map MacroGenMonster::CBaseDB<t>::s_map;