#pragma once

class XMapView
{
	public:
	XMapView();
	~XMapView();
	BOOL Create(const char *szPathName, DWORD dwDesiredAccess = FILE_MAP_READ);
	void Close();
	char *m_pMapView;
	HANDLE m_hFile;
	HANDLE m_hMapping;
};

class XMapViewEx : private XMapView
{
	public:
	enum SeekPosition
	{
		begin	=	0x0,
		current	=	0x1,
		end		=	0x2
	};

	XMapViewEx();
	XMapViewEx(LPCTSTR lpszFilename);
	~XMapViewEx();
	BOOL Open(LPCTSTR lpszFilename);
	UINT Read(void *lpBuf, UINT uiCount);
	void Close();
	long Seek(long lOffset, UINT nFrom);
	BOOL IsValidHandle() { return (m_hFile != INVALID_HANDLE_VALUE);}
	DWORD SeekToEnd(void) { return	Seek( 0L, XMapViewEx::end);	}
	void SeekToBegin(void) { Seek(0L, XMapViewEx::begin); }
	DWORD GetLength(void) const { return (DWORD)(m_pViewEnd - m_pMapView); }
	DWORD GetPosition(void) const { return (DWORD)(m_pView - m_pMapView); }
	char *m_pView;
	char *m_pViewEnd;
};

class XFile
{
	public:
	enum SeekPosition
	{
		begin	=	0x0,
		current	=	0x1,
		end		=	0x2
	};

	XFile();
	XFile(void *pFile, DWORD size);
	~XFile();
	void Open(void *pVoid, DWORD size);
	UINT Read(void *lpBuf, UINT uiCount);
	void Close();
	long Seek(long lOffset, UINT nFrom);
	DWORD SeekToEnd(void) { return Seek( 0L, XFile::end); }
	void SeekToBegin(void) { Seek(0L, XFile::begin); }
	DWORD GetLength(void) const { return (DWORD)(m_pViewEnd - m_pViewBegin); }
	DWORD GetPosition(void) const { return (DWORD)(m_pView - m_pViewBegin); }
	char *m_pView;
	char *m_pViewBegin;
	char *m_pViewEnd;
};

class XFileEx : public XFile
{
	public:
	enum { MEMORY_SIZE = 64 * 1024};
	HANDLE m_hFile;
	HANDLE m_hMapping;
	DWORD m_nSize;
	void *m_pMemory;
	XFileEx();
	~XFileEx();
	int	PreOpen(HANDLE hFile, DWORD size);
	int	PreOpen(LPCSTR szPath, DWORD size);
	BOOL PostOpen(LPVOID pVoid);
	BOOL Open(HANDLE hFile);
	BOOL Open(LPCSTR szPath);
	void Close();
	DWORD Touch();
};
