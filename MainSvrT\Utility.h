#pragma once
#include <vector>

template <class TYPE>
char *GetNumeric(char *packet, TYPE *Num)
{
	*Num = *(TYPE*) packet;
	return packet + sizeof(TYPE);
}

template <class TYPE>
char *GetNumeric(char *packet, TYPE *Num, const int nArry)
{
	memcpy(Num, packet, sizeof(TYPE) * nArry);
	return packet + sizeof(TYPE) * nArry;
}

template <class TYPE>
char *ScanNumeric(char *packet, char *end, TYPE *Num)
{
	if (packet + sizeof(TYPE) > end) return packet;
	*Num = *(TYPE*) packet;
	return packet + sizeof(TYPE);
}

template <class TYPE>
char *ScanNumeric(char *packet, char *end, TYPE *Num, const int nArry)
{
	if (packet + sizeof(TYPE) * nArray > end) return packet;
	memcpy(Num, packet, sizeof(TYPE) * nArry);
	return packet + sizeof(TYPE) * nArry;
}

template <class TYPE>
char *PutNumeric(char *packet, const TYPE Num)
{
	*(TYPE*) packet = Num;
	return packet + sizeof(TYPE);
}

template <class TYPE>
char *PutNumeric(char *packet, const TYPE *Num, const int nArry)
{
	memcpy( packet, Num, sizeof(TYPE) * nArry);
	return packet + sizeof(TYPE) * nArry;
}

template <typename TYPE> class Secure
{
	public:	
	TYPE m_nValue;
	static TYPE m_nKey;
	Secure() {}
	Secure(TYPE nValue) { m_nValue = nValue ^ m_nKey; }
	operator TYPE () { return m_nValue ^ m_nKey; }
	void operator = (TYPE nValue) { m_nValue = nValue ^ m_nKey; }
};

template <typename TYPE> TYPE Secure<TYPE>::m_nKey;

template <typename TYPE> class deque
{
	typedef	std::vector<TYPE> QueueType;
	QueueType m_queue;
	typename QueueType::iterator m_front;
	typename QueueType::iterator m_back;
	long m_nSize;
 
	public:
	deque()
	{
		m_nSize = 0;
		m_front = m_back = m_queue.insert(m_queue.begin(), TYPE());
	}

	void push(const TYPE &element) 
	{
		*m_back = element;
		if (++m_back == m_queue.end()) m_back = m_queue.begin();
		if (m_front == m_back)
		{
			m_back = m_queue.insert( m_back, TYPE());
			m_front = m_back + 1;
		}
		m_nSize++;
	}
	
	void pop()
	{
		if (++m_front == m_queue.end()) m_front = m_queue.begin();
		m_nSize--;
	}
	
	TYPE &front()
	{
		return *m_front;
	}

	TYPE &back()
	{
		if (m_back == m_eqeue.begin())
			return m_queue.end()[-1];
		else
			return m_back[-1];
	}

	int	size() const
	{
		return m_nSize;
	}

	bool empty() const
	{
		return m_nSize == 0;
	}

};

template <typename TYPE> class priority_queue 
{
	public:
	typedef std::vector<TYPE> container_type;
	typedef typename container_type::value_type value_type;
	typedef typename container_type::size_type size_type;
	typedef	typename container_type::iterator	iterator;
	typedef	typename container_type::difference_type	difference_type;

	public:
	bool empty() const
		{
		return (c.empty());
		}

	size_type size() const
		{
		return (c.size());
		}

	const value_type& top() const
		{
		return (c.front());
		}

	value_type& top()
		{
		return (c.front());
		}

	void push(const value_type& _Pred)
		{
		c.push_back(_Pred);
		push_heap(c.begin(), c.end());
		}

	void pop()
		{
		pop_heap(c.begin(), c.end());
		c.pop_back();
		}

	iterator begin()
	{
		return c.begin();
	}

	iterator end()
	{
		return	c.end();
	}

	void erase(iterator _Where)
	{
		TYPE _Val = *(c.end() - 1);
		iterator	_First = c.begin();
		difference_type _Hole = _Where - _First;
		difference_type _Bottom = c.end() - _First - 1;
		difference_type _Top = 0;
		difference_type _Idx = 2 * _Hole + 2;

		for (; _Idx < _Bottom; _Idx = 2 * _Idx + 2)
			{
			if (*(_First + _Idx) < *(_First + (_Idx - 1)))
				--_Idx;
			*(_First + _Hole) = *(_First + _Idx), _Hole = _Idx;
			}

		if (_Idx == _Bottom)
			{
			*(_First + _Hole) = *(_First + (_Bottom - 1));
			_Hole = _Bottom - 1;
			}
		
		for (_Idx = (_Hole - 1) / 2;
			_Top < _Hole && *(_First + _Idx) < _Val;
			_Idx = (_Hole - 1) / 2)
			{
			*(_First + _Hole) = *(_First + _Idx);
			_Hole = _Idx;
			}

		*(_First + _Hole) = _Val;
		c.pop_back();
	}

	void raise(iterator _Where)
	{
		TYPE _Val = *_Where;
		iterator _First = c.begin();
		difference_type _Hole = _Where - _First;
		difference_type _Idx;

		for (_Idx = (_Hole - 1) / 2;
			0 < _Hole && *(_First + _Idx) < _Val;
			_Idx = (_Hole - 1) / 2)
			{
			*(_First + _Hole) = *(_First + _Idx);
			_Hole = _Idx;
			}

		*(_First + _Hole) = _Val;
	}

	protected:
	container_type c;

};

template<class _FwdIt, class _Ty> inline _FwdIt equal(_FwdIt _First, _FwdIt _Last, const _Ty& _Val)
{	
	std::iterator_traits<_FwdIt>::difference_type _Count = _Last - _First;

	for (; 0 < _Count; )
	{
		std::iterator_traits<_FwdIt>::difference_type _Count2 = _Count / 2;
		_FwdIt _Mid = _First + _Count2;
		int nCmp = *_Mid - _Val;
		if (nCmp < 0)
			_First = ++_Mid, _Count -= _Count2 + 1;
		else if (nCmp > 0)
			_Count = _Count2;
		else 
			return _Mid;
	}
	return (_Last);
}

template<class _FwdIt, class _Ty> inline _FwdIt lower_bound(_FwdIt _First, _FwdIt _Last, const _Ty& _Val)
{
	std::iterator_traits<_FwdIt>::difference_type _Count = _Last - _First;

	for (; 0 < _Count; )
	{
		std::iterator_traits<_FwdIt>::difference_type _Count2 = _Count / 2;
		_FwdIt _Mid = _First + _Count2;

		if ((*_Mid - _Val) < 0)
			_First = ++_Mid, _Count -= _Count2 + 1;
		else
			_Count = _Count2;
	}
	return (_First);
}

template<class _FwdIt, class _Ty> inline _FwdIt upper_bound(_FwdIt _First, _FwdIt _Last, const _Ty& _Val)
{
	std::iterator_traits<_FwdIt>::difference_type _Count = _Last - _First;

	for (; 0 < _Count; )
	{
		std::iterator_traits<_FwdIt>::difference_type _Count2 = _Count / 2;
		_FwdIt _Mid = _First + _Count2;

		if ((*_Mid - _Val) <= 0)
			_First = ++_Mid, _Count -= _Count2 + 1;
		else
			_Count = _Count2;
	}
	return (_First);
}

template<class _FwdIt, class _Ty> inline std::pair<_FwdIt, _FwdIt> equal_range(_FwdIt _First, _FwdIt _Last, const _Ty& _Val)
{	
	std::iterator_traits<_FwdIt>::difference_type _Count = _Last - _First;

	for (; 0 < _Count; )
	{
		std::iterator_traits<_FwdIt>::difference_type _Count2 = _Count / 2;
		_FwdIt _Mid = _First + _Count2;

		int nCmp = *_Mid - _Val;
		if (nCmp < 0)
		{
			_First = ++_Mid;
			_Count -= _Count2 + 1;
		} else if (nCmp > 0) {
			_Count = _Count2;
		} else {	
			_FwdIt _First2 = ::lower_bound(_First, _Mid, _Val);
			_First += _Count;
			_FwdIt _Last2 = ::upper_bound(++_Mid, _First, _Val);
			return (std::pair<_FwdIt, _FwdIt>(_First2, _Last2));
		}
	}

	return (std::pair<_FwdIt, _FwdIt>(_First, _First));
}

template <typename T> void	minimize(T &left, const T& right) { if (right < left) left = right; }
template <typename T> void	maximize(T &left, const T& right) { if (right > left) left = right; }

template <typename T> void	minmaximize(T &left, const T& min, const T& max) 
{ 
	if (left > max) 
		left = max;
	else if (left < min)
		left = min;
}

class XLock
{
	protected:
	CRITICAL_SECTION m_critical_section;
	public:
	XLock() { InitializeCriticalSection( &m_critical_section); }
	~XLock() { DeleteCriticalSection( &m_critical_section); }
	void Enter() { EnterCriticalSection( &m_critical_section); }
	void Leave() { LeaveCriticalSection( &m_critical_section); }
};

#ifdef	_MT
extern	XLock	MemoryLock;
#endif

template <int size> class CMemoryPool
{
	public:
	union U
	{
		char m_buffer[size];
		U *m_pU;
	};

	class CPool
	{
		public:
		U* m_pU;
		~CPool() { CMemoryPool::FreeAll(); }
	};

	static CPool s_pool;
		
	static void *Alloc() 
	{
		#ifdef _MT
		MemoryLock.Enter();
		#endif
		U* pU;
		if ((pU = s_pool.m_pU) != NULL)
		{
			s_pool.m_pU = pU->m_pU;
			#ifdef _DEBUG
			pU = (U*) realloc(pU, sizeof(U));
			#endif
		} else {
			pU = (U*) malloc(sizeof(U));
		}
		#ifdef _MT
		MemoryLock.Leave();
		#endif
		return pU;
	}

	static void Free(void *pT)
	{
		#ifdef _MT
		MemoryLock.Enter();
		#endif
		memset(pT, 0xfb, sizeof(U));
		U* pU = (U*) pT;
		pU->m_pU = s_pool.m_pU;
		s_pool.m_pU = pU;
		#ifdef _MT
		MemoryLock.Leave();
		#endif
	}

	static void FreeAll()
	{
		#ifdef _MT
		MemoryLock.Enter();
		#endif
		U *pU;
		while ((pU = s_pool.m_pU) != NULL)
		{
			s_pool.m_pU = pU->m_pU;
			free(pU);
		}
		#ifdef _MT
		MemoryLock.Leave();
		#endif
	}
};

template <int size>  typename CMemoryPool<size>::CPool  CMemoryPool<size>::s_pool;

template <typename TYPE> TYPE *XConstruct()
{
	return new (CMemoryPool<sizeof(TYPE)>::Alloc()) TYPE;
}

template <typename TYPE> void XDestruct(TYPE *ptr)
{
	ptr->~TYPE();
	CMemoryPool<sizeof(TYPE)>::Free(ptr);
}

template <int size> class CVirtualMemoryPool
{
	public:
	union U
	{
		char m_buffer[size];
		U *m_pU;
	};

	class CPool
	{
		public:
		U* m_pU;
		~CPool() { CVirtualMemoryPool::FreeAll(); }
	};

	static CPool s_pool;
		
	static void *Alloc() 
	{
		#ifdef _MT
		MemoryLock.Enter();
		#endif
		U* pU;
		if ((pU = s_pool.m_pU) != NULL)
		{
			s_pool.m_pU = pU->m_pU;
		} else {
			pU = (U*) VirtualAlloc(0, sizeof(U), MEM_COMMIT, PAGE_READWRITE);
		}
		#ifdef _MT
		MemoryLock.Leave();
		#endif
		return pU;
	}

	static void Free(void *pT)
	{
		#ifdef _MT
		MemoryLock.Enter();
		#endif
		memset(pT, 0xfb, sizeof(U));
		U* pU = (U*) pT;
		pU->m_pU = s_pool.m_pU;
		s_pool.m_pU = pU;
		#ifdef _MT
		MemoryLock.Leave();
		#endif
	}

	static void FreeAll()
	{
		#ifdef _MT
		MemoryLock.Enter();
		#endif
		U *pU;
		while ((pU = s_pool.m_pU) != NULL)
		{
			s_pool.m_pU = pU->m_pU;
			VirtualFree(pU, sizeof(U), MEM_RELEASE);
		}
		#ifdef _MT
		MemoryLock.Leave();
		#endif
	}
};

template <int size>  typename CVirtualMemoryPool<size>::CPool  CVirtualMemoryPool<size>::s_pool;

class CReserveVoid
{
	public:
	void *m_pMemory;
	void *m_pAlloc;
	size_t m_nSize;

	CReserveVoid()
	{
		m_nSize = 0;
		m_pMemory = 0;
		m_pAlloc = 0;
	}

	CReserveVoid(size_t nSize, void *pMemory)
	{
		m_nSize = nSize;
		m_pMemory = pMemory;
		m_pAlloc = 0;
	}

	~CReserveVoid()
	{
		free(m_pAlloc);
	}

	void Reserve(size_t nSize, void *pMemory)
	{
		free(m_pAlloc);
		m_nSize = nSize;
		m_pMemory = pMemory;
		m_pAlloc = 0;
	}

	void *Alloc(size_t nSize)
	{
		if (nSize <= m_nSize) return m_pMemory;
		m_pMemory = m_pAlloc = realloc(m_pAlloc, nSize);
		m_nSize = nSize;
		return m_pMemory;
	}
};

template <typename TYPE> class CReserveMemory : public CReserveVoid
{
	public:
	CReserveMemory() {}
	CReserveMemory(size_t nSize, TYPE *pMemory) : CReserveVoid(sizeof(TYPE)*nSize, pMemory) {}
	void Reserve(size_t nSize, TYPE *pMemory) { CRserveVoid::Reserve(sizeof(TYPE)*nSize, pMemory); }
	TYPE *Alloc(size_t nSize) { return (TYPE*) CReserveVoid::Alloc(sizeof(TYPE)*nSize); }
	operator TYPE *() { return (TYPE*) m_pMemory; }
	TYPE * operator -> () { return (TYPE*) m_pMemory; }
};