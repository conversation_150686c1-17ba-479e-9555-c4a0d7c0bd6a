namespace MainSvrT
{
	void __fastcall PullingSword(IChar IPlayer, int pPacket, int pPos)
	{
		int pSkill = IPlayer.GetSkillPointer(48);
		if (ControlBlade.find(IPlayer.GetPID())->second < 1) return;
		if (!IPlayer.IsBuff(433)) return;

		if (IPlayer.IsValid() && pSkill)
		{
			ISkill xSkill((void*)pSkill);
			int nSkillGrade = xSkill.GetGrade();
			int nTargetID = 0; char bType = 0; void *pTarget = 0;
			CPacket::Read((char*)pPacket, (char*)pPos, "bd", &bType, &nTargetID);
			if (bType == 0 && nTargetID) return;
			if (bType == 1 && nTargetID) pTarget = CMonster::FindMonster(nTargetID);

			if (pTarget)
			{
				if (nSkillGrade && IPlayer.IsValid())
				{
					IChar Target(pTarget);

					if (IPlayer.GetCurMp() < 100)
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (pTarget == IPlayer.GetOffset())
					{
						if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
						return;
					}

					if (IPlayer.IsValid() && Target.IsValid())
					{
						if (!IPlayer.IsInRange(Target,300))
						{
							if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
							return;
						}

						if (IPlayer.IsValid() && Target.IsValid() && Target.IsMobAggressive())
						{
							IPlayer.DecreaseMana(100);
							ControlBlade.find(IPlayer.GetPID())->second -= 1;
							CChar::WriteInSight(Target.GetOffset(),56,"db",Target.GetID(),7);
							if (Target.IsValid()) *(DWORD*)((int)Target.GetOffset() + 332) = IPlayer.GetX() - 5;
							if (Target.IsValid()) *(DWORD*)((int)Target.GetOffset() + 336) = IPlayer.GetY() - 5;
							CChar::WriteInSight(Target.GetOffset(),51,"wdddwddIIsbdsIIb",Target.GetMobIndex(),Target.GetID(),IPlayer.GetX() - 5,IPlayer.GetY() - 5,*(DWORD*)((int)Target.GetOffset() + 348),Target.GetCurHp(),CChar::GetMaxHp((int)Target.GetOffset()),*(DWORD*)((int)Target.GetOffset() + 280),(unsigned __int64)*(DWORD*)((int)Target.GetOffset() + 288),*(DWORD*)((int)Target.GetOffset() + 292),(*(int (__thiscall **)(int))(*(DWORD*)(int)Target.GetOffset() + 224))((int)Target.GetOffset()),*(DWORD*)(*(DWORD*)((int)Target.GetOffset() + 440) + 64) | 0x80,*(DWORD*)((int)Target.GetOffset() + 524),(*(int (__thiscall **)(int))(*(DWORD*)(int)Target.GetOffset() + 240))((int)Target.GetOffset()),(__int64)0, (__int64)0, 0);
							Target.AddFxToTarget("TI_SK_15",1,0,0);		
						}
					}
				}

				if (pTarget) CSkill::ObjectRelease(pTarget, (int)pTarget + 352);
			}
		}
	}
}