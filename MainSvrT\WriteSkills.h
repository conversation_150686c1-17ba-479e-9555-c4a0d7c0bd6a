namespace MainSvrT
{
	void __cdecl PacketsWriteSkills(void *Player, char Type, char* format, char *rskills, size_t size)
	{
		char *skills = rskills; Interface<ITools> Tools;
		unsigned char Skills = skills[0];
		skills++; char *Array = new char[Skills*6];

		for(unsigned char i = 0; i < Skills; i++)
		{
			char id = 0, grade = 0;
			skills = Tools->ParseData(skills,"bb",&id,&grade);
			if (id == 87) id = 85;
			else if (id == 88) id = 86;
			else if (id == 89) id = 87;
			else if (id == 79) id = 89;
			else if (id == 95) id = 88;
			Tools->Compile(Array +i*6,"bbd",id,grade,0);
		}

		CPlayer::Write(Player,16,"bm",Skills,Array,Skills*6);
		delete[] Array;
	}

	void MyWriteSkills()
	{
		Interface<IMemory> Memory;
		unsigned char MaxSkillID = 96;
		Memory->Copy((void*)0x0047F97C, &MaxSkillID, 1);
		Memory->Copy((void*)0x0047F7AC, &MaxSkillID, 1);
		Memory->Copy((void*)0x0047F6BC, &MaxSkillID, 1);
		Memory->Hook(0x004570A8,MainSvrT::PacketsWriteSkills);
	}
}