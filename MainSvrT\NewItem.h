namespace MainSvrT
{
	int __cdecl NewItem(int Value)
	{
		if (*(DWORD*)(Value + 68) == -1)
		{
			void *Check = 0, *GetPointer = 0;
			Check = (void*)Undefined::GetMonsterValue(0x5Cu);
			if ((int)Check) GetPointer = CItemOrnament::CItemOrnament(Check, Value);

			if ((int)GetPointer)
			{
				IItem xItem(GetPointer);

				if (xItem.CheckIndex() >= 3384 && xItem.CheckIndex() <= 3386)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -94;
					return (int)GetPointer;
				}

				if (SuitWearFix.count(xItem.CheckIndex()))
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 100;
					return (int)GetPointer;
				}

				if (MonsterPet.count(xItem.CheckIndex()))
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 101;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 7883 || xItem.CheckIndex() == 7889 || xItem.CheckIndex() == 7895 || xItem.CheckIndex() == 7901)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 102;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 7884 || xItem.CheckIndex() == 7890 || xItem.CheckIndex() == 7896 || xItem.CheckIndex() == 7902)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 103;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 7885 || xItem.CheckIndex() == 7891 || xItem.CheckIndex() == 7897 || xItem.CheckIndex() == 7903)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 104;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 7886 || xItem.CheckIndex() == 7892 || xItem.CheckIndex() == 7898 || xItem.CheckIndex() == 7904)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 105;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 7887 || xItem.CheckIndex() == 7893 || xItem.CheckIndex() == 7899 || xItem.CheckIndex() == 7905)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 106;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 7888 || xItem.CheckIndex() == 7894 || xItem.CheckIndex() == 7900 || xItem.CheckIndex() == 7906)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 107;
					return (int)GetPointer;
				}

				if (ConfigLimitedPet.count(xItem.CheckIndex()))
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 108;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 9801 && xItem.CheckIndex() <= 9820)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 109;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 9821 && xItem.CheckIndex() <= 9840)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 110;
					return (int)GetPointer;
				}

				if (DecoWearFix.count(xItem.CheckIndex()))
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = 111;
					return (int)GetPointer;
				}

				if (PetTime.count(xItem.CheckIndex()))
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -100;
					*(DWORD*)((int)GetPointer + 48) = 128;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2986 || xItem.CheckIndex() == 2994 || xItem.CheckIndex() == 3002)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -101;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2987 || xItem.CheckIndex() == 2995 || xItem.CheckIndex() == 3003)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -102;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2988 || xItem.CheckIndex() == 2996 || xItem.CheckIndex() == 3004)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -103;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2989 || xItem.CheckIndex() == 2997 || xItem.CheckIndex() == 3005)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -104;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2990 || xItem.CheckIndex() == 2998 || xItem.CheckIndex() == 3006)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -105;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2991 || xItem.CheckIndex() == 2999 || xItem.CheckIndex() == 3007)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -106;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2992 || xItem.CheckIndex() == 3000 || xItem.CheckIndex() == 3008)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -107;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() == 2993 || xItem.CheckIndex() == 3001 || xItem.CheckIndex() == 3009)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -108;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 3199 && xItem.CheckIndex() <= 3204)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -109;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 3381 && xItem.CheckIndex() <= 3383)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -110;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 2946 && xItem.CheckIndex() <= 2948)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -111;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 3018 && xItem.CheckIndex() <= 3020)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -112;
					*(DWORD*)((int)GetPointer + 48) = 128;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 2448 && xItem.CheckIndex() <= 2450)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -113;
					return (int)GetPointer;
				}

				if (xItem.CheckIndex() >= 2461 && xItem.CheckIndex() <= 2463)
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -113;
					return (int)GetPointer;
				}

				if (ConfigWeaponSkins.count(xItem.CheckIndex()))
				{
					*(DWORD*)(*(DWORD*)((int)GetPointer + 40) + 72) = -114;
					return (int)GetPointer;
				}
			}
		}

		return CItem::NewItem(Value);
	}

	int __cdecl MyCreateItem(int Index, int Prefix, int Amount, int Argument)
	{
		int result = 0, Item = 0, FindItem = 0;

		if (Amount > 0)
		{
			FindItem = CItem::FindInitItem(Index);

			if (FindItem)
			{
				Item = MainSvrT::NewItem(FindItem);

				if (Item)
				{
					if (Prefix >= 256)
					{
						*(DWORD*)(Item + 48) = 128;
						Prefix -= 256;
					}

					(*(void (__thiscall **)(void *, int, int, int))(*(DWORD*)Item + 76))((void*)Item, Prefix, Amount, Argument);
				}
			}

			result = Item;
		} else {
			result = 0;
		}

		return result;
	}

	int __fastcall CItemInit(int Item, void *edx, int Prefix, int Amount, int Argument)
	{
		if (Prefix >= 256)
		{
			*(DWORD*)(Item + 48) = 128;
			Prefix -= 256;
		}

		return CItem::Init(Item,Prefix,Amount,Argument);
	}
}